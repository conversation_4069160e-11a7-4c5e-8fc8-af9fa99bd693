package com.techcubics.data.auth.remote


import com.techcubics.data.auth.utils.Constants
import com.techcubics.domain.auth.models.LoginResponse
import com.techcubics.domain.auth.requests.ForgetPasswordByEmailRequest
import com.techcubics.domain.auth.requests.LoginRequest
import com.techcubics.domain.auth.requests.UpdatePasswordRequest
import com.techcubics.domain.auth.responses.ForgetPasswordResponseData
import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.auth.models.User
import retrofit2.Response
import retrofit2.http.*

interface EndPoints {

    @POST(Constants.login)
    suspend fun login(@Body loginRequest: LoginRequest, @Path("userType")userType: String): Response<BaseResponse<LoginResponse>>

    @POST(Constants.forget_password)
    suspend fun forgetPasswordByEmail(@Path("userType")userType: String,@Body forgetPasswordRequest: ForgetPasswordByEmailRequest): Response<BaseResponse<ForgetPasswordResponseData>>

    @GET(Constants.logout)
    suspend fun logout(@Path("userType")userType: String): Response<BaseResponse<String>>

    @POST(Constants.deleteAccount)
    suspend fun deleteAccount(@Path("userType")userType: String,@Query("note") note : String): Response<BaseResponse<String>>

    @POST(Constants.update_password)
    suspend fun updatePasswordReset(@Path("userType")userType: String,@Body updatePasswordRequest: UpdatePasswordRequest): Response<BaseResponse<Nothing>>
    @GET(Constants.profile)
    suspend fun checkAuth(@Path("userType")userType: String): Response<BaseResponse<Any>>?

}
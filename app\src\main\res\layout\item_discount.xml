<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:clickable="true"
    android:layout_marginVertical="4dp"
    android:focusable="true"
    app:cardCornerRadius="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/product_image"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:adjustViewBounds="true"
            android:src="@drawable/portrait_placeholder"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/guideline"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintGuide_percent="0.35"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/p_code"
            style="@style/ExtraSmallBoldAppColorText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="5dp"
            android:visibility="gone"
            android:layout_marginEnd="3dp"
            android:textAlignment="center"
            app:layout_constraintTop_toTopOf="@id/p_name"
            app:layout_constraintBottom_toBottomOf="@id/p_name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/p_name"
            tools:text="Discount#507" />

        <TextView
            android:id="@+id/p_name"
            style="@style/LargeBoldDarkText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="10dp"
            android:textAlignment="viewStart"
            app:layout_constraintEnd_toStartOf="@id/p_code"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/guideline"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="زيتون - 125 مل" />

        <LinearLayout
            android:id="@+id/price_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginVertical="10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="@id/p_code"
            app:layout_constraintStart_toStartOf="@id/p_name"
            app:layout_constraintTop_toBottomOf="@id/p_name">

            <TextView
                android:id="@+id/discount_percentage"
                style="@style/SmallBoldAppColorText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:drawablePadding="5dp"
                android:gravity="center_vertical"
                app:drawableStartCompat="@drawable/ic_discount"
                tools:text="10%" />

            <TextView
                android:id="@+id/discount_price_after"
                style="@style/Medium2BoldGreenText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:textColor="@color/color_59"
                tools:text="40 EGP" />

            <TextView
                android:id="@+id/discount_price_before"
                style="@style/Medium2RegularGray"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:background="@drawable/line_diagonal2"
                tools:text="44 EGP" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/date_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginVertical="10dp"
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="@id/p_code"
            app:layout_constraintStart_toStartOf="@id/p_name"
            app:layout_constraintTop_toBottomOf="@id/price_container">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    style="@style/SmallBoldAppColorText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:gravity="center_vertical"
                    android:text="@string/start_date_title" />

                <TextView
                    android:id="@+id/start_date"
                    style="@style/SmallRegularDarkText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="10dp"
                    tools:text="1/1/2121" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:orientation="horizontal">

                <TextView
                    style="@style/SmallBoldAppColorText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:gravity="center_vertical"
                    android:text="@string/end_date_title" />

                <TextView
                    android:id="@+id/end_date"
                    style="@style/SmallRegularDarkText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    tools:text="1/1/2121" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/titles_cont"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:gravity="start"
            android:orientation="vertical"
            android:weightSum="3"
            app:layout_constraintBottom_toBottomOf="@id/items_cont"
            app:layout_constraintStart_toStartOf="@id/p_name"
            app:layout_constraintTop_toTopOf="@id/items_cont">

            <TextView
                android:id="@+id/owner_title"
                style="@style/SmallMediumDarkText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="3dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/owner2" />

            <TextView
                style="@style/SmallMediumDarkText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="3dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/shop" />

            <TextView
                style="@style/SmallMediumDarkText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/activate" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/items_cont"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:orientation="vertical"
            android:weightSum="3"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/titles_cont"
            app:layout_constraintTop_toBottomOf="@id/date_container">

            <LinearLayout
                android:id="@+id/owner_cont"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginBottom="3dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="5dp"
                    app:cardCornerRadius="50dp">

                    <ImageView
                        android:id="@+id/owner_image"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:src="@drawable/portrait_contactus" />
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:id="@+id/owner"
                    style="@style/SmallMediumDarkText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="مؤسسة البركة" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/shop_cont"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginBottom="3dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">


                <com.google.android.material.card.MaterialCardView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="5dp"
                    app:cardCornerRadius="50dp">

                    <ImageView
                        android:id="@+id/shop_image"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:src="@drawable/portrait_contactus" />
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:id="@+id/shop_name"
                    style="@style/SmallMediumDarkText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="البركة" />
            </LinearLayout>

            <com.google.android.material.switchmaterial.SwitchMaterial
                android:id="@+id/active_status"
                android:layout_width="wrap_content"
                android:buttonTint="@color/color_59"
                app:thumbTint="@drawable/selector_switch_thumb"
                app:trackTint="@drawable/selector_switch_track"
                android:layout_height="0dp"
                android:layout_weight="1" />
        </LinearLayout>

        <ImageButton
            android:id="@+id/edit"
            style="@style/ExtraSmallBoldWhiteText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_min="50dp"
            android:minHeight="40dp"
            android:layout_marginEnd="5dp"
            android:layout_marginBottom="5dp"
            android:contentDescription="@string/img_cnt_desc"
            android:background="@drawable/btn_yellow_ripple"
            android:src="@drawable/ic_edit"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintStart_toEndOf="@id/items_cont"
            app:tint="@color/black" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
package com.techcubics.albarkahyperdashboard.features.chats.intents

import com.techcubics.albarkahyperdashboard.features.storage.intents.ProductIntent
import com.techcubics.domain.chats.request.SendMessageRequest

sealed class ChatIntent{
    data class GetOrderChatRoomsIntent(val page:Int,val ownerId:Int,val shopId:Int):ChatIntent()
    data class GetOrderChatsIntent(val id: Int,val ownerId:Int,val shopId:Int, val showProgress: Boolean):ChatIntent()
    data class SendOrderChatMessageIntent(val request:SendMessageRequest,val ownerId:Int,val shopId:Int):ChatIntent()
    data class GetSupportChatRoomsIntent(val page:Int):ChatIntent()
    data class GetSupportChatsIntent(val id: Int, val showProgress: Boolean):ChatIntent()
    data class SendSupportChatMessageIntent(val request:SendMessageRequest):ChatIntent()
    object GetOwners : ChatIntent()
    data class GetShopsByOwnerId(val ownerId: Int) : ChatIntent()

}

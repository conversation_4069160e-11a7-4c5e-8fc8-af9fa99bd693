package com.techcubics.data.common

import android.util.Log
import com.google.gson.GsonBuilder
import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.data.auth.utils.Constants.userType
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.converter.scalars.ScalarsConverterFactory
import java.util.concurrent.TimeUnit

class RetrofitBuilder(private val sharedPreferencesManager: SharedPreferencesManager) {

    // var mContext: Context? = null
    private val BASE_URL = Constants.BASE_API_URL
    var retrofit: Retrofit? = null

    fun start(): Retrofit? {
        if (retrofit == null) {
            val logging = HttpLoggingInterceptor()
            // set your desired log level
            logging.level = HttpLoggingInterceptor.Level.BODY


            val okHttpClient = OkHttpClient().newBuilder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .addInterceptor(logging)
                .addInterceptor { chain ->
                    val original = chain.request()
                    val request = original.newBuilder()
                        .addHeader("Authorization", "Bearer "+sharedPreferencesManager.getToken())
                        .addHeader("country-id",sharedPreferencesManager.getCountryID())
                        .addHeader("Accept", "application/json")
                       .addHeader("Api-Version", "v1")
                        .addHeader("Accept-Language", sharedPreferencesManager.getLanguage())
                        .method(original.method, original.body)
                        .build()
                    chain.proceed(request)
                }
                .build()

            val gson = GsonBuilder()
                .setLenient()
                .create()

            Log.i("login", "ret$userType")
              retrofit = Retrofit.Builder().apply {
                baseUrl(BASE_URL)
                addConverterFactory(GsonConverterFactory.create(gson))
                addConverterFactory(ScalarsConverterFactory.create())
            }.client(okHttpClient).build()


        }
        return retrofit
    }

}
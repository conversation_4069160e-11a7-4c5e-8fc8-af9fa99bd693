package com.techcubics.albarkahyperdashboard.features.chats.states

import com.techcubics.albarkahyperdashboard.features.storage.states.ProductsStates
import com.techcubics.domain.chats.models.OrderChat
import com.techcubics.domain.chats.models.OrderChatRoom
import com.techcubics.domain.chats.models.SupportChat
import com.techcubics.domain.chats.models.SupportChatResponse
import com.techcubics.domain.chats.models.SupportChatRoom
import com.techcubics.domain.storage.models.Owner
import com.techcubics.domain.storage.models.Shop

sealed class ChatState{
    object Idle : ChatState()
    object PageLoading : ChatState()
    object ProgressLoading : ChatState()
    object HideRv : ChatState()
    data class Error(val error: String?) : ChatState()
    data class ServerError(val error: String?) : ChatState()
    data class SendOrderChatMessageResponse(val chatMessage:OrderChat) : ChatState()
    data class ViewOrderChats(val chats:ArrayList<OrderChat>) : ChatState()
    data class ViewOrdersChatRooms(val chatRooms:ArrayList<OrderChatRoom>) : ChatState()
    data class SendSupportChatMessageResponse(val chatMessage:SupportChat) : ChatState()
    data class ViewSupportChats(val chatResponse:SupportChatResponse) : ChatState()
    data class ViewSupportChatRooms(val chatRooms:ArrayList<SupportChatRoom>) : ChatState()
    data class ViewOwners(val owners: ArrayList<Owner>?) : ChatState()
    data class ViewShops(val shops: ArrayList<Shop>?) : ChatState()

}

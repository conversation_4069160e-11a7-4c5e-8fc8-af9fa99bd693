package com.techcubics.domain.auth.repositories

import com.techcubics.domain.auth.models.LoginResponse
import com.techcubics.domain.auth.requests.*
import com.techcubics.domain.auth.responses.ForgetPasswordResponseData
import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.auth.requests.LoginRequest
import com.techcubics.domain.auth.models.User
import retrofit2.http.Path

interface AuthRepo {
    suspend fun login(request: LoginRequest, userType: String): BaseResponse<LoginResponse>?
    suspend fun forgetPasswordEmailCall(request: ForgetPasswordByEmailRequest): BaseResponse<ForgetPasswordResponseData>?
    suspend fun logout(): BaseResponse<String>?
    suspend fun deleteAccount(note : String): BaseResponse<String>?
    suspend fun updatePasswordResetCall(request: UpdatePasswordRequest) : BaseResponse<Nothing>?
    suspend fun checkAuth(): BaseResponse<Any>?
}
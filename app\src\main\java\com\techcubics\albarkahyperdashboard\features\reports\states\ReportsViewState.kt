package com.techcubics.albarkahyperdashboard.features.reports.states

import com.techcubics.domain.reports.response.ReportResponse
import com.techcubics.domain.storage.models.Owner
import com.techcubics.domain.storage.models.Shop

sealed class ReportsViewState {

    object Idle : ReportsViewState()
    object Loading : ReportsViewState()
    object BtnLoading : ReportsViewState()
    object BtnSuccess : ReportsViewState()
    object Pagination: ReportsViewState()
    data class ServerError(val error:String): ReportsViewState()

    data class Error(val message: String) : ReportsViewState()
    data class GetReports(val reports : ReportResponse) : ReportsViewState()
    data class GetShopsByOwnerId(val shops : MutableList<Shop>) : ReportsViewState()
    data class GetOwners(val owners : MutableList<Owner>) : ReportsViewState()
}
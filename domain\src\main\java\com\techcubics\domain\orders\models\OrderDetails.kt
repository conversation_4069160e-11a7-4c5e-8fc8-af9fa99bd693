package com.techcubics.domain.orders.models

import com.google.gson.annotations.SerializedName
import com.techcubics.domain.storage.models.Image

data class OrderDetails (

  @SerializedName("order_detail_id" ) var orderDetailId : Int?                     = null,
  @SerializedName("model_type"      ) var modelType     : String?                  = null,
  @SerializedName("model_id"        ) var modelId       : Int?                     = null,
  @SerializedName("qty"             ) var qty           : Int?                     = null,
  @SerializedName("price"           ) var price         : Float?                     = null,
  @SerializedName("product_id"      ) var productId     : Int?                     = null,
  @SerializedName("product_name"    ) var productName   : String?                  = null,
  @SerializedName("product_slug"    ) var productSlug   : String?                  = null,
  @SerializedName("product_icon"    ) var productIcon   : String?                  = null,
  @SerializedName("product_images"  ) var productImages : ArrayList<Image> = arrayListOf()

)
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="17dp"
    android:height="18dp"
    android:viewportWidth="17"
    android:viewportHeight="18">
  <path
      android:strokeWidth="1"
      android:pathData="M4.621,0.571C4.881,0.571 5.131,0.692 5.315,0.906C5.499,1.12 5.602,1.411 5.602,1.714V4C5.602,4.303 5.499,4.594 5.315,4.808C5.131,5.022 4.881,5.143 4.621,5.143C4.361,5.143 4.111,5.022 3.927,4.808C3.743,4.594 3.64,4.303 3.64,4V1.714C3.64,1.411 3.743,1.12 3.927,0.906C4.111,0.692 4.361,0.571 4.621,0.571V0.571Z"
      android:strokeAlpha="0.6"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#3C3C43"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M16.396,2.857H5.602"
      android:strokeAlpha="0.6"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#3C3C43"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M3.64,2.857H0.696"
      android:strokeAlpha="0.6"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#3C3C43"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M4.621,12C4.881,12 5.131,12.12 5.315,12.335C5.499,12.549 5.602,12.84 5.602,13.143V15.429C5.602,15.732 5.499,16.022 5.315,16.237C5.131,16.451 4.881,16.571 4.621,16.571C4.361,16.571 4.111,16.451 3.927,16.237C3.743,16.022 3.64,15.732 3.64,15.429V13.143C3.64,12.84 3.743,12.549 3.927,12.335C4.111,12.12 4.361,12 4.621,12Z"
      android:strokeAlpha="0.6"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#3C3C43"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M16.396,14.286H5.602"
      android:strokeAlpha="0.6"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#3C3C43"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M3.64,14.286H0.696"
      android:strokeAlpha="0.6"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#3C3C43"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M12.47,6.286C12.731,6.286 12.98,6.406 13.164,6.62C13.348,6.835 13.452,7.125 13.452,7.429V9.714C13.452,10.017 13.348,10.308 13.164,10.522C12.98,10.737 12.731,10.857 12.47,10.857C12.21,10.857 11.961,10.737 11.777,10.522C11.593,10.308 11.489,10.017 11.489,9.714V7.429C11.489,7.125 11.593,6.835 11.777,6.62C11.961,6.406 12.21,6.286 12.47,6.286V6.286Z"
      android:strokeAlpha="0.6"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#3C3C43"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M11.489,8.571H0.696"
      android:strokeAlpha="0.6"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#3C3C43"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M16.396,8.571H13.452"
      android:strokeAlpha="0.6"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#3C3C43"
      android:strokeLineCap="round"/>
</vector>

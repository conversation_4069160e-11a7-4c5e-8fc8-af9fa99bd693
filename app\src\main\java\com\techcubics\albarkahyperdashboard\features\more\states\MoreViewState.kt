package com.techcubics.albarkahyperdashboard.features.more.states

import com.techcubics.domain.auth.models.LoginResponse
import com.techcubics.domain.auth.models.User
import com.techcubics.domain.more.models.Language

sealed class MoreViewState {

    object Idle : MoreViewState()
    object Loading : MoreViewState()
    object BtnLoading : MoreViewState()
    object BtnSuccess : MoreViewState()
    data class Profile(val user : LoginResponse) : MoreViewState()
    data class Error(val message: String) : MoreViewState()
    data class LanguagesList(val languageList: List<Language>?) : MoreViewState()
    data class AccountSettingConfirmMessage(val message : String) : MoreViewState()
}
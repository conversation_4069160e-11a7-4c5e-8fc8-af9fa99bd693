<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="match_parent"
    android:theme="@style/Theme.TextInputLayout">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_horizontal"
            android:layout_marginTop="100dp"
            android:orientation="vertical">
            <TextView
                android:layout_marginStart="16dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/welcome_to_elbarkah"
                android:textAppearance="@style/ExtraLargeBoldDark2Text" />
            <TextView
                android:id="@+id/signin_owner"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/owners_signin"
                android:textAppearance="@style/LargeBoldAppColorText" />
            <TextView
                android:id="@+id/signin_admin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:visibility="gone"
                android:text="@string/admin_signin"
                android:textAppearance="@style/LargeBoldAppColorText" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="18dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/et_style"
                android:gravity="right"
                android:layoutDirection="ltr"
                android:orientation="horizontal"
                android:weightSum="4"
                android:padding="9dp"
                >

                <com.hbb20.CountryCodePicker
                    android:id="@+id/country_picker"
                    android:layout_width="wrap_content"
                    app:ccp_showNameCode="false"
                    app:ccp_textSize="14sp"
                    android:layout_height="match_parent"
                    app:ccp_defaultNameCode="EG" />

                <EditText
                    android:id="@+id/email_or_phone_edt"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:background="@drawable/et_style"
                    android:gravity="center_vertical"
                    android:hint="@string/email_or_phone"
                    android:singleLine="false"
                    android:textAppearance="@style/Medium2RegularDarkSearch"
                    />
            </LinearLayout>

            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                app:hintEnabled="false"
                app:boxCornerRadiusBottomEnd="8dp"
                app:boxCornerRadiusBottomStart="8dp"
                app:boxCornerRadiusTopEnd="8dp"
                app:boxCornerRadiusTopStart="8dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                app:errorEnabled="false"
                app:boxStrokeColor="@drawable/text_input_box_stroke_color"
                app:helperTextEnabled="false"
                app:startIconDrawable="@drawable/ic_password"
                app:startIconTint="@color/app_color"
                android:gravity="center_vertical"
                android:padding="1dp"
                app:passwordToggleEnabled="true">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/password_edt"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/password"
                    android:inputType="textPassword"
                    android:background="@drawable/et_style"
                    android:textAppearance="@style/Medium2RegularDarkSearch" />
            </com.google.android.material.textfield.TextInputLayout>
            <TextView
                android:id="@+id/forget_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="16dp"
                android:layout_marginStart="16dp"
                android:background="?attr/selectableItemBackground"
                android:text="@string/forget_password"
                android:gravity="end"
                android:textAppearance="@style/Medium2RegularDarkSearch" />

            <include
                android:id="@+id/sign_btn_progress"
                layout="@layout/btn_progress"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="24dp"
                android:layout_marginEnd="16dp" />
            <include
                android:id="@+id/terms_conditions_layout"
                layout="@layout/include_terms_and_conditions_layout"/>
            <TextView
                android:id="@+id/login_owner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:visibility="gone"
                android:background="?attr/selectableItemBackground"
                android:text="@string/login_owner"
                android:gravity="center_horizontal"
                android:textAppearance="@style/Medium2RegularDarkSearch" />
            <TextView
                android:id="@+id/login_admin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:visibility="visible"
                android:background="?attr/selectableItemBackground"
                android:text="@string/login_admin"
                android:gravity="center_horizontal"
                android:textAppearance="@style/Medium2RegularDarkSearch" />

        </LinearLayout>


    </androidx.core.widget.NestedScrollView>

    <include
        android:id="@+id/action_loading_animation"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>


package com.techcubics.albarkahyperdashboard.utils.components

import android.app.Dialog
import android.content.DialogInterface
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup
import android.view.Window
import androidx.appcompat.app.AppCompatDialogFragment
import androidx.navigation.fragment.findNavController
import com.techcubics.albarkahyperdashboard.MainActivity
import com.techcubics.albarkahyperdashboard.databinding.DialogAddProductBinding
import com.techcubics.albarkahyperdashboard.features.storage.fragments.StorageFragmentDirections
import com.techcubics.albarkahyperdashboard.utils.listeners.OnAttachChangeListener
import com.techcubics.resources.R


class AddProductDialog(val listener: OnAttachChangeListener) :
    AppCompatDialogFragment() {
    private var _binding: DialogAddProductBinding? = null
    private val binding get() = _binding!!
    private lateinit var onAttachChangeListener: OnAttachChangeListener

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        _binding = DialogAddProductBinding.inflate(layoutInflater)
        listener.updateAttached(true)
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setContentView(binding.root)
        onAttachChangeListener = requireActivity() as MainActivity
        binding.addProduct.textView.text=getString(R.string.add_product)
        binding.addCodedProduct.textView.text=getString(R.string.add_coded_product)
        binding.addProduct.root.setOnClickListener {
            val action = StorageFragmentDirections.viewAddNewProductFragment(null)
            findNavController().navigate(action)
            dismiss()
        }
        binding.addCodedProduct.root.setOnClickListener {
            val action = StorageFragmentDirections.viewCodedProducts()
            findNavController().navigate(action)
            dismiss()
        }
        dialog.setCanceledOnTouchOutside(true)
        dialog.setCancelable(true)
        dialog.window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        dialog.window?.setGravity(Gravity.CENTER)
        setStyle(STYLE_NORMAL, com.techcubics.resources.R.style.CustomAlertDialogTheme)
        return dialog
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        listener.updateAttached(false)
    }
}

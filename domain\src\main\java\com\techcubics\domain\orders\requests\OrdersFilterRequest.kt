package com.techcubics.domain.orders.requests

import com.google.gson.annotations.SerializedName
import com.techcubics.domain.storage.requests.Paginator
import com.techcubics.domain.storage.requests.Sort
import retrofit2.http.Field

data class OrdersFilterRequest(
    @SerializedName("filter[status]") var status: String,
    @SerializedName("filter[sort]") var sort: String =Sort.DESC.value,
    @SerializedName("filter[payment_method]") var method : String,
    var search: Map<String,String> ,
    @SerializedName("filter[governorate_id]") var governorate : String,
    @SerializedName("filter[region_ids]") var regionList : List<Int>,
    @SerializedName("paginator") var paginator: Int=Paginator.ENABLED.value
)
data class OrdersFilterRequestBody(
    var status: String,
    var sort: String,
    var method : String,
    var search: Map<String, String>,
    var governorate : String,
    var regionList : List<Int>,
    var paginator: Int
)

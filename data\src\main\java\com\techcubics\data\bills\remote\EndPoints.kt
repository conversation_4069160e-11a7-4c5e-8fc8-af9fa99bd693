package com.techcubics.data.bills.remote

import com.techcubics.data.bills.utils.Constants
import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.orders.models.Governorate
import com.techcubics.domain.orders.models.Order
import com.techcubics.domain.orders.models.OrderStatus
import com.techcubics.domain.orders.models.Region
import retrofit2.Response
import retrofit2.http.*

interface EndPoints {
    @FormUrlEncoded
    @POST(Constants.filter)
    suspend fun ordersByFilter(
        @Path("userType") userType : String,
        @Query("page") page: Int,
        @Field("filter[status]") status: String,
        @Field("filter[sort]") sort: String,
        @FieldMap filter: Map<String, String>,
        @Field("paginator") paginator: Int
    ): Response<BaseResponse<MutableList<Order>>>
   @FormUrlEncoded
    @POST(Constants.filter)
    suspend fun ordersByFilter(
        @Path("userType") userType : String,
        @Query("page") page: Int,
        @Field("filter[status]") status: String,
        @Field("filter[sort]") sort: String,
        @Field("filter[region_ids][]") regionList : List<Int>,
        @FieldMap filter: Map<String, String>,
        @Field("paginator") paginator: Int
        ): Response<BaseResponse<MutableList<Order>>>

    @GET(Constants.orderDetails)
    suspend fun orderDetails(@Path("userType") userType : String,@Path("id") id: String): Response<BaseResponse<Order>>

    @GET(Constants.orderStatus)
    suspend fun getOrdersStatus(@Path("userType") userType : String): Response<BaseResponse<MutableList<OrderStatus>>>

    @POST(Constants.updateOrderStatus)
    suspend fun updateOrderStatus(@Path("userType") userType : String,@Path("id") id: String): Response<BaseResponse<Order>>

    @POST(Constants.cancelOrder)
    suspend fun cancelOrder(
        @Path("userType") userType : String,
        @Path("id") id: String,
        @Query("reason_message") reasonMessage: String
    ): Response<BaseResponse<Order>>

    @POST(Constants.returnOrder)
    suspend fun returnOrder(
        @Path("userType") userType : String,
        @Path("id") id: String,
        @Query("returned_message") reasonMessage: String
    ): Response<BaseResponse<Order>>
    @GET(Constants.governorate)
    suspend fun getGovernorate(@Path("userType") userType : String,@Query("country_id")countryId : String = "1") : Response<BaseResponse<MutableList<Governorate>>>

    @GET(Constants.regionsByGovernorate)
    suspend fun getRegionsByGovernorate(@Path("userType") userType : String,@Query("governorate_id") governorateId : String,@Query("country_id")countryId : String = "1") : Response<BaseResponse<MutableList<Region>>>

}
package com.techcubics.albarkahyperdashboard.features.reports.fragments

import android.app.DatePickerDialog
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.techcubics.albarkahyperdashboard.R
import com.techcubics.albarkahyperdashboard.databinding.FragmentReportsBinding
import com.techcubics.albarkahyperdashboard.features.reports.adapters.ReportsAdapter
import com.techcubics.albarkahyperdashboard.features.reports.intents.ReportsIntent
import com.techcubics.albarkahyperdashboard.features.reports.states.ReportsViewState
import com.techcubics.albarkahyperdashboard.features.reports.viewmodels.ReportsViewModel
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.data.auth.utils.Constants
import com.techcubics.data.auth.utils.UserTypeEnum
import com.techcubics.domain.reports.request.ReportRequest
import com.techcubics.domain.reports.response.ReportResponse
import com.techcubics.domain.storage.models.Owner
import com.techcubics.domain.storage.models.Shop
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

class ReportsFragment : Fragment() {
    private var _binding: FragmentReportsBinding? = null
    private val binding get() = _binding!!
    private val reportsViewModel by viewModel<ReportsViewModel>()
    private lateinit var ownerAdapter: ArrayAdapter<String>
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private var selectedOwnerId = sharedPreferencesManager.getOwnerId()
    private lateinit var shopAdapter: ArrayAdapter<String>
    private var selectedShopId = -1
    private var selectedFromDate = ""
    private var selectedToDate = ""
    private var listOfOwners = arrayListOf<String>()
    private lateinit var adapter: ReportsAdapter
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentReportsBinding.inflate(inflater, container, false)
        initViews()
        events()
        observers()
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        binding.owner.setText("")
        binding.shop.setText("")
        binding.to.setText("", false)
        binding.from.setText("", false)
    }

    private fun initViews() {
        adapter = ReportsAdapter(requireContext(), arrayListOf())
        binding.reportsRV.adapter = adapter
        setEmptyReports(true)
        if (sharedPreferencesManager.getUserType() == UserTypeEnum.Admin.value) {
            getOwners()
        } else {
            binding.ownerCont.visibility = View.GONE
            getShops()
        }
        initCalenderFrom()
        initCalenderTo()
        binding.toolbarTitle.toolbarTitle.text =
            getString(com.techcubics.resources.R.string.nav_reports)
    }

    private fun setEmptyReports(visible : Boolean) {
        if(visible){
            binding.reportsRV.visibility = View.GONE
            binding.layout.layout.visibility = View.VISIBLE
            binding.layout.icon.setAnimation(com.techcubics.resources.R.raw.lottie_error)
        }else{
            binding.reportsRV.visibility = View.VISIBLE
            binding.layout.layout.visibility = View.GONE
        }
    }

    private fun getOwners() {
        lifecycleScope.launch { reportsViewModel.reportsIntent.send(ReportsIntent.GetOwners) }
    }

    private fun collectResponse(reportsViewState: ReportsViewState?) {
        Helper.loadingAnimationVisibility(View.GONE, binding.actionLoadingAnimation.root)
        binding.layout.root.visibility = View.GONE
        when (reportsViewState) {
            is ReportsViewState.Idle -> {}
            is ReportsViewState.GetReports -> {
                setReportsAdapter(reportsViewState.reports)
            }

            is ReportsViewState.GetOwners -> {
                setOwnerAdapter(reportsViewState.owners)
            }

            is ReportsViewState.GetShopsByOwnerId -> {
                setShopsAdapter(reportsViewState.shops)
            }

            is ReportsViewState.Error -> {
                if (!reportsViewState.message.contains(Constants.unauthenticated)) {
                    Helper.showErrorDialog(requireContext(), reportsViewState.message)
                }
                binding.reportsRV.visibility = View.GONE
                binding.layout.layout.visibility = View.VISIBLE
                binding.layout.icon.setAnimation(com.techcubics.resources.R.raw.lottie_error)
                binding.layout.tvMessage.text = reportsViewState.message
            }

            is ReportsViewState.ServerError -> {
                Helper.showErrorDialog(requireContext(), reportsViewState.error)
                binding.reportsRV.visibility = View.GONE
                binding.layout.layout.visibility = View.VISIBLE
                binding.layout.icon.setAnimation(com.techcubics.resources.R.raw.lottie_error)
                binding.layout.tvMessage.text = reportsViewState.error
            }

            is ReportsViewState.Loading -> {
                Helper.loadingAnimationVisibility(View.VISIBLE, binding.actionLoadingAnimation.root)
            }

            else -> {}
        }
        reportsViewModel.viewState.value = null
    }

    private fun setReportsAdapter(reports: ReportResponse) {
        val listOfReports = arrayListOf<String>()
        listOfReports.add(reports.countOrderNew.toString())
        listOfReports.add(reports.countOrderPreparing.toString())
        listOfReports.add(reports.countOrderDelivered.toString())
        listOfReports.add(reports.countOrderReturned.toString())
        listOfReports.add(reports.countOrderCanceled.toString())
        listOfReports.add(reports.countProducts.toString())
        listOfReports.add(reports.countCustomers.toString())

        listOfReports.add(changeNumberFormat(reports.totalSalesNew))
        listOfReports.add(changeNumberFormat(reports.totalSalesPreparing))
        listOfReports.add(changeNumberFormat(reports.totalSalesDelivered))
        listOfReports.add(changeNumberFormat(reports.totalSalesReturned))
        listOfReports.add(changeNumberFormat(reports.totalSalesCanceled))
        listOfReports.add(changeNumberFormat(reports.totalSales))

        if(listOfReports.isNotEmpty()){
            setEmptyReports(false)
            adapter.updateList(listOfReports)
        }else{
            setEmptyReports(true)
        }
    }

    private fun changeNumberFormat(totalSales: Double): String {
        val formatter = NumberFormat.getInstance(Locale.US)
        return formatter.format(totalSales.toInt())
    }

    private fun setShopsAdapter(shops: MutableList<Shop>) {
        Log.i("testRenter","shops")
        val list = mutableListOf<String>()
        for (shop in shops)
            shop.name?.let { list.add(it) }
        shopAdapter = ArrayAdapter(
            requireContext(),
            R.layout.item_dropdown,
            list
        )
        binding.shop.setAdapter(shopAdapter)
        binding.shop.setOnItemClickListener { _, _, i, l ->
            binding.shop.setText(shops[i].name,false)
            selectedShopId = shops[i].shopId!!
            getReports()
        }
    }

    private fun getReports() {
        lifecycleScope.launch {
            reportsViewModel.reportsIntent.send(
                ReportsIntent.GetReports(
                    ReportRequest(
                        ownerId = selectedOwnerId.toString(),
                        shopId = selectedShopId.toString(),
                        from = selectedFromDate,
                        to = selectedToDate
                    )
                )
            )
        }
    }

    private fun setOwnerAdapter(owners: MutableList<Owner>) {
        Log.i("testRenter","owners")

        for (owner in owners)
            owner.user?.name?.let { listOfOwners.add(it) }
        ownerAdapter = ArrayAdapter(
            requireContext(),
            R.layout.item_dropdown,
            listOfOwners
        )
        binding.owner.setAdapter(ownerAdapter)
        binding.owner.setOnItemClickListener { _, _, i, l ->
            binding.owner.setText(owners[i].user?.name,false)
            owners[i].ownerId?.let {
                selectedOwnerId = it
            }
            getShops()

        }
    }

    private fun getShops() {
        lifecycleScope.launch {
            reportsViewModel.reportsIntent.send(
                ReportsIntent.GetShopsByOwnerId(
                    selectedOwnerId
                )
            )
        }
    }

    private fun events() {

    }

    private fun observers() {
        lifecycleScope.launch { reportsViewModel.viewState.collect { collectResponse(it) } }
    }

    private fun initCalenderFrom() {
        val myCalender = Calendar.getInstance()
        val datePicker = DatePickerDialog.OnDateSetListener { _, year, month, day ->
            myCalender.set(Calendar.YEAR, year)
            myCalender.set(Calendar.MONTH, month)
            myCalender.set(Calendar.DAY_OF_MONTH, day)
            updateFromDateTextView(myCalender)

        }
        binding.fromCont.setEndIconOnClickListener {
            DatePickerDialog(
                requireContext(),
                com.techcubics.resources.R.style.MySpinnerDatePickerStyle,
                datePicker,
                myCalender.get(Calendar.YEAR),
                myCalender.get(Calendar.MONTH),
                myCalender.get(Calendar.DAY_OF_MONTH)
            ).show()
        }

        binding.from.setOnClickListener {
            DatePickerDialog(
                requireContext(),
                com.techcubics.resources.R.style.MySpinnerDatePickerStyle,
                datePicker,
                myCalender.get(Calendar.YEAR),
                myCalender.get(Calendar.MONTH),
                myCalender.get(Calendar.DAY_OF_MONTH)
            ).show()
        }

    }

    private fun initCalenderTo() {
        val myCalender = Calendar.getInstance()
        val datePicker = DatePickerDialog.OnDateSetListener { _, year, month, day ->
            myCalender.set(Calendar.YEAR, year)
            myCalender.set(Calendar.MONTH, month)
            myCalender.set(Calendar.DAY_OF_MONTH, day)
            updateToDateTextView(myCalender)

        }
        binding.toCont.setEndIconOnClickListener {
            DatePickerDialog(
                requireContext(),
                com.techcubics.resources.R.style.MySpinnerDatePickerStyle,
                datePicker,
                myCalender.get(Calendar.YEAR),
                myCalender.get(Calendar.MONTH),
                myCalender.get(Calendar.DAY_OF_MONTH)
            ).show()
        }

        binding.to.setOnClickListener {
            DatePickerDialog(
                requireContext(),
                com.techcubics.resources.R.style.MySpinnerDatePickerStyle,
                datePicker,
                myCalender.get(Calendar.YEAR),
                myCalender.get(Calendar.MONTH),
                myCalender.get(Calendar.DAY_OF_MONTH)
            ).show()
        }

    }

    private fun updateFromDateTextView(myCalender: Calendar) {
        val myFormat = "yyyy-MM-dd"
        val sdf = SimpleDateFormat(myFormat, Locale.UK)
        binding.from.setText(sdf.format(myCalender.time),false)
        selectedFromDate = sdf.format(myCalender.time)
        binding.toCont.isEnabled = true
        binding.toCont.isClickable = true
    }

    private fun updateToDateTextView(myCalender: Calendar) {
        val myFormat = "yyyy-MM-dd"
        val sdf = SimpleDateFormat(myFormat, Locale.UK)
        binding.to.setText(sdf.format(myCalender.time),false)
        selectedToDate = sdf.format(myCalender.time)
        getReports()
    }
}
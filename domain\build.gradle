plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-parcelize'
}

apply from: '../dependencies.gradle'
android {
    namespace 'com.techcubics.domain'
    compileSdk 35

    defaultConfig {
        minSdk 24
        targetSdk 35

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = '11'
    }
}

dependencies {

    implementation kotlinDependencies.core
    implementation androidxDependencies.core
    implementation materialDependencies.core
    //
    implementation retrofitDependencies.retrofit
    implementation retrofitDependencies.gson
    implementation retrofitDependencies.scalar
    //
    implementation httpOkDependencies.httpOK
    testImplementation juintDependencies.core
    androidTestImplementation testDependencies.core
    androidTestImplementation espressoDependencies.core
}
package com.techcubics.albarkahyperdashboard.features.storage.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.Adapter
import com.techcubics.albarkahyperdashboard.databinding.ItemCodedProductBinding
import com.techcubics.albarkahyperdashboard.databinding.ItemProductBinding
import com.techcubics.albarkahyperdashboard.features.storage.adapters.holders.CodedProductsViewHolder
import com.techcubics.albarkahyperdashboard.features.storage.adapters.holders.ProductViewHolder
import com.techcubics.albarkahyperdashboard.utils.listeners.OnItemClickListener
import com.techcubics.domain.storage.models.Product

class ProductsAdapter(
    var products: MutableList<Product>?,
    private val listener: OnItemClickListener,
    private val isCodedProduct: Boolean = false
) : Adapter<RecyclerView.ViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == 1) {
            val binding =
                ItemProductBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            ProductViewHolder(binding, parent.context, listener)
        } else {
            val binding =
                ItemCodedProductBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            CodedProductsViewHolder(binding, parent.context, listener)
        }

    }

    override fun getItemViewType(position: Int): Int {
        super.getItemViewType(position)
        return if (!isCodedProduct) {
            1
        } else {
            2
        }
    }

    override fun getItemCount(): Int {
        return products?.size ?: 0
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is CodedProductsViewHolder){
            holder.setData(products?.get(position))
        }else if (holder is ProductViewHolder){
            holder.setData(products?.get(position))
        }
    }

    fun updateProducts(products: MutableList<Product>?) {
        val diffResult = DiffUtil.calculateDiff(ProductsDiffUtilCallback(this.products, products))
        this.products?.clear()
        products?.let { this.products?.addAll(it) }
        diffResult.dispatchUpdatesTo(this)
    }
}

class ProductsDiffUtilCallback(
    private val oldList: List<Product>?,
    private val newList: List<Product>?
) : DiffUtil.Callback() {
    override fun getOldListSize(): Int {
        return oldList?.size?:0
    }

    override fun getNewListSize(): Int {
        return newList?.size?:0
    }

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return oldList?.get(oldItemPosition)?.id == newList?.get(newItemPosition)?.id
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return when (oldList?.size) {
            newList?.size -> true
            else -> false
        }
    }

    override fun getChangePayload(oldItemPosition: Int, newItemPosition: Int): Any {
        return when (oldItemPosition) {
            newItemPosition -> true
            else -> false
        }
    }

}
package com.techcubics.albarkahyperdashboard.utils.components

import android.content.Context
import android.view.View
import androidx.core.content.res.ResourcesCompat
import com.techcubics.albarkahyperdashboard.databinding.BtnProgressBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class ProgressButton(private val context: Context,private val progressBtnLayoutBinding: BtnProgressBinding) {

    private lateinit var binding: BtnProgressBinding

    init {
        binding  = progressBtnLayoutBinding
    }

    fun btnActivated(){
        binding.progressBar2.visibility = View.VISIBLE
        binding.textView.visibility = View.GONE
    }

    fun btnFinishedSuccessfully(btnText : String,style : Int?){
        CoroutineScope(Dispatchers.Main).launch {
            binding.constraintsLayout.background = ResourcesCompat.getDrawable(context.resources,com.techcubics.resources.R.drawable.btn_green,context.theme)?.mutate()
            binding.progressBar2.visibility = View.GONE
            binding.textView.visibility = View.VISIBLE
            binding.textView.text = context.getString(com.techcubics.resources.R.string.done)
            delay(2000)
            binding.constraintsLayout.background = ResourcesCompat.getDrawable(context.resources,com.techcubics.resources.R.drawable.btn_style,context.theme)?.mutate()
            binding.progressBar2.visibility = View.GONE
            binding.textView.text = btnText
            if (style != null){
                binding.textView.setTextAppearance(
                    context,
                    style
                )
            }
        }

    }

    fun btnFinishedFailed(btnText: String,style: Int?) {
        CoroutineScope(Dispatchers.Main).launch {
            binding.constraintsLayout.background = ResourcesCompat.getDrawable(context.resources,com.techcubics.resources.R.drawable.btn_red,context.theme)?.mutate()
            binding.progressBar2.visibility = View.GONE
            binding.textView.visibility = View.VISIBLE
            binding.textView.text = context.getString(com.techcubics.resources.R.string.fail)
            delay(2000)
            binding.constraintsLayout.background = ResourcesCompat.getDrawable(context.resources,com.techcubics.resources.R.drawable.btn_style,context.theme)?.mutate()
            binding.progressBar2.visibility = View.GONE
            binding.textView.text = btnText
            if (style != null){
                binding.textView.setTextAppearance(
                    context,
                    style
                )
            }
        }
    }

}
package com.techcubics.domain.reports.response

import com.google.gson.annotations.SerializedName

data class ReportResponse(
    @SerializedName("count_order_new") var countOrderNew: Int = 0,
    @SerializedName("count_order_preparing") var countOrderPreparing: Int = 0,
    @SerializedName("count_order_delivered") var countOrderDelivered: Int = 0,
    @SerializedName("count_order_returned") var countOrderReturned: Int = 0,
    @SerializedName("count_order_canceled") var countOrderCanceled: Int = 0,
    @SerializedName("count_products") var countProducts: Int = 0,
    @SerializedName("count_customers") var countCustomers: Int = 0,
    @SerializedName("total_sales") var totalSales: Double = 0.0,
    @SerializedName("total_sales_new") var totalSalesNew: Double = 0.0,
    @SerializedName("total_sales_preparing") var totalSalesPreparing: Double = 0.0,
    @SerializedName("total_sales_delivered") var totalSalesDelivered: Double = 0.0,
    @SerializedName("total_sales_returned") var totalSalesReturned: Double = 0.0,
    @SerializedName("total_sales_canceled") var totalSalesCanceled: Double = 0.0
)

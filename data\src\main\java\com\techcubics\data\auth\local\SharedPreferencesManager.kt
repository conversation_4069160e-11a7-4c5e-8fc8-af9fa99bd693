package com.techcubics.data.auth.local

import android.content.SharedPreferences
import com.google.gson.Gson
import com.techcubics.data.auth.utils.Constants
import com.techcubics.data.auth.utils.LoginStateEnum
import com.techcubics.data.auth.utils.UserTypeEnum
import com.techcubics.domain.auth.models.LoginResponse
import com.techcubics.domain.auth.models.User
import java.util.*


class SharedPreferencesManager(private val prefs: SharedPreferences) {

    private val TOKEN = "TOKEN"
    private val LANGUAGE = "language"
    private val ID = "ID"
    private val NAME = "NAME"
    private val OWNERID = "OWNERID"
    private val EMAIL = "EMAIL"
    private val DEFAULTLANG = "DEFAULTLANG"
    private val PHONE = "PHONE"
    private val COUNTRYCODE = "COUNTRYCODE"
    private val COUNTRYID = "COUNTRYID"
    private val CHAT_ID = "chat_id"


    fun getToken(): String = prefs.getString(TOKEN, " ")!!

    fun saveToken(value: String?) {
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(TOKEN, value)
        prefsEditor.apply()

    }

    fun saveCountryID(value : String){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(COUNTRYID, value)
        prefsEditor.apply()
    }

    fun getCountryID(): String = prefs.getString(COUNTRYID, "1")!!

    fun saveLanguage(value: String) {
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(LANGUAGE, value)
        prefsEditor.apply()
    }

    fun getLanguage(): String = prefs.getString(LANGUAGE, Locale.getDefault().language)!!

    fun saveCountryCode(value : String){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(COUNTRYCODE, value)
        prefsEditor.apply()
    }
    fun getCountryCode(): String = prefs.getString(COUNTRYCODE, "EG")!!
    fun getID(): Int? = prefs.getInt(ID, -1)

    fun saveID(value: Int?) {
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putInt(ID, value!!)
        prefsEditor.apply()

    }

    fun setTerms(value: Boolean){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putBoolean("terms", value)
        prefsEditor.apply()
    }

    fun getLoginState():String=prefs.getString(Constants.LOGIN_STATE, LoginStateEnum.Other.value)!!

    fun setLoginState(state:String){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(Constants.LOGIN_STATE, state)
        prefsEditor.apply()
    }

    fun getName() : String = prefs.getString(NAME,"")!!

    fun saveName(value: String?){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(NAME, value)
        prefsEditor.apply()
    }

    fun getOwnerId() : Int = prefs.getInt(OWNERID,-1)

    fun saveOwnerId(value: Int){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putInt(OWNERID, value)
        prefsEditor.apply()
    }

    fun saveDefaultLangCode(code: String) {
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(DEFAULTLANG, code)
        prefsEditor.apply()
    }

    fun getDefaultLangCode() : String = prefs.getString(DEFAULTLANG, "")!!

    fun isTermsAccepted() : Boolean = prefs.getBoolean("terms", false)

    fun saveEmail(email: String?) {
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(EMAIL, email)
        prefsEditor.apply()
    }

    fun getEmail() : String = prefs.getString(EMAIL,"")!!

    fun savePhone(phone: String?) {
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(PHONE, phone)
        prefsEditor.apply()
    }

    fun getPhone() : String = prefs.getString(PHONE,"")!!

    fun <T> saveObject(key: String?, obj: T?) {
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        val gson : Gson = Gson()
        val json = gson.toJson(obj)
        prefsEditor.putString(key, json ?: "EMPTYOBJECT")
        prefsEditor.apply()
    }

    fun getUser(): LoginResponse {
        val gson = Gson()
        val json: String = prefs.getString(Constants.USER, "")!!
        val obj: LoginResponse = gson.fromJson(json, LoginResponse::class.java)
        return obj
    }

    fun getUserPhoto(): String? = prefs.getString(Constants.PHOTO,"")!!

    fun saveUserPhoto(value: String?) {
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(Constants.PHOTO, value)
        prefsEditor.apply()
    }

    fun getUserType(): String = prefs.getString(Constants.userType,UserTypeEnum.Owner.value)!!

    fun saveUserType(value: String) {
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(Constants.userType, value)
        prefsEditor.apply()
    }


}
plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    id 'androidx.navigation.safeargs.kotlin'
    id 'kotlin-kapt'
}

apply from: '../dependencies.gradle'

android {
    namespace 'com.techcubics.albarkahyperdashboard'
    compileSdk 34
    signingConfigs {
        release {

            keyAlias project.property("signing.keyId")
            keyPassword project.property("signing.keyPassword")
            storeFile file(project.property("signing.storePath"))
            storePassword project.property("signing.storePassword")

        }

        debug {

            keyAlias project.property("signing.keyId")
            keyPassword project.property("signing.keyPassword")
            storeFile file(project.property("signing.storePath"))
            storePassword project.property("signing.storePassword")

        }



    }
    defaultConfig {
        applicationId "com.techcubics.albarkahyperdashboard"
        minSdk 24
        targetSdk 34
        versionCode 17
        versionName "1.3.1"
        multiDexEnabled true
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    bundle {
        language {
            enableSplit = false
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = '11'
    }
    buildFeatures {
        viewBinding true
        dataBinding true
        buildConfig true
    }

}

configurations.all {
    exclude group: 'com.google.android.play', module: 'core'
}

dependencies {

    implementation kotlinDependencies.core
    implementation constraintDependencies.core
    implementation androidxDependencies.core
    implementation materialDependencies.core

    testImplementation juintDependencies.core
    androidTestImplementation testDependencies.core
    androidTestImplementation espressoDependencies.core

    implementation project(path: ':data')
    implementation project(path: ':domain')
    implementation project(path: ':resources')

    implementation googleServicesDependencies.auth
    //lifeCycle
    implementation lifecycleDependencies.viewModel
    implementation lifecycleDependencies.liveData
    //bottomBar
    implementation navDependencies.fragment
    implementation navDependencies.ui
    implementation navDependencies.dynamicFeatures
    implementation navDependencies.testing
    //glide
    implementation glideDependencies.glide
    implementation glideDependencies.annotation
    //circleImage
    implementation circleImageDependencies.mikhaell
    implementation circleImageDependencies.hdodenhof
    //shimmer
    implementation shimmerLayoutDependencies.shimmerLayout
    //lottie
    implementation lottieDependencies.lottie
    //paging
    implementation pagingDependencies.paging
    //seek
    implementation rangeseekbarDependencies.seekbar
    //favorite button
    implementation favoriteButtonDependencies.fav_button
    //dots indicator
    implementation dotsIndicatorDependencies.dots_indicator
    //youtube player
    implementation youtubePlayerDependencies.youtube_player
    //facebook
    implementation facebookDepdencies.sdk
    //phone verfication
    implementation phoneVerficationDepdencies.browser
    implementation phoneVerficationDepdencies.google_safetynet
    //countryCode
    implementation countryCodeDependencies.ccp
    //compressor
    implementation compressorDepdencies.compressor
    //material
    implementation materialDepdencies.material
    //firebase
    implementation firebaseDependencies.database
    implementation firebaseDependencies.firestore
    implementation firebaseDependencies.auth
    implementation firebaseDependencies.storage
    implementation firebaseAuthDependencies.core
    implementation firebaseDependencies.remote_config
    //QR
    implementation zingDepdencies.core
    coreLibraryDesugaring zingDepdencies.coreLibraryDesugaring
    implementation zingDepdencies.multidex
    //FCM
    implementation platform(firebaseFcmDependencies.bom)
    implementation firebaseFcmDependencies.messaging
    implementation firebaseFcmDependencies.crashlytics
    implementation firebaseFcmDependencies.analytics
    //
    implementation tabDependencies.smarttablibrary
    implementation tabDependencies.smarttabutil
    //map
    implementation googlemapDependencies.core
    //location
    implementation googlemapLocationDependencies.core
    // Preference
    implementation prefencesDependencies.core

    implementation googleMapPlacesDependencies.core

    implementation googleMapUtils.maputils

    implementation phoenixDependencies.core



    implementation photoview_dependecy
    implementation httpOkDependencies.httpOK

    //koin
    implementation koinDependencies.koin
    implementation koinDependencies.koin_android
    implementation koinDependencies.javaCompat
    implementation koinDependencies.workManager
    implementation koinDependencies.navGraph
    // implementation googlePlayDependencies.core
    implementation localHelperDependencies
    implementation toggle_dependencies
    implementation customToastDependencies.core

    implementation searchable_multiselect
    implementation previewLinkDependency
    implementation colorPickerDependency
    implementation stepper_dependencies
    implementation(platform("org.jetbrains.kotlin:kotlin-bom:1.9.24"))
}
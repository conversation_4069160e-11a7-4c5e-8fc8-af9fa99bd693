package com.techcubics.domain.orders.models

import com.google.gson.annotations.SerializedName

data class Location (

  @SerializedName("id"               ) var id              : Int?    = null,
  @SerializedName("customer_id"      ) var customerId      : Int?    = null,
  @SerializedName("first_name"       ) var firstName       : String? = null,
  @SerializedName("last_name"        ) var lastName        : String? = null,
  @SerializedName("phone"            ) var phone           : String? = null,
  @SerializedName("phone_verified"   ) var phoneVerified   : String? = null,
  @SerializedName("lat"              ) var lat             : Double? = null,
  @SerializedName("lng"              ) var lng             : Double? = null,
  @SerializedName("address"          ) var address         : String? = null,
  @SerializedName("country_id"       ) var countryId       : Int?    = null,
  @SerializedName("governorate_id"   ) var governorateId   : Int?    = null,
  @SerializedName("region_id"        ) var regionId        : Int?    = null,
  @SerializedName("building_type"    ) var buildingType    : String? = null,
  @SerializedName("street"           ) var street          : String? = null,
  @SerializedName("building_number"  ) var buildingNumber  : String? = null,
  @SerializedName("floor_no"         ) var floorNo         : String? = null,
  @SerializedName("apartment_number" ) var apartmentNumber : String? = null,
  @SerializedName("notes"            ) var notes           : String? = null,
  @SerializedName("created_at"       ) var createdAt       : String? = null,
  @SerializedName("updated_at"       ) var updatedAt       : String? = null

)
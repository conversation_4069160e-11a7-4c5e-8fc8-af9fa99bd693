package com.techcubics.albarkahyperdashboard.features.storage.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.techcubics.albarkahyperdashboard.features.storage.intents.DiscountIntent
import com.techcubics.albarkahyperdashboard.features.storage.states.DiscountsStates
import com.techcubics.domain.common.Constants
import com.techcubics.domain.orders.requests.DeleteImageRequest
import com.techcubics.domain.storage.enums.ProductAction
import com.techcubics.domain.storage.requests.AddNewDiscount
import com.techcubics.domain.storage.usecases.DeleteImagesUseCase
import com.techcubics.domain.storage.usecases.GetCategoriesUseCase
import com.techcubics.domain.storage.usecases.GetOwnersUseCase
import com.techcubics.domain.storage.usecases.GetDiscountDetailsUseCase
import com.techcubics.domain.storage.usecases.GetProductsByOwnerAndShopIdUseCase
import com.techcubics.domain.storage.usecases.GetShopsByOwnerIdUseCase
import com.techcubics.domain.storage.usecases.GetSubCategoriesUseCase
import com.techcubics.domain.storage.usecases.SetDiscountStatusUseCase
import com.techcubics.domain.storage.usecases.SetStatusUseCase
import com.techcubics.domain.storage.usecases.StoreDiscountUseCase
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.consumeAsFlow
import kotlinx.coroutines.launch

class DiscountViewModel(
    private val getDiscountDetailsUseCase: GetDiscountDetailsUseCase,
    private val setDiscountStatusUseCase: SetDiscountStatusUseCase,
    private val getOwnersUseCase: GetOwnersUseCase,
    private val getShopsByOwnerIdUseCase: GetShopsByOwnerIdUseCase,
    private val storeDiscountUseCase: StoreDiscountUseCase,
    private val getProductsByOwnerAndShopIdUseCase: GetProductsByOwnerAndShopIdUseCase
) : ViewModel() {
    val discountIntent = Channel<DiscountIntent>(Channel.UNLIMITED)
    private val _discountState = MutableStateFlow<DiscountsStates>(DiscountsStates.Idle)
    val discountState: StateFlow<DiscountsStates> get() = _discountState

    init {
        handleIntent()
    }

    private fun handleIntent() {
        viewModelScope.launch {
            discountIntent.consumeAsFlow().collect {
                when (it) {
                    is DiscountIntent.GetDiscountDetails -> getDiscountDetails(it.discountId)
                    is DiscountIntent.SetStatus -> setStatus(it.discountId)
                    is DiscountIntent.GetOwners -> getOwners()
                    is DiscountIntent.GetShopsByOwnerId -> getShopsByOwnerId(it.ownerId)
                    is DiscountIntent.StoreUpdateDiscount -> storeDiscount(it.request, it.action)
                    is DiscountIntent.GetProductsByOwnerAndShopId -> getProductsByOwnerAndShopId(it.ownerId, it.shopId)
                    else -> {}
                }
            }
        }
    }

    private fun getProductsByOwnerAndShopId(ownerId: Int, shopId: Int) {
        _discountState.value = DiscountsStates.Loading
        viewModelScope.launch {
            val result = getProductsByOwnerAndShopIdUseCase.invoke(ownerId,shopId)
            _discountState.value = try {
                if (result?.data != null) {
                    DiscountsStates.ViewProducts(result.data!!)
                } else {
                    DiscountsStates.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                DiscountsStates.Error(e.localizedMessage)
            }
        }
    }


    private fun storeDiscount(request: AddNewDiscount, action: ProductAction) {
        _discountState.value = DiscountsStates.Loading
        viewModelScope.launch {
            val result = storeDiscountUseCase.invoke(request, action)
            _discountState.value = try {
                if (result?.data != null) {
                    DiscountsStates.Success
                } else {
                    DiscountsStates.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                DiscountsStates.Error(e.localizedMessage)
            }
        }
    }



    private fun getShopsByOwnerId(ownerId: Int) {
        _discountState.value = DiscountsStates.Loading
        viewModelScope.launch {
            val result = getShopsByOwnerIdUseCase.invoke(ownerId)
            _discountState.value = try {
                if (result?.data != null) {
                    DiscountsStates.ViewShops(result.data)
                } else {
                    DiscountsStates.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                DiscountsStates.Error(e.localizedMessage)
            }
        }
    }

    private fun getOwners() {
        _discountState.value = DiscountsStates.Loading
        viewModelScope.launch {
            val result = getOwnersUseCase.invoke()
            _discountState.value = try {
                if (result?.data != null) {
                    DiscountsStates.ViewOwners(result.data)
                } else {
                    DiscountsStates.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                DiscountsStates.Error(e.localizedMessage)
            }
        }
    }

    private fun getDiscountDetails(discountId: Int) {
        _discountState.value = DiscountsStates.Loading
        viewModelScope.launch {
            val result = getDiscountDetailsUseCase.invoke(discountId)
            _discountState.value = try {
                if (result?.message?.contains(Constants.SERVER_ERROR) == true) {
                    DiscountsStates.ServerError(result.message!!)
                } else if (result?.data != null) {
                    DiscountsStates.ViewDiscountDetails(result.data)
                } else {
                    DiscountsStates.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                DiscountsStates.Error(e.localizedMessage)
            }
        }
    }

    private fun setStatus(discountId: Int) {
        viewModelScope.launch {
            val result = setDiscountStatusUseCase.invoke(discountId)
            _discountState.value = try {
                if (result?.data != null) {
                    DiscountsStates.Idle
                } else {
                    DiscountsStates.StatusError(result?.message ?: "error", discountId)
                }
            } catch (e: Exception) {
                DiscountsStates.StatusError(e.localizedMessage, discountId)
            }
        }
    }

}
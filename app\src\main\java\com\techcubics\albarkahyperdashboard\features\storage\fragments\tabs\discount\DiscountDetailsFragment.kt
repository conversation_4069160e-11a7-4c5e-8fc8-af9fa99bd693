package com.techcubics.albarkahyperdashboard.features.storage.fragments.tabs.discount


import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.techcubics.albarkahyperdashboard.databinding.FragmentDiscountDetailsBinding
import com.techcubics.albarkahyperdashboard.features.storage.adapters.ImagesAdapter
import com.techcubics.albarkahyperdashboard.features.storage.adapters.TranslationsAdapter
import com.techcubics.albarkahyperdashboard.features.storage.adapters.holders.toBoolean
import com.techcubics.albarkahyperdashboard.features.storage.intents.DiscountIntent
import com.techcubics.albarkahyperdashboard.features.storage.states.DiscountsStates
import com.techcubics.albarkahyperdashboard.features.storage.viewmodels.DiscountViewModel
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.listeners.NavigationBarVisibilityListener
import com.techcubics.albarkahyperdashboard.utils.listeners.OnItemsChangedListener
import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.data.auth.utils.Constants
import com.techcubics.data.auth.utils.UserTypeEnum
import com.techcubics.domain.storage.models.Discount
import com.techcubics.resources.R
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.Locale

class DiscountDetailsFragment : Fragment(), OnItemsChangedListener {
    private var _binding: FragmentDiscountDetailsBinding? = null
    private val binding get() = _binding!!
    private val viewModel by viewModel<DiscountViewModel>()
    private var discountId: Int = -1

    private var discount: Discount? = null

    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentDiscountDetailsBinding.inflate(inflater, container, false)
        initViews()
        events()
        observeViews()
        return binding.root
    }

    private fun initViews() {
        val bundle = arguments ?: return
        val args = DiscountDetailsFragmentArgs.fromBundle(bundle)
        discountId = args.discountId
        getDiscountDetails()
    }

    private fun getDiscountDetails() {
        lifecycleScope.launch {
            viewModel.discountIntent.send(DiscountIntent.GetDiscountDetails(discountId))
        }
    }

    private fun events() {
        binding.details.edit.textView.text =
            getString(R.string.edit)
        binding.toolbarFragment.mainToolbar.setNavigationOnClickListener {
            findNavController().popBackStack()
        }
        binding.toolbarFragment.tvTitle.visibility = View.VISIBLE
        binding.details.edit.constraintsLayout.setOnClickListener {
           val action = DiscountDetailsFragmentDirections.viewAddNewDiscountFragment(discount)
            findNavController().navigate(action)
        }
        binding.details.activeStatus.setOnClickListener {
            setStatus(discountId)
        }
       
    }

    private fun setStatus(id: Int) {
        lifecycleScope.launch {
            viewModel.discountIntent.send(DiscountIntent.SetStatus(id))
        }
    }

    private fun observeViews() {
        lifecycleScope.launch {
            viewModel.discountState.collect {
                binding.loading.root.visibility = View.GONE
                binding.msgLayout.root.visibility = View.GONE
                when (it) {
                    is DiscountsStates.Idle -> {}
                    is DiscountsStates.Loading -> {
                        binding.scrollView.visibility = View.GONE
                        binding.loading.root.visibility = View.VISIBLE
                    }

                    is DiscountsStates.ViewDiscountDetails -> {
                        discount = it.discount
                        renderDiscountDetails()
                    }

                    is DiscountsStates.Error -> {
                        if(!it.error.contains(Constants.unauthenticated)){
                            Helper.showErrorDialog(requireContext(), it.error)
                        }
                    }

                    is DiscountsStates.ServerError -> {
                        setMsgLayout(it.error, R.raw.lottie_error)
                    }

                    is DiscountsStates.StatusError -> {
                        Helper.showErrorDialog(requireContext(), it.error)
                             binding.details.activeStatus.isChecked = ( discount?.status ?: 0).toBoolean()
                    }

                    else -> {}
                }
            }
        }

    }

    private fun setMsgLayout(msg: String?, lottieIcon: Int) {
        binding.scrollView.visibility = View.GONE
        binding.msgLayout.root.visibility = View.VISIBLE
        binding.msgLayout.icon.setAnimation(lottieIcon)
        binding.msgLayout.tvMessage.text = msg
    }

    private fun renderDiscountDetails() {
        if (discount == null) {
            setMsgLayout(
                getString(R.string.message_empty_list_general),
                R.raw.empty_box_lottie
            )
        } else {
            binding.scrollView.visibility = View.VISIBLE
            binding.toolbarFragment.tvTitle.text = discount?.product?.name
            setShopData()
            setOwnerData()
            discount?.priceAfter?.let { setPrice(it, binding.details.discountPriceAfter) }
            discount?.priceBefore?.let { setPrice(it, binding.details.discountPriceBefore) }
            setDiscountData()
        }
    }


    private fun setDiscountData() {
        binding.details.activeStatus.isChecked = (discount?.status ?: 0).toBoolean()
        binding.details.tvTitle.text = discount?.product?.name
        binding.details.minQty.text = discount?.minimumOrderNumber.toString()
        binding.details.maxQty.text = discount?.maximumOrderNumber.toString()
         binding.details.startDate.text = discount?.startFormat.toString()
        binding.details.endDate.text = discount?.endFormat.toString()
        binding.details.discountPercentage.text = discount?.percent.toString()
        discount?.product?.icon?.let { Helper.loadImage(requireContext(),it,binding.productIcon) }
    }

    private fun setShopData() {
        Helper.loadImage(
            requireContext(),
            discount?.shop?.logo!!,
            binding.details.shopImage
        )

        binding.details.shopName.text = discount?.shop?.name
    }

    private fun setOwnerData() {
        if (sharedPreferencesManager.getUserType()==UserTypeEnum.Owner.value){
            binding.details.ownerCont.visibility= View.GONE
        }else {
            Helper.loadImage(
                requireContext(),
                discount?.owner?.user?.avatar!!,
                binding.details.ownerImage
            )

            binding.details.ownerName.text = discount?.owner?.user?.name
        }
    }



    private fun setPrice(price: Float, priceTextView: TextView) {
        "${
            java.text.NumberFormat.getNumberInstance(Locale.ENGLISH).format(price)
        } ${getString(R.string.currency_name)}".also {
            priceTextView.text = it
        }
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}



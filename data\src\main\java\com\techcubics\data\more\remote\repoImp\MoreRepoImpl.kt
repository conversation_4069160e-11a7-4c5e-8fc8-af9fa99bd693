package com.techcubics.data.more.remote.repoImp

import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.domain.auth.models.User
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.common.RepositoryResponse
import com.techcubics.data.more.remote.EndPoints
import com.techcubics.data.common.RetrofitBuilder
import com.techcubics.domain.auth.models.LoginResponse
import com.techcubics.domain.more.models.BannerData
import com.techcubics.domain.more.models.Language
import com.techcubics.domain.more.repositories.MoreRepo
import com.techcubics.domain.more.requests.AccountSettingRequest

class MoreRepoImpl(private val retrofitBuilder: RetrofitBuilder, private val sharedPreferencesManager: SharedPreferencesManager) : <PERSON><PERSON><PERSON><PERSON>,
    RepositoryResponse {

    private val api=retrofitBuilder.start()?.create(EndPoints::class.java)

    override suspend fun updateProfileCall(accountSettingRequest: AccountSettingRequest): BaseResponse<LoginResponse>? {
        val nameBody: RequestBody = accountSettingRequest.name.toRequestBody("text/plain;charset=utf-8".toMediaType())
        val phoneBody: RequestBody = accountSettingRequest.phone.toRequestBody("text/plain;charset=utf-8".toMediaType())
        val emailBody: RequestBody = accountSettingRequest.email.toRequestBody("text/plain;charset=utf-8".toMediaType())

        //photo
        val reqFile: RequestBody? =
            accountSettingRequest.avatar?.let { RequestBody.create("image/*".toMediaType(), it) }
        val body: MultipartBody.Part? = reqFile?.let {
            MultipartBody.Part.createFormData("avatar", accountSettingRequest.avatar?.name,
                it
            )
        }

        try {
            val result = api?.updateProfile(userType = sharedPreferencesManager.getUserType(),nameBody,phoneBody,emailBody,body)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }

    }

    override suspend fun getLanguages(): BaseResponse<List<Language>>? {
        try {
            val result = api?.getLanguages(userType = sharedPreferencesManager.getUserType())
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun getHomeBanner(): BaseResponse<MutableList<BannerData>>? {
        try {
            val result = api?.getHomeBanner(userType = sharedPreferencesManager.getUserType())
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun getBanners(): BaseResponse<MutableList<BannerData>>? {
        try {
            val result = api?.getBanners(userType = sharedPreferencesManager.getUserType())
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

}
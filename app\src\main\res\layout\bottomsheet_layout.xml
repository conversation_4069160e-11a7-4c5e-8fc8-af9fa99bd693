<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@drawable/bg_dialog">

    <LinearLayout
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="vertical"
        android:id="@+id/customer_layout">
        <TextView
            android:id="@+id/header"
            android:layout_marginStart="15dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/message"
            android:layout_marginTop="5dp"
            android:textAppearance="@style/LargeBoldDarkText"

            />
        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:layout_margin="15dp"
            android:layout_height="wrap_content">
            <LinearLayout
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/customer_name"
                    android:textStyle="bold"
                    android:textSize="15sp"
                    android:textAppearance="@style/Medium2RegularDarkSearch"/>
                <TextView
                    android:id="@+id/customer_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textStyle="bold"
                    android:textSize="15sp"
                    android:textAlignment="viewEnd"
                    android:gravity="end"
                    tools:text="@string/customer_name"
                    android:textAppearance="@style/Medium2RegularDarkSearch"/>
            </LinearLayout>
            <LinearLayout
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:textStyle="bold"
                    android:textSize="15sp"
                    android:layout_weight="1"
                    android:text="@string/facility_name"
                    android:textAppearance="@style/Medium2RegularDarkSearch"/>
                <TextView
                    android:id="@+id/facility_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textStyle="bold"
                    android:textSize="15sp"
                    android:textAlignment="viewEnd"
                    android:gravity="end"
                    tools:text="@string/customer_name"
                    android:textAppearance="@style/Medium2RegularDarkSearch"/>
            </LinearLayout>
            <LinearLayout
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textStyle="bold"
                    android:textSize="15sp"
                    android:text="@string/phone_number"
                    android:textAppearance="@style/Medium2RegularDarkSearch"/>
                <TextView
                    android:id="@+id/phone"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textAlignment="viewEnd"
                    android:gravity="end"
                    android:textStyle="bold"
                    android:textSize="15sp"
                    android:text="@string/customer_name"
                    android:textAppearance="@style/Medium2RegularDarkSearch"/>
            </LinearLayout>
            <LinearLayout
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/address"
                    android:textStyle="bold"
                    android:textSize="15sp"
                    android:textAppearance="@style/Medium2RegularDarkSearch"/>
                <TextView
                    android:id="@+id/address"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textAlignment="viewEnd"
                    android:gravity="end"
                    android:textStyle="bold"
                    android:textSize="15sp"
                    tools:text="@string/customer_name"
                    android:textAppearance="@style/Medium2RegularDarkSearch"/>
            </LinearLayout>

        </LinearLayout>
    </LinearLayout>
    <LinearLayout
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="vertical"
        android:id="@+id/status_layout">
        <TextView
            android:layout_marginStart="15dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/please_choose_order_status"
            android:layout_marginTop="5dp"
            android:textAppearance="@style/LargeBoldDarkText"

            />
        <TextView
            android:id="@+id/facility_sname"
            android:layout_marginStart="15dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:textColor="@color/color_gray_35"
            android:textAppearance="@style/Medium2BoldDarkText"

            />
        <RadioGroup
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:layout_margin="15dp"
            android:layoutDirection="ltr"
            android:layout_height="wrap_content">
            <RadioButton
                android:id="@+id/delivered"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="@style/Medium2RegularGray"
                android:textColor="@color/color_gray_36"
                android:textAlignment="viewEnd"
                android:text="@string/delivered"/>
            <TextView
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@drawable/divider"
                />
            <RadioButton
                android:id="@+id/refused"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="@style/Medium2RegularGray"
                android:textColor="@color/color_gray_36"
                android:textAlignment="viewEnd"
                android:text="@string/refused"/>
            <TextView
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@drawable/divider"
                />
            <RadioButton
                android:id="@+id/cancelled"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="@style/Medium2RegularGray"
                android:textColor="@color/color_gray_36"
                android:textAlignment="viewEnd"
                android:text="@string/cancelled"/>
            <TextView
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@drawable/divider"
                />
        </RadioGroup>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="wrap_content"
        android:clickable="true"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:focusable="true"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:background="@drawable/ic_gray_circle">
        <ImageView
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:src="@drawable/ic_close"
            app:tint="@color/color_gray_35" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
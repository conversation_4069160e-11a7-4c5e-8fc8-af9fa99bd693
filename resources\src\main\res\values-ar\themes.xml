<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->

    <style name="Theme.Main" parent="Palette">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/app_color</item>
        <item name="colorPrimaryVariant">@color/app_color</item>
        <item name="colorOnPrimary">@color/app_color</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/app_color</item>
        <item name="colorSecondaryVariant">@color/app_color</item>
        <item name="colorOnSecondary">@color/app_color</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/app_color</item>
        <!-- Customize your theme here. -->
        <item name="android:windowLightStatusBar">false</item>

    </style>

    <style name="FullScreen" parent="Theme.Main">
        <item name="android:windowTranslucentStatus">true</item>
        <!-- Customize your theme here. -->
        <item name="android:windowTranslucentNavigation">true</item>

    </style>

    <style name="Theme.TextInputLayout" parent="Theme.Main">
        <item name="colorPrimary">@color/gray</item>
    </style>
    <style name="Theme.Colored" parent="Theme.Main">
        <item name="colorPrimary">@color/app_color2</item>
        <item name="colorPrimaryVariant">@color/app_color</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/app_color2</item>
        <item name="colorSecondaryVariant">@color/app_color</item>
        <item name="colorOnSecondary">@color/black</item>
    </style>
</resources>
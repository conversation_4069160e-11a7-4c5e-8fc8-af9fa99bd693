package com.techcubics.albarkahyperdashboard.features.storage.intents

//import com.techcubics.domain.storage.requests.AddNewDiscount
import com.techcubics.domain.storage.enums.ProductAction
import com.techcubics.domain.storage.requests.AddNewDiscount
import com.techcubics.domain.storage.requests.ProductsFilter

sealed class DiscountIntent {
    data class GetDiscounts(val page: Int, val filter: ProductsFilter) : DiscountIntent()
    object GetOwners : DiscountIntent()
    data class GetShopsByOwnerId(val ownerId: Int) : DiscountIntent()
   data class GetDiscountDetails(val discountId: Int) : DiscountIntent()
    data class SetStatus(val discountId: Int) : DiscountIntent()
    data class StoreUpdateDiscount(val request: AddNewDiscount, val action: ProductAction) :
        DiscountIntent()

    data class GetProductsByOwnerAndShopId(val ownerId: Int,val shopId: Int) : DiscountIntent()
}

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="16dp"
    android:gravity="center_horizontal">

    <CheckBox
        android:id="@+id/terms_conditions_checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:theme="@style/Medium1BoldAppColorCheckBox" />

    <TextView
        android:id="@+id/terms_conditions_textview"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:text="@string/terms_and_conditions"
        android:textAppearance="@style/Medium2RegularDarkSearch"/>
</LinearLayout>


plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

apply from: '../dependencies.gradle'

android {
    namespace 'com.techcubics.resources'
    compileSdk 34

    defaultConfig {

        minSdk 24
        targetSdk 34
     

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = '11'
    }
}

dependencies {

    implementation kotlinDependencies.core
    implementation androidxDependencies.core
    implementation materialDependencies.core
    testImplementation juintDependencies.core
    androidTestImplementation testDependencies.core
    androidTestImplementation espressoDependencies.core
}
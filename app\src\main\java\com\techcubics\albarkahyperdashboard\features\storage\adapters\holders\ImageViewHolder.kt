package com.techcubics.albarkahyperdashboard.features.storage.adapters.holders

import android.content.Context
import android.view.View
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyperdashboard.databinding.ItemImageBinding
import com.techcubics.albarkahyperdashboard.features.storage.viewmodels.ProductViewModel
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.components.general.YouTubeHelper
import com.techcubics.albarkahyperdashboard.utils.listeners.OnItemsChangedListener
import com.techcubics.domain.storage.models.Image

class ImageViewHolder(
    private val context: Context,
    private val binding: ItemImageBinding,
    private val listener: OnItemsChangedListener?,
    private val viewModel: ProductViewModel?
) :
    RecyclerView.ViewHolder(binding.root) {

    fun setData(image: Image) {
        with(binding) {
            youtubePlayerView.visibility = View.GONE
            furnitureImg.visibility = View.VISIBLE

            Helper.loadImage(context, image.path, furnitureImg)
        }
    }

    fun setVideo(videoId: String) {
        with(binding) {
            furnitureImg.visibility = View.GONE
            youtubePlayerView.visibility = View.VISIBLE
            val youTubeHelper =
                YouTubeHelper(context as FragmentActivity, videoId, youtubePlayerView, 0f)
            youTubeHelper.initialize(false)
            setCurrentSeekTimeObserver(youTubeHelper)
            youTubeHelper.setFullScreenListener(fullScreen = { seconds ->
                listener?.onYoutubeSizeChanged(
                    seconds,
                    videoId,
                    bindingAdapterPosition
                )
            })
        }
    }

    private fun setCurrentSeekTimeObserver(youTubeHelper: YouTubeHelper) {
        viewModel?.seconds?.observe(listener as LifecycleOwner) { seconds ->
            binding.youtubePlayerView.exitFullScreen()
            youTubeHelper.setCurrentSeconds(seconds)
            youTubeHelper.setCurrentSeekTime()
        }
    }

}
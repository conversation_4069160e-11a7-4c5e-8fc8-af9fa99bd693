package com.techcubics.albarkahyperdashboard.features.storage.fragments.tabs.product


import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.techcubics.albarkahyperdashboard.databinding.FragmentProductDetailsBinding
import com.techcubics.albarkahyperdashboard.features.storage.adapters.ImagesAdapter
import com.techcubics.albarkahyperdashboard.features.storage.adapters.TranslationsAdapter
import com.techcubics.albarkahyperdashboard.features.storage.intents.ProductIntent
import com.techcubics.albarkahyperdashboard.features.storage.states.ProductsStates
import com.techcubics.albarkahyperdashboard.features.storage.viewmodels.ProductViewModel
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.listeners.NavigationBarVisibilityListener
import com.techcubics.albarkahyperdashboard.utils.listeners.OnItemsChangedListener
import com.techcubics.data.auth.utils.Constants
import com.techcubics.domain.storage.enums.ProductStatusTypes
import com.techcubics.domain.storage.models.Product
import com.techcubics.resources.R
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.Locale

class ProductDetailsFragment : Fragment(), OnItemsChangedListener {
    private var _binding: FragmentProductDetailsBinding? = null
    private val binding get() = _binding!!
    private val viewModel by viewModel<ProductViewModel>()
    private var productId: Int = -1
    private lateinit var imagesAdapter: ImagesAdapter
    private lateinit var translationsAdapter: TranslationsAdapter

    private var product: Product? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentProductDetailsBinding.inflate(inflater, container, false)
        initViews()
        events()
        observeViews()
        return binding.root
    }

    private fun initViews() {
        setImages()
        setTranslations()
        val bundle = arguments ?: return
        val args = ProductDetailsFragmentArgs.fromBundle(bundle)
        productId = args.productId
        getProductDetails()
    }

    private fun getProductDetails() {
        lifecycleScope.launch {
            viewModel.productIntent.send(ProductIntent.GetProductDetails(productId))
        }
    }

    private fun events() {
        binding.productDetailsContainer.edit.textView.text =
            getString(R.string.edit)
        binding.toolbarFragment.mainToolbar.setNavigationOnClickListener {
            findNavController().popBackStack()
        }
        binding.toolbarFragment.tvTitle.visibility = View.VISIBLE
        binding.productDetailsContainer.edit.constraintsLayout.setOnClickListener {
           val action = ProductDetailsFragmentDirections.viewAddNewProductFragment(product)
            findNavController().navigate(action)
        }
        binding.productDetailsContainer.activeStatus.setOnClickListener {
            setStatus(productId, ProductStatusTypes.Active)
        }
        binding.productDetailsContainer.wantedStatus.setOnClickListener {
            setStatus(productId, ProductStatusTypes.Wanted)
        }
    }

    private fun setStatus(id: Int, type: ProductStatusTypes) {
        lifecycleScope.launch {
            viewModel.productIntent.send(ProductIntent.SetStatus(id, type))
        }
    }

    private fun observeViews() {
        lifecycleScope.launch {
            viewModel.productState.collect {
                binding.loading.root.visibility = View.GONE
                binding.msgLayout.root.visibility = View.GONE
                when (it) {
                    is ProductsStates.Idle -> {}
                    is ProductsStates.Loading -> {
                        binding.scrollView.visibility = View.GONE
                        binding.loading.root.visibility = View.VISIBLE
                    }

                    is ProductsStates.ViewProductDetails -> {
                        product = it.product
                        renderProductDetails()
                    }

                    is ProductsStates.Error -> {
                        if(!it.error.contains(Constants.unauthenticated)){
                            Helper.showErrorDialog(requireContext(), it.error)
                        }
                    }

                    is ProductsStates.ServerError -> {
                        setMsgLayout(it.error, R.raw.lottie_error)
                    }

                    is ProductsStates.StatusError -> {
                        Helper.showErrorDialog(requireContext(), it.error)
                        when (it.type) {
                            ProductStatusTypes.Active -> binding.productDetailsContainer.activeStatus.isChecked =
                                (product?.status ?: false)

                            ProductStatusTypes.Wanted -> binding.productDetailsContainer.wantedStatus.isChecked =
                                (product?.wanted ?: false)
                        }
                    }

                    else -> {}
                }
            }
        }

    }

    private fun setMsgLayout(msg: String?, lottieIcon: Int) {
        binding.scrollView.visibility = View.GONE
        binding.msgLayout.root.visibility = View.VISIBLE
        binding.msgLayout.icon.setAnimation(lottieIcon)
        binding.msgLayout.tvMessage.text = msg
    }

    private fun renderProductDetails() {
        if (product == null) {
            setMsgLayout(
                getString(R.string.message_empty_list_general),
                R.raw.empty_box_lottie
            )
        } else {
            binding.scrollView.visibility = View.VISIBLE
            binding.toolbarFragment.tvTitle.text = product?.name
            setVisibilityOfDotsIndicator()
            setShopData()
            setOwnerData()
            product?.price?.let { setPrice(it, binding.productDetailsContainer.tvPrice) }
            setProductData()
        }
    }


    private fun setProductData() {
        binding.productDetailsContainer.activeStatus.isChecked = product?.status ?: false
        binding.productDetailsContainer.wantedStatus.isChecked = product?.wanted ?: false
        imagesAdapter.updateImagesAndVideo(
            product?.images ?: listOf(),
            product?.video
        )
        binding.productDetailsContainer.category.text = product?.category?.name
        binding.productDetailsContainer.subcategory.text = product?.subCategory?.name
        product?.category?.image?.let {
            Helper.loadImage(
                requireContext(),
                it,
                binding.productDetailsContainer.catImage
            )
        }
        product?.subCategory?.image?.let {
            Helper.loadImage(
                requireContext(),
                it,
                binding.productDetailsContainer.subcatImage
            )
        }
        binding.productDetailsContainer.productDetails.text = product?.description
        binding.productDetailsContainer.tvTitle.text = product?.name
        binding.productDetailsContainer.minQty.text = product?.minimumOrderNumber.toString()
        binding.productDetailsContainer.maxQty.text = product?.maximumOrderNumber.toString()
        product?.translations?.let { translationsAdapter.updateTranslations(it) }
    }

    private fun setShopData() {
        Helper.loadImage(
            requireContext(),
            product?.shop?.logo!!,
            binding.productDetailsContainer.shopImage
        )

        binding.productDetailsContainer.shopName.text = product?.shop?.name
    }

    private fun setOwnerData() {
        Helper.loadImage(
            requireContext(),
            product?.owner?.user?.avatar!!,
            binding.productDetailsContainer.ownerImage
        )

        binding.productDetailsContainer.ownerName.text = product?.owner?.user?.name
    }

    private fun setVisibilityOfDotsIndicator() {
        val size = product?.images?.size ?: 0
        val videoNotFound = product?.video.isNullOrEmpty()
        val isSingleItem =
            if (videoNotFound) {
                size <= 1
            } else {
                (size + 1) <= 1
            }
        if (isSingleItem) {
            binding.productDetailsContainer.dotsIndicator.visibility = View.GONE
        }
    }


    private fun setPrice(price: Float, priceTextView: TextView) {
        "${
            java.text.NumberFormat.getNumberInstance(Locale.ENGLISH).format(price)
        } ${getString(R.string.currency_name)}".also {
            priceTextView.text = it
        }
    }

    private fun setImages() {
        imagesAdapter = ImagesAdapter(
            requireContext(),
            listOf(),
            null,
            viewModel,
            this
        )
        binding.productImg.viewPager2.adapter = imagesAdapter
        binding.productDetailsContainer.dotsIndicator.attachTo(binding.productImg.viewPager2)
    }

    private fun setTranslations() {
        translationsAdapter = TranslationsAdapter(arrayListOf())
        binding.productDetailsContainer.rvTranslations.adapter = translationsAdapter
    }

    override fun onYoutubeSizeChanged(seconds: Float, vidId: String, position: Int) {
        val action = ProductDetailsFragmentDirections.viewYoutubeFullScreenFragment(vidId, seconds)
        findNavController().navigate(action)
        requireActivity().requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}



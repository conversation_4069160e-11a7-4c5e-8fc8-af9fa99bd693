<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/product_details_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="16dp"
    android:paddingBottom="10dp"
    android:paddingTop="10dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:textAlignment="viewStart"
        android:theme="@style/LargeBoldDarkText"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="لاب توب" />

    <LinearLayout
        android:id="@+id/price_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="10dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title">

        <TextView
            android:id="@+id/discount_percentage"
            style="@style/SmallBoldAppColorText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            app:drawableStartCompat="@drawable/ic_discount"
            tools:text="10%" />

        <TextView
            android:id="@+id/discount_price_after"
            style="@style/Medium2BoldGreenText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:textColor="@color/color_59"
            tools:text="40 EGP" />

        <TextView
            android:id="@+id/discount_price_before"
            style="@style/Medium2RegularGray"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:background="@drawable/line_diagonal2"
            tools:text="44 EGP" />
    </LinearLayout>
    <LinearLayout
        android:id="@+id/sdate_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="10dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/price_container">

        <TextView
            style="@style/SmallBoldAppColorText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:gravity="center_vertical"
            android:text="@string/start_date_title" />

        <TextView
            android:id="@+id/start_date"
            style="@style/SmallRegularDarkText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            tools:text="1/1/2121" />
    </LinearLayout>
    <LinearLayout
        android:id="@+id/edate_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="10dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sdate_container">
        <TextView
            style="@style/SmallBoldAppColorText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:gravity="center_vertical"
            android:text="@string/end_date_title" />
        <TextView
            android:id="@+id/end_date"
            style="@style/SmallRegularDarkText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            tools:text="1/1/2121" />
    </LinearLayout>


    <LinearLayout
        android:id="@+id/min_qty_cont"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/edate_container">

        <TextView
            style="@style/Medium1RegularDarkText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/min_num" />

        <TextView
            style="@style/Medium1RegularDarkText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="5dp"
            android:text=":"
            tools:ignore="HardcodedText" />

        <TextView
            android:id="@+id/min_qty"
            style="@style/Medium1RegularGreenText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:textColor="@color/app_color"
            tools:text="5" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/max_qty_cont"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/min_qty_cont">

        <TextView
            style="@style/Medium1RegularDarkText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/max_num" />

        <TextView
            style="@style/Medium1RegularDarkText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="5dp"
            android:text=":"
            tools:ignore="HardcodedText" />

        <TextView
            android:id="@+id/max_qty"
            style="@style/Medium1RegularGreenText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:textColor="@color/app_color"
            tools:text="20" />
    </LinearLayout>

<LinearLayout
    android:id="@+id/cont"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="10dp"
    android:orientation="horizontal"
    app:layout_constraintTop_toBottomOf="@id/max_qty_cont">
    <LinearLayout
        android:id="@+id/owner_cont"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">


        <com.google.android.material.card.MaterialCardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            app:cardCornerRadius="50dp">

            <ImageView
                android:id="@+id/owner_image"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:contentDescription="@string/img_cnt_desc"
                android:src="@drawable/portrait_contactus" />
        </com.google.android.material.card.MaterialCardView>

        <TextView
            android:id="@+id/owner_name"
            style="@style/SmallMediumDarkText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="البركة" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/shop_cont"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">


        <com.google.android.material.card.MaterialCardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            app:cardCornerRadius="50dp">

            <ImageView
                android:id="@+id/shop_image"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:src="@drawable/portrait_contactus" />
        </com.google.android.material.card.MaterialCardView>

        <TextView
            android:id="@+id/shop_name"
            style="@style/SmallMediumDarkText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="مؤسسة البركة" />
    </LinearLayout>
</LinearLayout>


    <com.google.android.material.switchmaterial.SwitchMaterial
        android:id="@+id/active_status"
        style="@style/SmallMediumDarkText"
        android:layout_width="wrap_content"
        app:thumbTint="@drawable/selector_switch_thumb"
        app:trackTint="@drawable/selector_switch_track"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:text="@string/activate"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cont"
        app:switchPadding="5dp" />

    <View
        android:id="@+id/divider1"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginVertical="10dp"
        android:background="@color/color_gray_7"
        android:elevation="2dp"
        app:layout_constraintTop_toBottomOf="@id/active_status" />

        <include
            android:id="@+id/edit"
            layout="@layout/btn_progress"
            android:layout_width="match_parent"
            android:baselineAligned="false"
            android:layout_height="45dp"
            android:layout_marginTop="10dp"
            android:layout_weight="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider1"/>


</androidx.constraintlayout.widget.ConstraintLayout>

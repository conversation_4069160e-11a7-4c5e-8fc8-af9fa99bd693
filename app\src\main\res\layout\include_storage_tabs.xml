<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/storage_tab"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:tabBackground="@drawable/selector_home_tab"
        app:tabIndicator="@null"
        app:tabMode="fixed"
        app:tabGravity="fill"
        android:theme="@style/tab_round"
        app:tabTextAppearance="@style/Medium2RegularDarkSearch"
        app:tabRippleColor="@null"
        app:tabSelectedTextColor="@color/app_color"
        app:tabTextColor="@color/light_txt_color1"/>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/storage_tab" />
</androidx.constraintlayout.widget.ConstraintLayout>
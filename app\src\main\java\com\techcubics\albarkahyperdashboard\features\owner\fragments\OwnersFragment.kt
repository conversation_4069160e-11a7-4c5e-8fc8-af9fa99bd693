package com.techcubics.albarkahyperdashboard.features.owner.fragments

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.ArrayAdapter
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.techcubics.albarkahyperdashboard.R
import com.techcubics.albarkahyperdashboard.databinding.FragmentOwnersBinding
import com.techcubics.albarkahyperdashboard.features.owner.adapter.OwnersAdapter
import com.techcubics.albarkahyperdashboard.features.owner.intents.OwnersIntent
import com.techcubics.albarkahyperdashboard.features.owner.states.OwnersViewState
import com.techcubics.albarkahyperdashboard.features.owner.viewmodels.OwnersViewModel
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.listeners.NavigationBarVisibilityListener
import com.techcubics.albarkahyperdashboard.utils.listeners.OnItemClickListener
import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.data.auth.utils.LoginStateEnum
import com.techcubics.data.owner.utils.OwnerItemEnum
import com.techcubics.domain.owner.requests.OwnerFilterRequest
import com.techcubics.domain.storage.models.Owner
import com.techcubics.domain.storage.requests.Sort
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class OwnersFragment : Fragment(), OnItemClickListener {

    private var sort: String = Sort.DESC.value
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private var _binding: FragmentOwnersBinding? = null
    private val binding get() = _binding!!
    private val ownersViewModel by viewModel<OwnersViewModel>()
    private var bottomSheetBehavior: BottomSheetBehavior<ConstraintLayout>? = null
    private lateinit var rvListener: RecyclerView.OnScrollListener
    private var scrollToStart = true
    private val keysList = arrayListOf(
        "filter[all]",
        "filter[name]",
        "filter[shop]"
    )
    private var key = keysList[0]
    private lateinit var searchTypeAdapter: ArrayAdapter<String>
    private var filter: OwnerFilterRequest = OwnerFilterRequest(mapOf(key to ""))
    private lateinit var ownersAdapter: OwnersAdapter
    private lateinit var searchTypeList: ArrayList<String>
    private lateinit var value: String


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentOwnersBinding.inflate(inflater, container, false)
        initViews()
        events()
        observeViews()
        return binding.root
    }

    private fun events() {
        rvListener = object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                val lastVisibleItemPosition: Int =
                    (recyclerView.layoutManager as LinearLayoutManager).findLastVisibleItemPosition()
                println("$lastVisibleItemPosition")
                val totalItemCount: Int = recyclerView.layoutManager?.itemCount ?: 0
                if (lastVisibleItemPosition == totalItemCount - 1) {
                    println("$totalItemCount")
                    if (ownersViewModel.hasMorePagesState.value) {
                        scrollToStart = false
                        setFilterData(false, ownersViewModel.pageState.value)
                    }
                }
            }
        }
        binding.ownersRv.addOnScrollListener(rvListener)
        binding.search.searchProducts.setOnClickListener {
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_EXPANDED
        }

        binding.search.textInputLayout.setEndIconOnClickListener {
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_EXPANDED
        }
        binding.bottomSearch.closeIcon.setOnClickListener {
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
        }
        binding.bottomSearch.search.root.setOnClickListener {
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
            setFilterData(true, 1)
        }
        binding.bottomSearch.clearFilter.root.setOnClickListener {
            binding.bottomSearch.searchType.setText(searchTypeList[0], false)
            binding.bottomSearch.sortCont.check(binding.bottomSearch.desc.id)
            binding.bottomSearch.statusCont.check(binding.bottomSearch.available.id)
            binding.bottomSearch.statusCont.visibility = View.INVISIBLE
            binding.bottomSearch.searchCont.visibility = View.VISIBLE
            binding.bottomSearch.searchText.setText("")
            key = keysList[0]
            value = ""
            filter = OwnerFilterRequest(mapOf(key to value))
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
            binding.search.searchProducts.setText("")
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
            setFilterData(true, 1)
        }

    }

    private fun observeViews() {
        ownersViewModel.popupMenuItemLiveData.observe(viewLifecycleOwner) {ownerItemEnum ->
            if(ownerItemEnum != null){
                if(ownerItemEnum.contains(OwnerItemEnum.UpdateStatus.value)){
                    lifecycleScope.launch { ownersViewModel.ownersIntent.send(OwnersIntent.ChangeOwnerStatus(ownerItemEnum.split(",")[1])) }
                }
            }
        }
        lifecycleScope.launch { ownersViewModel.viewState.collect { collectResponse(it) } }
    }

    private fun collectResponse(ownersViewState: OwnersViewState?) {
        Helper.loadingAnimationVisibility(View.GONE, binding.actionLoadingAnimation.root)
        binding.pagingLoadingImg.visibility = View.GONE
        binding.layout.root.visibility = View.GONE
        binding.pagingLoadingImg.visibility = View.GONE
        when (ownersViewState) {
            is OwnersViewState.Idle -> {}
            is OwnersViewState.Pagination -> {
                binding.pagingLoadingImg.visibility = View.VISIBLE
            }

            is OwnersViewState.OwnersByFilter -> {
                initOwnersRV(ownersViewState.owner)
            }
            is OwnersViewState.OwnerStatus -> {

            }
            is OwnersViewState.Error -> {
                Helper.showErrorDialog(requireContext(), ownersViewState.message)
                binding.ownersRv.visibility = View.GONE
                binding.layout.layout.visibility = View.VISIBLE
                binding.layout.icon.setAnimation(com.techcubics.resources.R.raw.lottie_error)
                binding.layout.tvMessage.text = ownersViewState.message
            }

            is OwnersViewState.ServerError -> {
                Helper.showErrorDialog(requireContext(), ownersViewState.error)
                binding.ownersRv.visibility = View.GONE
                binding.layout.layout.visibility = View.VISIBLE
                binding.layout.icon.setAnimation(com.techcubics.resources.R.raw.lottie_error)
                binding.layout.tvMessage.text = ownersViewState.error
            }

            is OwnersViewState.Loading -> {
                Helper.loadingAnimationVisibility(View.VISIBLE, binding.actionLoadingAnimation.root)
            }

            else -> {}
        }
    }


    private fun initViews() {
        getOwners(1)
        searchTypeList = arrayListOf(
            getString(com.techcubics.resources.R.string.all),
            getString(com.techcubics.resources.R.string.owner_name),
            getString(com.techcubics.resources.R.string.shop_name),
        )
        setFilterData(true, 1)
        setSearchKeyAdapter()
        setOrdersAdapter()
        binding.bottomSearch.search.textView.text =
            getString(com.techcubics.resources.R.string.search)
        binding.bottomSearch.clearFilter.textView.text =
            getString(com.techcubics.resources.R.string.clear_filter)
        bottomSheetBehavior = BottomSheetBehavior.from(binding.bottomSearch.root)
        bottomSheetBehavior?.state = BottomSheetBehavior.STATE_HIDDEN
        binding.search.searchProducts.hint =
            getString(com.techcubics.resources.R.string.search_for_owners)
        binding.search.searchProducts.isClickable = true
        binding.search.searchProducts.isFocusable = false
        binding.bottomSearch.sortCont.visibility = View.GONE
        binding.toolbar.mainToolbar.setNavigationOnClickListener {
            findNavController().popBackStack()
        }
        binding.toolbar.tvTitle.text = getString(com.techcubics.resources.R.string.owners)
        if (sharedPreferencesManager.getLoginState().equals(LoginStateEnum.Other.value)) {
            findNavController().navigate(R.id.toLogin)
        }
    }

    private fun setSearchKeyAdapter() {
        searchTypeAdapter = ArrayAdapter(
            requireContext(),
            R.layout.item_dropdown,
            searchTypeList
        )
        binding.bottomSearch.searchType.setAdapter(searchTypeAdapter)
        binding.bottomSearch.searchType.setOnItemClickListener { _, _, i, l ->
            key = keysList[i]

            binding.bottomSearch.statusCont.visibility = View.INVISIBLE
            binding.bottomSearch.titleStatus.visibility = View.INVISIBLE
            binding.bottomSearch.searchCont.visibility = View.VISIBLE

        }
        key = keysList[0]
        binding.bottomSearch.searchType.setText(searchTypeList[0], false)
    }


    private fun getOwners(page: Int) {
        Log.i("dashboard",filter.toString())
        lifecycleScope.launch {
            ownersViewModel.ownersIntent.send(
                OwnersIntent.OwnersFilter(
                    page = page,
                    filter
                )
            )
        }
    }

    private fun setOrdersAdapter() {
        ownersAdapter =
            OwnersAdapter(requireContext(), arrayListOf(), ownersViewModel.popupMenuItemLiveData)
        binding.ownersRv.adapter = ownersAdapter
    }

    private fun initOwnersRV(owners: MutableList<Owner>) {
        if (owners.isEmpty()) {
            binding.ownersRv.visibility = View.GONE
            binding.layout.layout.visibility = View.VISIBLE
            binding.layout.icon.setAnimation(com.techcubics.resources.R.raw.empty_box_lottie)
        } else {
            ownersAdapter.updateOwners(owners)
            if (scrollToStart) {
                binding.ownersRv.scrollToPosition(0)
            } else {
                scrollToStart = true
            }
            binding.ownersRv.viewTreeObserver.addOnGlobalLayoutListener(object :
                ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    // Remove the listener to avoid multiple callbacks
                    binding.ownersRv.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    Log.d("ptime", "appendProducts: 3- " + System.currentTimeMillis())

                }
            })
        }
    }

    private fun setFilterData(isNewFilter: Boolean, page: Int) {
        if (!isNewFilter) {
            ownersViewModel.filterStates.value?.let { f -> filter = f }
        }
        value = binding.bottomSearch.searchText.text.toString()
        filter = OwnerFilterRequest( mapOf(key to value))
        getOwners(1)
    }


    override fun onStart() {
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.VISIBLE)
        super.onStart()
    }
}
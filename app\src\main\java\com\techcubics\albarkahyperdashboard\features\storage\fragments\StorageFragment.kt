package com.techcubics.albarkahyperdashboard.features.storage.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.tabs.TabLayoutMediator
import com.techcubics.albarkahyperdashboard.databinding.FragmentStorageBinding
import com.techcubics.albarkahyperdashboard.features.storage.fragments.tabs.product.ProductsFragment
import com.techcubics.albarkahyperdashboard.features.storage.fragments.tabs.discount.DiscountsFragment
import com.techcubics.albarkahyperdashboard.features.storage.intents.DiscountIntent
import com.techcubics.albarkahyperdashboard.features.storage.intents.ProductIntent
import com.techcubics.albarkahyperdashboard.features.storage.viewmodels.StorageViewModel
import com.techcubics.albarkahyperdashboard.utils.listeners.FragmentActionsHandler
import com.techcubics.albarkahyperdashboard.utils.listeners.NavigationBarVisibilityListener
import com.techcubics.domain.storage.requests.ProductsFilter
import com.techcubics.domain.storage.requests.Sort
import com.techcubics.resources.R
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel

class StorageFragment : Fragment() ,FragmentActionsHandler{
    private var _binding: FragmentStorageBinding? = null
    private val binding get() = _binding!!
    private lateinit var mTabsViewPagerAdapter: TabsViewPagerAdapter
    private val viewModel by viewModel<StorageViewModel>()
    private lateinit var bottomSheetBehavior:BottomSheetBehavior<View>
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = FragmentStorageBinding.inflate(inflater, container, false)
        initViews()
        events()
//        observeViews()
        return binding.root
    }

    private fun initViews() {
        binding.toolbarFragment.tvTitle.text = getString(R.string.nav_storage)
        binding.toolbarFragment.refresh.visibility = View.VISIBLE
        binding.toolbarFragment.search.visibility = View.VISIBLE
        tabBar()
    }

    private fun events() {
        binding.toolbarFragment.refresh.setOnClickListener {
            onRefresh()
        }
        binding.toolbarFragment.search.setOnClickListener {
            bottomSheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
        }
    }

    private fun observeViews() {
        TODO("Not yet implemented")
    }

    private fun tabBar() {
        mTabsViewPagerAdapter = TabsViewPagerAdapter(childFragmentManager, lifecycle,this)
        binding.tabs.container.adapter = mTabsViewPagerAdapter
        binding.tabs.container.isUserInputEnabled = false
        TabLayoutMediator(binding.tabs.storageTab, binding.tabs.container) { tab, position ->
            tab.text = when (position) {
                0 -> getString(R.string.products)
                1 -> getString(R.string.products_offers)
                else -> throw IllegalArgumentException("Invalid position: $position")
            }
        }.attach()
        val tabs = binding.tabs.storageTab.getChildAt(0) as ViewGroup
        for (i in 0 until tabs.childCount) {
            val tab = tabs.getChildAt(i)
            val layoutParams = tab.layoutParams as LinearLayout.LayoutParams
            layoutParams.setMargins(8, 0, 8, 0)
            tab.layoutParams = layoutParams
            binding.tabs.storageTab.requestLayout()
        }

    }

    override fun onStart() {
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.VISIBLE)
        super.onStart()
    }

    override fun onRefresh() {
        lifecycleScope.launch {
            if (binding.tabs.container.currentItem==0){
                viewModel.productIntent.send(ProductIntent.
                GetProducts(1, ProductsFilter(Sort.DESC.value, mapOf("filter[all]" to "")))
                )
            } else if (binding.tabs.container.currentItem==1){
                viewModel.discountIntent.send(DiscountIntent.
                GetDiscounts(1, ProductsFilter(Sort.DESC.value, mapOf("filter[product]" to ""))))
            }
        }


    }
    override fun <T : View> setBottomSheet(bottomSheetBehavior: BottomSheetBehavior<T>?){
        this.bottomSheetBehavior = bottomSheetBehavior as BottomSheetBehavior<View>
    }

}

class TabsViewPagerAdapter(fm: FragmentManager, lifecycle: Lifecycle,val handler:FragmentActionsHandler) :
    FragmentStateAdapter(fm, lifecycle) {


    override fun getItemCount(): Int {
        return 2
    }

    override fun createFragment(position: Int): Fragment {
        var fragment: Fragment? = null
        when (position) {
            0 -> {
                fragment = ProductsFragment()
            }
            1 -> fragment = DiscountsFragment()
        }

        return fragment!!
    }


}
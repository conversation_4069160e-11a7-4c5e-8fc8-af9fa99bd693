<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:clickable="true"
    android:layout_marginVertical="4dp"
    android:focusable="true"
    app:cardCornerRadius="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/product_image"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:adjustViewBounds="true"
            android:src="@drawable/portrait_placeholder"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/guideline"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/p_code"
            style="@style/ExtraSmallBoldWhiteText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="#80B51139"
            android:visibility="gone"
            android:padding="5dp"
            android:textAlignment="center"
            app:layout_constraintBottom_toBottomOf="@id/product_image"
            app:layout_constraintEnd_toEndOf="@id/product_image"
            app:layout_constraintStart_toStartOf="@id/product_image"
            tools:text="Product#507" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintGuide_percent="0.35"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/p_name"
            style="@style/Medium1BoldDarkText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="10dp"
            android:textAlignment="viewStart"
            app:layout_constraintEnd_toStartOf="@id/price"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/guideline"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="زيتون - 125 مل" />

        <TextView
            android:id="@+id/price"
            style="@style/Medium2BoldGreenText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:layout_marginStart="2dp"
            app:layout_constraintStart_toEndOf="@id/p_name"
            app:layout_constraintBottom_toBottomOf="@id/p_name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/p_name"
            tools:text="15 EGP" />

        <LinearLayout
            android:id="@+id/titles_cont"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:gravity="start"
            android:orientation="vertical"
            android:weightSum="4"
            app:layout_constraintBottom_toBottomOf="@id/items_cont"
            app:layout_constraintStart_toStartOf="@id/p_name"
            app:layout_constraintTop_toTopOf="@id/items_cont">

            <TextView
                style="@style/SmallMediumDarkText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="3dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/cat_name" />

            <TextView
                style="@style/SmallMediumDarkText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="3dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/shop" />

            <TextView
                style="@style/SmallMediumDarkText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/activate" />
            <TextView
                style="@style/SmallMediumDarkText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/wanted" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/items_cont"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:orientation="vertical"
            android:weightSum="4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/titles_cont"
            app:layout_constraintTop_toBottomOf="@id/p_name">

            <LinearLayout
                android:id="@+id/cat_cont"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginBottom="3dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="5dp"
                    app:cardCornerRadius="50dp">

                    <ImageView
                        android:id="@+id/cat_image"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:src="@drawable/portrait_contactus" />
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:id="@+id/category"
                    style="@style/SmallMediumDarkText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="جبن" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/shop_cont"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginBottom="3dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">


                <com.google.android.material.card.MaterialCardView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="5dp"
                    app:cardCornerRadius="50dp">

                    <ImageView
                        android:id="@+id/shop_image"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:src="@drawable/portrait_contactus" />
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:id="@+id/shop_name"
                    style="@style/SmallMediumDarkText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="مؤسسة البركة" />
            </LinearLayout>

            <com.google.android.material.switchmaterial.SwitchMaterial
                android:id="@+id/active_status"
                android:layout_width="wrap_content"
                android:buttonTint="@color/color_59"
                app:thumbTint="@drawable/selector_switch_thumb"
                app:trackTint="@drawable/selector_switch_track"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <com.google.android.material.switchmaterial.SwitchMaterial
                android:id="@+id/wanted_status"
                android:checked="false"
                app:thumbTint="@drawable/selector_switch_thumb"
                app:trackTint="@drawable/selector_switch_track"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="1" />
        </LinearLayout>

        <ImageButton
            android:id="@+id/edit"
            style="@style/ExtraSmallBoldWhiteText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintWidth_min="50dp"
            android:minHeight="40dp"
            android:layout_marginEnd="5dp"
            android:layout_marginBottom="5dp"
            android:contentDescription="@string/img_cnt_desc"
            android:background="@drawable/btn_yellow_ripple"
            android:src="@drawable/ic_edit"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintStart_toEndOf="@id/items_cont"
            app:tint="@color/black" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
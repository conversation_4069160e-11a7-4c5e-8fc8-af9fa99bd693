package com.techcubics.albarkahyperdashboard.features.chats.fragments

import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyperdashboard.R
import com.techcubics.albarkahyperdashboard.databinding.FragmentChatOrderRoomsBinding
import com.techcubics.albarkahyperdashboard.features.chats.adapters.ChatRoomsAdapter
import com.techcubics.albarkahyperdashboard.features.chats.intents.ChatIntent
import com.techcubics.albarkahyperdashboard.features.chats.states.ChatState
import com.techcubics.albarkahyperdashboard.features.chats.viewmodels.ChatViewModel
import com.techcubics.albarkahyperdashboard.features.storage.adapters.DropDownAdapter
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.listeners.NavigationBarVisibilityListener
import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.data.auth.utils.Constants
import com.techcubics.data.auth.utils.UserTypeEnum
import com.techcubics.domain.auth.models.User
import com.techcubics.domain.storage.models.Owner
import com.techcubics.domain.storage.models.Shop
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel


class ChatOrderRoomsFragment : Fragment() {
    private var _binding: FragmentChatOrderRoomsBinding? = null
    private val binding get() = _binding!!
    private val viewModel by viewModel<ChatViewModel>()
    private lateinit var rvScrollListener: RecyclerView.OnScrollListener
    private lateinit var chatRoomAdapter: ChatRoomsAdapter
    private var shopId: Int? = null
    private var ownerId: Int? = null
    private var ownerDropDownAdapter: DropDownAdapter<Owner>? = null
    private var shopDropDownAdapter: DropDownAdapter<Shop>? = null
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        if (_binding==null) {
            _binding = FragmentChatOrderRoomsBinding.inflate(inflater, container, false)
            initViews()
            events()
            observers()
        }
        return binding.root
    }
    override fun onStart() {
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.VISIBLE)
        super.onStart()
    }

    private fun events() {
        rvScrollListener = object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (!recyclerView.canScrollVertically(1)) {
                    getChatRooms()
                }
            }
        }
        binding.rvChatRooms.addOnScrollListener(rvScrollListener)
    }

    private fun observers() {
        lifecycleScope.launch { viewModel.state.collect { collectResponse(it) } }
    }

    private fun collectResponse(state: ChatState) {
        binding.loading.root.visibility = View.GONE
        binding.msgLayout.root.visibility = View.GONE
        binding.pagingLoadingImg.visibility = View.GONE
        binding.rvChatRooms.visibility = View.VISIBLE
        when (state) {
            is ChatState.Idle -> {}
            is ChatState.PageLoading -> binding.pagingLoadingImg.visibility = View.VISIBLE
            is ChatState.ProgressLoading -> binding.loading.root.visibility = View.VISIBLE
            is ChatState.HideRv -> binding.rvChatRooms.visibility = View.GONE
            is ChatState.Error -> {
                if(!state.error!!.contains(Constants.unauthenticated)){
                    Helper.showErrorDialog(requireContext(),state.error?:"")
                }
            }
            is ChatState.ServerError -> setMsgLayout(state.error,com.techcubics.resources.R.raw.lottie_error)
            is ChatState.ViewOrdersChatRooms -> chatRoomAdapter.updateChatRooms(state.chatRooms,ownerId, shopId)
            is ChatState.ViewOwners -> setOwnerDropDown(state.owners)
            is ChatState.ViewShops -> setShopDropDown(state.shops)
            else -> {}
        }
    }

    private fun setMsgLayout(msg: String?, lottieIcon: Int) {
        binding.rvChatRooms.visibility = View.GONE
        binding.msgLayout.root.visibility = View.VISIBLE
        binding.msgLayout.icon.setAnimation(lottieIcon)
        binding.msgLayout.tvMessage.text = msg
    }

    private fun initViews() {
        if (sharedPreferencesManager.getUserType() == UserTypeEnum.Owner.value) {
            binding.ownerCont.visibility = View.GONE
            ownerId = sharedPreferencesManager.getOwnerId()
            getShopsByOwnerId()
        } else {
            getOwners()
        }
        binding.toolbarTitle.tvTitle.text = getString(com.techcubics.resources.R.string.order_chat)
        setChatRoomAdapter()
    }
    private fun setOwnerDropDown(owners: ArrayList<Owner>?) {
        if (owners.isNullOrEmpty()) {
            owners?.add(0, Owner(user = User(name = " ")))
        }
        ownerDropDownAdapter = DropDownAdapter(requireContext(), R.layout.item_dropdown, owners!!)
        binding.ownerList.setAdapter(ownerDropDownAdapter)
        binding.ownerList.setOnItemClickListener { _, _, i, _ ->
            val width: Int =
                binding.ownerList.measuredWidth - (binding.ownerList.paddingLeft + binding.ownerList.paddingRight)

            val truncatedText = TextUtils.ellipsize(
                owners[i].user?.name,
                binding.shopList.paint,
                width.toFloat(),
                TextUtils.TruncateAt.END
            ).toString()
            if (truncatedText.isNotEmpty()) {
                binding.ownerList.setText(truncatedText,false)
            }
//            binding.ownerList.setText(owners[i].user?.name, false)
            ownerId = owners[i].id
            getShopsByOwnerId()
        }

    }

    private fun setShopDropDown(shops: ArrayList<Shop>?) {
        if (shops.isNullOrEmpty()) {
            shops?.add(0, Shop(name = " "))
        }
        shopDropDownAdapter = DropDownAdapter(requireContext(), R.layout.item_dropdown, shops!!)
        binding.shopList.setAdapter(shopDropDownAdapter)
        binding.shopList.setOnItemClickListener { _, _, i, _ ->
            val width: Int =
                binding.shopList.measuredWidth - (binding.shopList.paddingLeft + binding.shopList.paddingRight)

            val truncatedText = TextUtils.ellipsize(
                shops[i].name,
                binding.shopList.paint,
                width.toFloat(),
                TextUtils.TruncateAt.END
            ).toString()
            if (truncatedText.isNotEmpty()) {
                binding.shopList.setText(truncatedText,false)
            }
//            binding.shopList.setText(shops[i].name, false)
            shopId = shops[i].id
            getChatRooms()
        }

    }
    private fun getShopsByOwnerId() {
        lifecycleScope.launch {
            ownerId?.let { viewModel.chatIntent.send(ChatIntent.GetShopsByOwnerId(it)) }
        }
    }

    private fun getOwners() {
        lifecycleScope.launch {
            viewModel.chatIntent.send(ChatIntent.GetOwners)
        }
    }

    override fun onResume() {
        super.onResume()
        if (ownerId!=null&&shopId!=null) {
            getChatRooms()
        }
    }
    private fun setChatRoomAdapter() {
        chatRoomAdapter = ChatRoomsAdapter(requireContext(), arrayListOf(),ownerId,shopId)
        binding.rvChatRooms.adapter = chatRoomAdapter
        binding.rvChatRooms.layoutManager = LinearLayoutManager(requireContext())
    }

    private fun getChatRooms() {
        lifecycleScope.launch {
            shopId?.let {
                viewModel.chatIntent.send(
                    ChatIntent.GetOrderChatRoomsIntent(
                        viewModel.pageState.value, ownerId!!,
                        it
                    )
                )
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        binding.rvChatRooms.removeOnScrollListener(rvScrollListener)
    }
}
package com.techcubics.albarkahyperdashboard.features.reports.viewmodels

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.techcubics.albarkahyperdashboard.features.reports.intents.ReportsIntent
import com.techcubics.albarkahyperdashboard.features.reports.states.ReportsViewState
import com.techcubics.domain.common.Constants
import com.techcubics.domain.owner.usecases.GetOwnersInMoreUseCase
import com.techcubics.domain.reports.request.ReportRequest
import com.techcubics.domain.reports.usecases.GetReportsUseCase
import com.techcubics.domain.storage.usecases.GetShopsByOwnerIdUseCase
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.consumeAsFlow
import kotlinx.coroutines.launch


class ReportsViewModel(
    private val getOwnersInMoreUseCase: GetOwnersInMoreUseCase,
    private val getShopsByOwnerIdUseCase: GetShopsByOwnerIdUseCase,
    private val getReportsUseCase: GetReportsUseCase
) : ViewModel() {


    private val _viewState = MutableStateFlow<ReportsViewState?>(ReportsViewState.Idle)
    val viewState: MutableStateFlow<ReportsViewState?> get() = _viewState

    val reportsIntent = Channel<ReportsIntent>(Channel.UNLIMITED)
    var popupMenuItemLiveData: MutableLiveData<String?> = MutableLiveData()

    private val _hasMorePagesState = MutableStateFlow(false)
    val hasMorePagesState: StateFlow<Boolean> get() = _hasMorePagesState

    private val _pageState = MutableStateFlow(1)
    val pageState: StateFlow<Int> get() = _pageState




    init {
        handleIntent()
    }


    private fun handleIntent() {
        viewModelScope.launch {
            reportsIntent.consumeAsFlow().collect {
                when (it) {
                    is ReportsIntent.GetOwners -> getOwners()
                    is ReportsIntent.GetShopsByOwnerId -> getShopsByOwnerID(it.id)
                    is ReportsIntent.GetReports -> getReports(it.req)
                    else -> {}
                }
            }
        }
    }

    private fun getReports(req: ReportRequest) {
        _viewState.value = ReportsViewState.Loading
        viewModelScope.launch {
            val result = getReportsUseCase.invoke(req)
            if (result?.message?.contains(Constants.SERVER_ERROR) == true) {
                ReportsViewState.ServerError(result.message!!)
            }
            else if(result?.message?.contains(Constants.Unauthenticated) == true) {
                ReportsViewState.Error(result.message!!)
            }
            else if (result?.status == true) {
                _viewState.value = ReportsViewState.GetReports(result.data!!)
            }else{
                ReportsViewState.Error(result?.message ?: "error")
            }
        }
    }

    private fun getShopsByOwnerID(id: Int) {
        _viewState.value = ReportsViewState.Loading
        viewModelScope.launch {
            val result = getShopsByOwnerIdUseCase.invoke(id)
            if (result?.message?.contains(Constants.SERVER_ERROR) == true) {
                ReportsViewState.ServerError(result.message!!)
            }
            else if(result?.message?.contains(Constants.Unauthenticated) == true) {
                ReportsViewState.Error(result.message!!)
            }
            else if (result?.status == true) {
                _viewState.value = ReportsViewState.GetShopsByOwnerId(result.data!!)
            }else{
                ReportsViewState.Error(result?.message ?: "error")
            }
        }
    }

    private fun getOwners() {
        _viewState.value = ReportsViewState.Loading
        viewModelScope.launch {
            val result = getOwnersInMoreUseCase.invoke()
            if (result?.message?.contains(Constants.SERVER_ERROR) == true) {
                ReportsViewState.ServerError(result.message!!)
            }
            else if(result?.message?.contains(Constants.Unauthenticated) == true) {
                ReportsViewState.Error(result.message!!)
            }
            else if (result?.status == true) {
                _viewState.value = ReportsViewState.GetOwners(result.data!!)
            }else{
                ReportsViewState.Error(result?.message ?: "error")
            }
        }
    }


}
package com.techcubics.albarkahyperdashboard.features.owner.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyperdashboard.databinding.ItemOwnerBinding
import com.techcubics.data.owner.utils.OwnerItemEnum
import com.techcubics.domain.storage.models.Owner
import it.beppi.tristatetogglebutton_library.TriStateToggleButton


class OwnersAdapter(
    private val context: Context,
    private var listOfOwners: List<Owner>,
    private val popupMenuItemLiveData: MutableLiveData<String?>
) : RecyclerView.Adapter<OwnersHolderItem>() {


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OwnersHolderItem {

        val itemBinding =
            ItemOwnerBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return OwnersHolderItem(itemBinding)
    }


    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: OwnersHolderItem, @SuppressLint("RecyclerView") position: Int) {
        holder.ownerName.text = listOfOwners[position].user?.name
        holder.shopName.text = listOfOwners[position].brandName
        holder.address.text = listOfOwners[position].address
        holder.count.text = listOfOwners[position].countShops.toString()
        holder.id.text = "Owner#${listOfOwners[position].ownerId.toString()}"

        holder.toggle.setOnCheckedChangeListener { compoundButton, b ->
            popupMenuItemLiveData.postValue(OwnerItemEnum.UpdateStatus.value+","+listOfOwners[position].ownerId)
        }
        holder.edit.setOnClickListener {
            popupMenuItemLiveData.postValue(OwnerItemEnum.Edit.value)
        }
        holder.password.setOnClickListener {
            popupMenuItemLiveData.postValue(OwnerItemEnum.Password.value)
        }

    }


    override fun getItemCount(): Int {
        return listOfOwners.size
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateOwners(owners:List<Owner>){
        notifyDataSetChanged()
        this.listOfOwners = owners

    }
}

class OwnersHolderItem(itemView: ItemOwnerBinding) : RecyclerView.ViewHolder(itemView.root) {
        val ownerName = itemView.ownerName
        val shopName = itemView.shopName
        val address = itemView.address
        val count = itemView.branchesCount
        val id = itemView.ownerId
        val toggle = itemView.toggle
        val edit = itemView.edit
        val password = itemView.password
}

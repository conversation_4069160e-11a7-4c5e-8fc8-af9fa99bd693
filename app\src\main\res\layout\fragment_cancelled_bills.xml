<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="8dp"
    android:background="@color/color_gray_3"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".features.bills.fragments.BillsStatusFragment">
    <include
        android:id="@+id/search"
        layout="@layout/include_search_layout"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="10dp"
        android:layout_height="wrap_content"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/ordersRv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        tools:itemCount="3"
        android:background="@color/color_gray_3"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        tools:listitem="@layout/item_order"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/search"/>

    <include
        android:id="@+id/action_loading_animation"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/layout"
        layout="@layout/include_placeholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"

        />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/pagingLoadingImg"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_gravity="bottom"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_rawRes="@raw/lottie_pager_loading" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:elevation="16dp">

        <include
            android:id="@+id/bottom_search"
            layout="@layout/include_products_filter" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
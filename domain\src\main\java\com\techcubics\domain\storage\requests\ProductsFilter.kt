package com.techcubics.domain.storage.requests

import com.google.gson.annotations.SerializedName

data class ProductsFilter(
    @SerializedName("filter[sort]") var sort: String=Sort.DESC.value,
    var search: Map<String,String>,
    val isOwner:Int?=null,
    @SerializedName("paginator") var paginator: Int=Paginator.ENABLED.value
)

data class ProductsFilterRequestBody(
    var sort: String,
    var search: Map<String, String>,
    var paginator: Int,
    val isOwner:Int?=null
)

enum class Paginator(val value: Int) {
    ENABLED(1),
    DISABLED(0)
}

enum class Sort(val value: String) {
    ASC("ASC"),
    DESC("DESC")
}
enum class Status(val value: Int?) {
    Available(1),
    Unavailable(0)
}
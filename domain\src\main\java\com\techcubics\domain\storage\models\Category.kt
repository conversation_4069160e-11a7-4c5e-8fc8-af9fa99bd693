package com.techcubics.domain.storage.models

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class Category (

    @SerializedName("id"          ) var id         : Int?    = null,
    @SerializedName("category_id" ) var categoryId : Int?    = null,
    @SerializedName("image"       ) var image      : String? = null,
    @SerializedName("name"        ) var name       : String? = null,
    @SerializedName("slug"        ) var slug       : String? = null,
    @SerializedName("count"       ) var count      : Int? = null

): Parcelable
<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="16dp"
    android:elevation="8dp"
    app:strokeWidth="1dp"
    app:cardPreventCornerOverlap="true"
    app:cardUseCompatPadding="true">
    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:padding="15dp"
        android:layout_height="wrap_content">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:gravity="start"
                android:textAlignment="viewStart"
                android:id="@+id/owner_name"
                android:layout_weight="1"
                android:layout_gravity="center_vertical"
                android:textColor="@color/app_color"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:textAppearance="@style/Medium2BoldDarkText"
                tools:text="محممدددددددد"/>
            <LinearLayout
                android:layout_width="0dp"
                android:textAlignment="viewEnd"
                android:gravity="end"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/toggle"
                    android:layout_width="60dp"
                    android:layout_height="40dp"
                    android:textColor="@color/app_color"
                    android:layout_marginEnd="5dp"
                     />

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/edit"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="5dp"
                    android:backgroundTint="@color/app_color">
                    <ImageView
                        android:layout_width="15dp"
                        android:layout_height="15dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_edit"
                        app:tint="@color/white"/>
                </com.google.android.material.card.MaterialCardView>
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/password"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="5dp"
                    android:backgroundTint="@color/app_color">
                    <ImageView
                        android:layout_width="15dp"
                        android:layout_height="15dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_password"
                        app:tint="@color/white" />
                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>

        </LinearLayout>
        <TextView
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@drawable/divider"
            android:layout_marginTop="10dp"/>
        <TextView
            android:gravity="start"
            android:textAlignment="viewStart"
            android:id="@+id/address"
            android:layout_marginTop="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@style/Medium2RegularDarkSearch"
            tools:text="محممددددد"/>
        <TextView
            android:gravity="start"
            android:textAlignment="viewStart"
            android:id="@+id/owner_id"
            android:layout_marginTop="5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@style/Medium2RegularDarkSearch"
            tools:text="محممددددد"/>
        <LinearLayout
            android:layout_marginTop="5dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:weightSum="2">
            <TextView
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:textAppearance="@style/Medium2RegularDarkSearch"
                android:text="@string/shop_name"/>
            <TextView
                android:id="@+id/shop_name"
                android:layout_width="0dp"
                android:textAlignment="viewStart"
                android:gravity="start"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:textAppearance="@style/Medium2BoldDarkText"
                tools:text="unpaid"/>
        </LinearLayout>
        <LinearLayout
            android:layout_marginTop="5dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:weightSum="2">
            <TextView
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:textAppearance="@style/Medium2RegularDarkSearch"
                android:text="@string/gallery_branches_count"/>
            <TextView
                android:id="@+id/branches_count"
                android:layout_width="0dp"
                android:textAlignment="viewStart"
                android:gravity="start"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:textAppearance="@style/Medium2BoldDarkText"
                tools:text="unpaid"/>
        </LinearLayout>

    </LinearLayout>
</com.google.android.material.card.MaterialCardView>
package com.techcubics.albarkahyperdashboard.features.chats.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.techcubics.albarkahyperdashboard.features.chats.intents.ChatIntent
import com.techcubics.albarkahyperdashboard.features.chats.states.ChatState
import com.techcubics.domain.chats.models.OrderChat
import com.techcubics.domain.chats.models.OrderChatRoom
import com.techcubics.domain.chats.models.SupportChat
import com.techcubics.domain.chats.models.SupportChatRoom
import com.techcubics.domain.chats.request.SendMessageRequest
import com.techcubics.domain.chats.usecases.*
import com.techcubics.domain.common.Constants
import com.techcubics.domain.storage.usecases.GetOwnersUseCase
import com.techcubics.domain.storage.usecases.GetShopsByOwnerIdUseCase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.consumeAsFlow
import kotlinx.coroutines.launch

class ChatViewModel(
    private val getOrdersChatRoomsUseCase: GetOrdersChatRoomsUseCase,
    private val getOrderChatsUseCase: GetOrderChatsUseCase,
    private val sendOrderChatMessageUseCase: SendOrderChatMessageUseCase,
    private val getSupportChatRoomsUseCase: GetSupportChatRoomsUseCase,
    private val getSupportChatsUseCase: GetSupportChatsUseCase,
    private val sendSupportChatMessageUseCase: SendSupportChatMessageUseCase,
    private val getOwnersUseCase: GetOwnersUseCase,
    private val getShopsByOwnerIdUseCase: GetShopsByOwnerIdUseCase,
) : ViewModel() {
    val chatIntent = Channel<ChatIntent>(Channel.UNLIMITED)
    private val _state = MutableStateFlow<ChatState>(ChatState.Idle)
    val state: StateFlow<ChatState> get() = _state

    private val _pageState = MutableStateFlow(1)
    val pageState: StateFlow<Int> get() = _pageState

    private val _hasMorePagesState = MutableStateFlow(true)
    private val hasMorePagesState: StateFlow<Boolean> get() = _hasMorePagesState

    private val orderChats = arrayListOf<OrderChatRoom>()
    private val supportChats = arrayListOf<SupportChatRoom>()

    init {
        handleIntent()
    }

    private fun handleIntent() {
        viewModelScope.launch {
            chatIntent.consumeAsFlow().collect {
                when (it) {
                    is ChatIntent.GetOrderChatRoomsIntent -> getOrderChatRooms(it.page,it.ownerId,it.shopId)
                    is ChatIntent.GetOrderChatsIntent -> getOrderChats(
                        it.id,it.ownerId,it.shopId, it.showProgress
                    )

                    is ChatIntent.SendOrderChatMessageIntent -> sendOrderChatMessages(
                        it.request, it.ownerId,it.shopId
                    )

                    is ChatIntent.GetSupportChatRoomsIntent ->  getSupportChatRooms(it.page)
                    is ChatIntent.GetSupportChatsIntent -> getSupportChats(it.id,it.showProgress)
                    is ChatIntent.SendSupportChatMessageIntent -> sendSupportChatMessages(it.request)
                    ChatIntent.GetOwners -> getOwners()
                    is ChatIntent.GetShopsByOwnerId -> getShopsByOwnerId(it.ownerId)
                }
            }
        }
    }

    private fun getOrderChatRooms(page: Int,ownerId:Int,shopId:Int) {
        if (hasMorePagesState.value) {
            if (page == 1) {
                orderChats.clear()
                _state.value = ChatState.HideRv
                _state.value = ChatState.ProgressLoading
            } else {
                _state.value = ChatState.PageLoading
            }
        } else {
            orderChats.clear()
            _state.value = ChatState.Idle
        }
        viewModelScope.launch {
            _state.value = try {
                val result = getOrdersChatRoomsUseCase.invoke(page,ownerId, shopId)
                if (result?.message?.contains(Constants.SERVER_ERROR) == true) {
                    ChatState.ServerError(result.message)
                } else if (result?.data != null) {
                    _hasMorePagesState.value = result.pagingator?.hasMorePages ?: false
                    if (result.pagingator?.hasMorePages == true) {
                        _pageState.value =
                            result.pagingator?.currentPage?.plus(1) ?: pageState.value
                    }
                    appendOrderChatRoomList(result.data!!)
                } else {
                    ChatState.Error(result?.message)
                }
            } catch (e: Exception) {
                ChatState.Error(e.localizedMessage)
            }
        }
    }

    private fun getOrderChats(id: Int ,ownerId:Int,shopId:Int,showProgress: Boolean) {
        if (showProgress) {
            _state.value = ChatState.ProgressLoading
        }
        viewModelScope.launch {
            _state.value = try {
                val result = getOrderChatsUseCase.invoke(id,ownerId, shopId)
                if (result?.message?.contains(Constants.SERVER_ERROR) == true) {
                    ChatState.ServerError(result.message)
                } else if (result?.data != null) {
                    ChatState.ViewOrderChats(result.data!!)
                } else {
                    ChatState.Error(result?.message)
                }
            } catch (e: Exception) {
                ChatState.Error(e.localizedMessage)
            }
            refresh(id,ownerId,shopId)
        }
    }

    private fun refresh(id: Int, ownerId: Int, shopId: Int) {
        viewModelScope.launch(Dispatchers.Default) {
            delay(2000)
            getOrderChats(id, ownerId, shopId, false)
        }
    }

    private fun sendOrderChatMessages(request: SendMessageRequest, ownerId: Int, shopId: Int) {
        viewModelScope.launch {
            _state.value = try {
                val result = sendOrderChatMessageUseCase.invoke(request,ownerId, shopId)
                if (result?.message?.contains(Constants.SERVER_ERROR) == true) {
                    ChatState.ServerError(result.message)
                } else if (result?.data != null) {
                    ChatState.SendOrderChatMessageResponse(result.data!!)
                } else {
                    ChatState.Error(result?.message)
                }
            } catch (e: Exception) {
                ChatState.Error(e.localizedMessage)
            }
        }
    }

    private fun appendOrderChatRoomList(data: ArrayList<OrderChatRoom>): ChatState.ViewOrdersChatRooms {
        if (!orderChats.containsAll(data)) {
            orderChats.addAll(data)
        }
        return ChatState.ViewOrdersChatRooms(orderChats)
    }
    
    ////////////////////////////////

    private fun getSupportChatRooms(page: Int) {
        if (hasMorePagesState.value) {
            if (page == 1) {
                orderChats.clear()
                _state.value = ChatState.HideRv
                _state.value = ChatState.ProgressLoading
            } else {
                _state.value = ChatState.PageLoading
            }
        } else {
            orderChats.clear()
            _state.value = ChatState.Idle
        }
        viewModelScope.launch {
            _state.value = try {
                val result = getSupportChatRoomsUseCase.invoke(page)
                if (result?.message?.contains(Constants.SERVER_ERROR) == true) {
                    ChatState.ServerError(result.message)
                } else if (result?.data != null) {
                    _hasMorePagesState.value = result.pagingator?.hasMorePages ?: false
                    if (result.pagingator?.hasMorePages == true) {
                        _pageState.value =
                            result.pagingator?.currentPage?.plus(1) ?: pageState.value
                    }
                    appendSupportChatRoomList(result.data!!)
                } else {
                    ChatState.Error(result?.message)
                }
            } catch (e: Exception) {
                ChatState.Error(e.localizedMessage)
            }
        }
    }

    private fun getSupportChats(id: Int ,showProgress: Boolean) {
        if (showProgress) {
            _state.value = ChatState.ProgressLoading
        }
        viewModelScope.launch {
            _state.value = try {
                val result = getSupportChatsUseCase.invoke(id)
                if (result?.message?.contains(Constants.SERVER_ERROR) == true) {
                    ChatState.ServerError(result.message)
                } else if (result?.data != null) {
                    ChatState.ViewSupportChats(result.data!!)
                } else {
                    ChatState.Error(result?.message)
                }
            } catch (e: Exception) {
                ChatState.Error(e.localizedMessage)
            }
            refresh(id)
        }
    }

    private fun refresh(id: Int) {
        viewModelScope.launch(Dispatchers.Default) {
            delay(2000)
            getSupportChats(id, false)
        }
    }

    private fun sendSupportChatMessages(request: SendMessageRequest) {
        viewModelScope.launch {
            _state.value = try {
                val result = sendSupportChatMessageUseCase.invoke(request)
                if (result?.message?.contains(Constants.SERVER_ERROR) == true) {
                    ChatState.ServerError(result.message)
                } else if (result?.data != null) {
                    ChatState.SendSupportChatMessageResponse(result.data!!)
                } else {
                    ChatState.Error(result?.message)
                }
            } catch (e: Exception) {
                ChatState.Error(e.localizedMessage)
            }
        }
    }

    private fun appendSupportChatRoomList(data: ArrayList<SupportChatRoom>): ChatState.ViewSupportChatRooms {
        if (!supportChats.containsAll(data)) {
            supportChats.addAll(data)
        }
        return ChatState.ViewSupportChatRooms(supportChats)
    }


    private fun getShopsByOwnerId(ownerId: Int) {
        _state.value = ChatState.ProgressLoading
        viewModelScope.launch {
            val result = getShopsByOwnerIdUseCase.invoke(ownerId)
            _state.value = try {
                if (result?.data != null) {
                    ChatState.ViewShops(result.data)
                } else {
                    ChatState.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                ChatState.Error(e.localizedMessage)
            }
        }
    }

    private fun getOwners() {
        _state.value = ChatState.ProgressLoading
        viewModelScope.launch {
            val result = getOwnersUseCase.invoke()
            _state.value = try {
                if (result?.data != null) {
                    ChatState.ViewOwners(result.data)
                } else {
                    ChatState.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                ChatState.Error(e.localizedMessage)
            }
        }
    }

}
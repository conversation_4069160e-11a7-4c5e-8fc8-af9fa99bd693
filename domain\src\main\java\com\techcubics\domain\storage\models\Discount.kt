package com.techcubics.domain.storage.models

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class Discount (

    @SerializedName("id"                   ) var id                 : Int?     = null,
    @SerializedName("discount_id"          ) var discountId         : Int?     = null,
    @SerializedName("owner"                ) var owner              : Owner?   = Owner(),
    @SerializedName("shop"                 ) var shop               : Shop?    = Shop(),
    @SerializedName("start"                ) var start              : String?  = null,
    @SerializedName("end"                  ) var end                : String?  = null,
    @SerializedName("start_format"         ) var startFormat        : String?  = null,
    @SerializedName("end_format"           ) var endFormat          : String?  = null,
    @SerializedName("status"               ) var status             : Int?     = null,
    @SerializedName("price_before"         ) var priceBefore        : Float?     = null,
    @SerializedName("percent"              ) var percent            : Float?  = null,
    @SerializedName("price_after"          ) var priceAfter         : Float?  = null,
    @SerializedName("product"              ) var product            : Product? = Product(),
    @SerializedName("maximum_order_number" ) var maximumOrderNumber : Int?     = null,
    @SerializedName("minimum_order_number" ) var minimumOrderNumber : Int?     = null,
    @SerializedName("images"               ) var images             : ArrayList<Image>?        = null
):Parcelable
package com.techcubics.albarkahyperdashboard.utils.firebase_cloud_messaging

import android.util.Log
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage

class FcmService : FirebaseMessagingService() {

    companion object {
        private const val TAG = "MyFirebaseMessagingServ"
    }

    private var body: String = ""

    override fun onMessageReceived(remoteMessage: RemoteMessage) {

        remoteMessage.data.let {
            Log.d(TAG, "onMessageReceivedData: $it")
            val title: String = remoteMessage.data["title"].toString()
            body = remoteMessage.data["body"].toString()

            val info = remoteMessage.data.getValue("info")
//            val convertedInfo = createInfoFromJson(info)

//            NotificationHelper.show(this, NotificationObject(title, body, convertedInfo))
        }

    }

//    private fun createInfoFromJson(infoJson: String): Info {
//        val intent = Intent("com.google.firebase.messaging.DATA_MESSAGE")
//        val json = JSONObject(infoJson)
//        val info= when (json.getString("notification_type")) {
//            NotificationTypes.General.value -> Info.GeneralInfo
//            NotificationTypes.Course.value -> {
//                val courseId = json.getInt("course_id")
//                val teacherId = json.getInt("teacher_id")
//                Info.CourseInfo(courseId, teacherId)
//            }
//            NotificationTypes.Coupon.value -> {
//                val courseId = json.getInt("course_id")
//                val teacherId = json.getInt("teacher_id")
//                val code = json.getString("code")
//                body = body + "\n" + code
//                Info.CouponInfo(courseId, teacherId, code)
//            }
//            NotificationTypes.GeneralCoupon.value -> {
//                val code = json.getString("code")
//                body = body + "\n" + code
//                Info.GeneralCouponInfo(code)
//            }
//            NotificationTypes.LiveChat.value -> {
//                val chatId = json.getInt("chat_id")
//                intent.putExtra(Constants.CHAT_ID, chatId)
//                Info.ChatInfo(chatId)
//            }
//            else -> Info.GeneralInfo
//        }
//        sendBroadcast(intent)
//        return info
//    }

}



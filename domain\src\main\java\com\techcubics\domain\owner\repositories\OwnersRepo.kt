package com.techcubics.domain.owner.repositories

import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.owner.requests.OwnerFilterRequest
import com.techcubics.domain.owner.requests.OwnerFilterRequestBody
import com.techcubics.domain.owner.requests.OwnerRequest
import com.techcubics.domain.owner.requests.UpdateOwnerPasswordRequest
import com.techcubics.domain.storage.models.Owner

interface OwnersRepo {

    suspend fun getOwners(page : Int? = null): BaseResponse<MutableList<Owner>>?

    suspend fun ownersByFilter(page: Int, filter: OwnerFilterRequestBody): BaseResponse<MutableList<Owner>>?

    suspend fun createOwner(request : OwnerRequest): BaseResponse<Nothing>?

    suspend fun ownerDetails(id: String): BaseResponse<Owner>?

    suspend fun updateOwnerStatus(id: String): BaseResponse<Nothing>?

    suspend fun updateOwner(id: String,request: OwnerRequest): BaseResponse<Nothing>?

    suspend fun updateOwnerPassword(request : UpdateOwnerPasswordRequest): BaseResponse<Nothing>?

}
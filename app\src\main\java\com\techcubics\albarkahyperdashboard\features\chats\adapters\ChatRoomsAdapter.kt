package com.techcubics.albarkahyperdashboard.features.chats.adapters

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView.Adapter
import com.techcubics.albarkahyperdashboard.databinding.ItemChatRoomBinding
import com.techcubics.albarkahyperdashboard.features.chats.adapters.holders.ChatRoomsViewHolder
import com.techcubics.domain.chats.models.ChatResponse

class ChatRoomsAdapter(
    val context: Context,
    var chatRooms: List<ChatResponse>,
    private var ownerId: Int? = null,
    private var shopId: Int? = null,
    private val isSupport: Boolean = false
) : Adapter<ChatRoomsViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChatRoomsViewHolder {
        val binding = ItemChatRoomBinding.inflate(LayoutInflater.from(context), parent, false)
        return ChatRoomsViewHolder(context, binding, ownerId, shopId, isSupport)
    }

    override fun getItemCount() = chatRooms.size

    override fun onBindViewHolder(holder: ChatRoomsViewHolder, position: Int) {
        holder.setData(chatRooms[position])
        holder.update(ownerId,shopId)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateChatRooms(chatRooms: List<ChatResponse>, ownerId: Int? = null, shopId: Int? = null) {
        this.chatRooms = chatRooms
        this.ownerId = ownerId
        this.shopId = shopId
        notifyDataSetChanged()
    }
}

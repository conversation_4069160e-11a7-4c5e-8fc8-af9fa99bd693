package com.techcubics.albarkahyperdashboard.features.more.viewmodels

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.techcubics.albarkahyperdashboard.features.more.intents.MoreIntent
import com.techcubics.albarkahyperdashboard.features.more.states.MoreViewState
import com.techcubics.domain.common.Constants
import com.techcubics.domain.more.requests.AccountSettingRequest
import com.techcubics.domain.more.usecases.AccountSettingUsesCase
import com.techcubics.domain.more.usecases.LanguageUseCase
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.consumeAsFlow
import kotlinx.coroutines.launch


class MoreViewModel(
    private val languageUseCase: LanguageUseCase,
    private val editProfileUseCse: AccountSettingUsesCase
) : ViewModel() {


    private val _viewState = MutableStateFlow<MoreViewState?>(MoreViewState.Idle)
    val viewState: MutableStateFlow<MoreViewState?> get() = _viewState

    val moreIntent = Channel<MoreIntent>(Channel.UNLIMITED)
    var popupMenuItemLiveData: MutableLiveData<String> = MutableLiveData()

    init {
        handleIntent()
    }


    private fun handleIntent() {
        viewModelScope.launch {
            moreIntent.consumeAsFlow().collect {
                when (it) {
                    is MoreIntent.GetLanguages -> getLanguages()
                    is MoreIntent.AccountSetting -> editProfileCall(it.request)
                    else -> {}
                }
            }
        }
    }


    fun getLanguages() {
        _viewState.value = MoreViewState.Loading
        viewModelScope.launch {
            val result = languageUseCase.invoke()
            if (result?.message?.contains(Constants.SERVER_ERROR) == true) {
            } else if (result != null) {
                _viewState.value = result.data?.let { MoreViewState.LanguagesList(it) }
            }
        }
    }


    private fun editProfileCall(request: AccountSettingRequest) {
        _viewState.value = MoreViewState.BtnLoading
        viewModelScope.launch {
            val result = editProfileUseCse.invoke(request)
            if (result?.message?.contains(Constants.SERVER_ERROR) == true) {
            } else if (result?.status == true) {
                _viewState.value = MoreViewState.BtnSuccess
                _viewState.value = result.data?.let { MoreViewState.Profile(it) }
                _viewState.value =
                    result.message?.let { MoreViewState.AccountSettingConfirmMessage(it) }
            } else {
                _viewState.value = result?.message?.let { MoreViewState.Error(it) }

            }
        }
    }


}
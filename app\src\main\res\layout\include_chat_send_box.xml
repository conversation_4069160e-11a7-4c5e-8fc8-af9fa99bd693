<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_top_shadow"
    android:paddingStart="16dp"
    android:paddingTop="16dp"
    android:paddingEnd="8dp"
    android:paddingBottom="8dp">

    <EditText
        android:id="@+id/txtMessage"
        style="@style/SmallRegularDarkText"
        android:layout_width="0dp"
        android:padding="10dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        android:background="@drawable/et_chat_style"
        android:gravity="start|top"
        android:hint="@string/chat_hint"
        android:inputType="textMultiLine|text"
        android:maxLines="6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btnSend"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageButton
        android:id="@+id/btnSend"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@null"
        android:src="@drawable/ic_messenger_send"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
package com.techcubics.albarkahyperdashboard.features.storage.fragments.tabs.product

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.inputmethod.EditorInfo
import android.widget.ArrayAdapter
import android.widget.ScrollView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.widget.doOnTextChanged
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.techcubics.albarkahyperdashboard.databinding.FragmentCodedProductsBinding
import com.techcubics.albarkahyperdashboard.features.storage.adapters.DropDownAdapter
import com.techcubics.albarkahyperdashboard.features.storage.adapters.ProductsAdapter
import com.techcubics.albarkahyperdashboard.features.storage.intents.ProductIntent
import com.techcubics.albarkahyperdashboard.features.storage.states.ProductsStates
import com.techcubics.albarkahyperdashboard.features.storage.viewmodels.StorageViewModel
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.extensions.hideKeyboard
import com.techcubics.albarkahyperdashboard.utils.listeners.NavigationBarVisibilityListener
import com.techcubics.albarkahyperdashboard.utils.listeners.OnItemClickListener
import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.data.auth.utils.Constants
import com.techcubics.data.auth.utils.UserTypeEnum
import com.techcubics.domain.auth.models.User
import com.techcubics.domain.storage.enums.ProductAction
import com.techcubics.domain.storage.models.Owner
import com.techcubics.domain.storage.models.Product
import com.techcubics.domain.storage.models.Shop
import com.techcubics.domain.storage.requests.AddNewProduct
import com.techcubics.domain.storage.requests.ProductsFilter
import com.techcubics.domain.storage.requests.Sort
import com.techcubics.domain.storage.requests.Status
import com.techcubics.resources.R
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.io.File


class CodedProductsFragment : Fragment(), OnItemClickListener {
    private var sort: String = Sort.DESC.value
    private var _binding: FragmentCodedProductsBinding? = null
    private val binding get() = _binding!!
    private val viewModel by viewModel<StorageViewModel>()

    private lateinit var productsAdapter: ProductsAdapter
    private lateinit var rvListener: RecyclerView.OnScrollListener

    private val keysList = arrayListOf(
        "filter[all]",
        "filter[name]",
        "filter[owner]",
        "filter[shop]",
        "filter[category]",
        "filter[sub_category]",
        "filter[status]"
    )
    private var key = "filter[name]"
    private lateinit var value: String

    private lateinit var searchTypeAdapter: ArrayAdapter<String>
    private var filter: ProductsFilter = ProductsFilter(sort, mapOf(key to ""))
    private var bottomSheetBehavior: BottomSheetBehavior<ConstraintLayout>? = null
    private var bottomSheetBehaviorAddProduct: BottomSheetBehavior<ScrollView>? = null
    private var scrollToStart = true
    private lateinit var searchTypeList: ArrayList<String>

    //to create product
    private var ownerId: Int? = null

    private var bitmaps: ArrayList<Bitmap> = arrayListOf()

    private var iconBitmap: Bitmap? = null

    private var files: ArrayList<File>? = null

    private var iconFile: File? = null

    private var ownerDropDownAdapter: DropDownAdapter<Owner>? = null

    private var shopDropDownAdapter: DropDownAdapter<Shop>? = null

    private val addNewProduct = AddNewProduct()
    private var isOwner:Int?=null

    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentCodedProductsBinding.inflate(inflater, container, false)
        initViews()
        events()
        observeViews()
        return binding.root
    }

    private fun initViews() {
        binding.toolbar.tvTitle.text = getString(R.string.add_coded_product)
        binding.bottomSearch.search.textView.text = getString(R.string.search)
        binding.bottomSearch.clearFilter.textView.text = getString(R.string.clear_filter)
        bottomSheetBehavior = BottomSheetBehavior.from(binding.bottomSearch.root)
        bottomSheetBehaviorAddProduct = BottomSheetBehavior.from(binding.bottomAddProduct.root)
        bottomSheetBehavior?.state = BottomSheetBehavior.STATE_HIDDEN
        bottomSheetBehaviorAddProduct?.state = BottomSheetBehavior.STATE_HIDDEN
        binding.search.searchProducts.hint = getString(R.string.products_search)
//        binding.search.searchProducts.isClickable = true
//        binding.search.searchProducts.isFocusable = false
        isOwner = if (sharedPreferencesManager.getUserType() == UserTypeEnum.Owner.value) {
            0
        } else {
            null
        }
        setProductsAdapter()
        getProducts(1)

    }

    override fun onResume() {
        super.onResume()
//        searchTypeList = arrayListOf(
//            getString(R.string.all),
//            getString(R.string.product_name),
//            getString(R.string.owner_name),
//            getString(R.string.shop_name),
//            getString(R.string.category_name),
//            getString(R.string.subcategory_name),
//            getString(R.string.product_status_title)
//        )
//        setSearchKeyAdapter()
    }

    private fun setSearchKeyAdapter() {
        searchTypeAdapter = ArrayAdapter(
            requireContext(),
            com.techcubics.albarkahyperdashboard.R.layout.item_dropdown,
            searchTypeList
        )
        binding.bottomSearch.searchType.setAdapter(searchTypeAdapter)
        binding.bottomSearch.searchType.setOnItemClickListener { _, _, i, l ->
            key = keysList[i]
            if (i == 6) {
                binding.bottomSearch.statusCont.visibility = View.VISIBLE
                binding.bottomSearch.titleStatus.visibility = View.VISIBLE
                binding.bottomSearch.searchCont.visibility = View.INVISIBLE
            } else {
                binding.bottomSearch.statusCont.visibility = View.INVISIBLE
                binding.bottomSearch.titleStatus.visibility = View.INVISIBLE
                binding.bottomSearch.searchCont.visibility = View.VISIBLE
            }
        }
        key = keysList[0]
        binding.bottomSearch.searchType.setText(searchTypeList[0], false)
    }

    private fun setProductsAdapter() {
        productsAdapter = ProductsAdapter(mutableListOf(), this, true)
        binding.rvProducts.adapter = productsAdapter
    }

    private fun getProducts(page: Int) {
        lifecycleScope.launch {
            viewModel.productIntent.send(ProductIntent.GetProducts(page, filter))
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun events() {
        binding.bottomAddProduct.root.setOnTouchListener { _, _ ->
            val view = requireActivity().currentFocus
            view?.hideKeyboard(requireActivity())
            view?.clearFocus()
            false
        }
        binding.bottomSheetBackground.setOnClickListener {
            val view = requireActivity().currentFocus
            view?.hideKeyboard(requireActivity())
            view?.clearFocus()
            bottomSheetBehaviorAddProduct?.state = BottomSheetBehavior.STATE_COLLAPSED
        }
        rvListener = object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                val lastVisibleItemPosition: Int =
                    (recyclerView.layoutManager as LinearLayoutManager).findLastVisibleItemPosition()
                println("$lastVisibleItemPosition")
                val totalItemCount: Int = recyclerView.layoutManager?.itemCount ?: 0
                if (lastVisibleItemPosition == totalItemCount - 1) {
                    println("$totalItemCount")
                    if (viewModel.hasMorePagesState.value) {
                        scrollToStart = false
                        setFilterData(false, viewModel.pageState.value)
                    }
                }
            }
        }
        binding.rvProducts.addOnScrollListener(rvListener)

        binding.bottomSearch.searchText.setOnEditorActionListener { textView, i, _ ->
            when (i) {
                EditorInfo.IME_ACTION_SEARCH -> {
                    textView.clearFocus()
                    textView.hideKeyboard(requireActivity())
                    setFilterData(false, 1)
                    true
                }

                else -> false
            }
        }

//        binding.search.searchProducts.setOnClickListener {
//            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_EXPANDED
//        }
        binding.search.searchProducts.doOnTextChanged { _, _, _, _ ->
            setFilterData(true, 1)
        }

        binding.bottomSearch.closeIcon.setOnClickListener {
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
        }
        binding.bottomSearch.search.root.setOnClickListener {
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
            setFilterData(true, 1)
        }

        bottomSheetBehaviorAddProduct?.addBottomSheetCallback(object :
            BottomSheetBehavior.BottomSheetCallback() {
            override fun onStateChanged(bottomSheet: View, newState: Int) {
                if (newState == BottomSheetBehavior.STATE_EXPANDED) {
                    binding.bottomSheetBackground.visibility = View.VISIBLE
                } else if (newState == BottomSheetBehavior.STATE_COLLAPSED) {
                    binding.bottomSheetBackground.visibility = View.GONE
                }
            }

            override fun onSlide(bottomSheet: View, slideOffset: Float) {
                // Not necessary for this solution
            }
        })

        binding.bottomSearch.clearFilter.root.setOnClickListener {
            binding.bottomSearch.searchType.setText(searchTypeList[0], false)
            binding.bottomSearch.sortCont.check(binding.bottomSearch.desc.id)
            binding.bottomSearch.statusCont.check(binding.bottomSearch.available.id)
            binding.bottomSearch.statusCont.visibility = View.INVISIBLE
            binding.bottomSearch.searchCont.visibility = View.VISIBLE
            binding.bottomSearch.searchText.setText("")
            key = "filter[name]"
            value = ""
            sort = Sort.DESC.value
            filter = ProductsFilter(sort, mapOf(key to value))
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
            setFilterData(true, 1)
        }
        binding.bottomAddProduct.addProduct.root.setOnClickListener {
            binding.bottomAddProduct.addProduct.textView.visibility = View.INVISIBLE
            binding.bottomAddProduct.addProduct.progressBar2.visibility = View.VISIBLE
            setFiles()
            setIcon()
            addNewProduct.images = files
            addNewProduct.icon = iconFile
            setData()
            lifecycleScope.launch {
                viewModel.productIntent.send(
                    ProductIntent.StoreUpdateProduct(
                        addNewProduct,
                        ProductAction.Add
                    )
                )
            }
        }
        binding.toolbar.mainToolbar.setNavigationOnClickListener {
            findNavController().popBackStack()
        }
    }

    private fun setSortData() {
        sort = Sort.DESC.value
        if (binding.bottomSearch.sortCont.checkedRadioButtonId == binding.bottomSearch.asc.id) {
            sort = Sort.ASC.value
        }
    }

    private fun setStatusData() {
        value = when (binding.bottomSearch.statusCont.checkedRadioButtonId) {
            binding.bottomSearch.available.id -> {
                Status.Available.value
            }

            binding.bottomSearch.unavailable.id -> {
                Status.Unavailable.value
            }

            else -> {
                Status.Available.value
            }
        }.toString()
    }

    private fun setFilterData(isNewFilter: Boolean, page: Int) {
        if (!isNewFilter) {
            viewModel.filterStates.value?.let { f -> filter = f }
        }
        setSortData()
//        if (key == keysList[6]) {
//            setStatusData()
//        } else {
        value = binding.search.searchProducts.text.toString()
//        }
        key = "filter[name]"
        filter = ProductsFilter(sort, mapOf(key to value), isOwner)
        getProducts(page)
    }

    private fun observeViews() {
        lifecycleScope.launch {
            viewModel.productState.collect {
                binding.bottomAddProduct.addProduct.textView.visibility = View.VISIBLE
                binding.bottomAddProduct.addProduct.progressBar2.visibility = View.GONE

                binding.loading.root.visibility = View.GONE
                binding.msgLayout.root.visibility = View.GONE
                binding.pagingLoadingImg.visibility = View.GONE
                when (it) {
                    is ProductsStates.Idle -> {}
                    is ProductsStates.Loading -> {
                        binding.loading.root.visibility = View.VISIBLE
                    }

                    is ProductsStates.Pagination -> {
                        binding.pagingLoadingImg.visibility = View.VISIBLE
                    }

                    is ProductsStates.ViewProducts -> {
                        Log.d("ptime", "appendProducts: 2- " + System.currentTimeMillis())
                        renderProducts(it.products)
                    }

                    is ProductsStates.Error -> {
                        if(!it.error.contains(Constants.unauthenticated)){
                            Helper.showErrorDialog(requireContext(), it.error)
                        }
                    }

                    is ProductsStates.ServerError -> {
                        setMsgLayout(it.error, R.raw.lottie_error)
                    }

                    is ProductsStates.ViewProductDetails -> {
                        bottomSheetBehaviorAddProduct?.state = BottomSheetBehavior.STATE_EXPANDED
                        setProduct(it.product)
                    }

                    is ProductsStates.StatusError -> {

                        Helper.showErrorDialog(requireContext(), it.error)
                        val index =
                            productsAdapter.products?.indexOfFirst { p -> p.productId == it.productId }
                        if (index != -1 && index != null) {
                            productsAdapter.notifyItemChanged(index)
                        }
                    }

                    is ProductsStates.ViewOwners -> setOwnerDropDown(it.owners)
                    is ProductsStates.ViewShops -> setShopDropDown(it.shops)
                    ProductsStates.Success -> {
                        Helper.showSuccessDialog(
                            requireContext(),
                            getString(R.string.add_product_success)
                        )
                        bottomSheetBehaviorAddProduct?.state = BottomSheetBehavior.STATE_COLLAPSED
                    }

                    else -> {}
                }
            }
        }
    }

    private fun getOwners() {
        lifecycleScope.launch {
            viewModel.productIntent.send(ProductIntent.GetOwners)
        }
    }

    private fun setProduct(product: Product?) {

        binding.bottomAddProduct.tvTitle.text = product?.name
        binding.bottomAddProduct.category.text = product?.category?.name
        binding.bottomAddProduct.subcategory.text = product?.subCategory?.name
        product?.category?.image?.let {
            Helper.loadImage(
                requireContext(),
                it,
                binding.bottomAddProduct.catImage
            )
        }
        product?.subCategory?.image?.let {
            Helper.loadImage(
                requireContext(),
                it,
                binding.bottomAddProduct.subcatImage
            )
        }
        addNewProduct.video = product?.video
        addNewProduct.categoryId = product?.category?.categoryId
        addNewProduct.subCategoryId = product?.subCategory?.id
        addNewProduct.descriptionAr =
            product?.translations?.find { t -> t.locale == "ar" }?.description
        addNewProduct.descriptionEn =
            product?.translations?.find { t -> t.locale == "en" }?.description
        addNewProduct.nameAr = product?.translations?.find { t -> t.locale == "ar" }?.name
        addNewProduct.nameEn = product?.translations?.find { t -> t.locale == "en" }?.name
        bitmaps = arrayListOf()
        if (sharedPreferencesManager.getUserType() == UserTypeEnum.Owner.value) {
            binding.bottomAddProduct.ownerCont.visibility = View.GONE
            ownerId = sharedPreferencesManager.getOwnerId()
            getShopsByOwnerId()
        } else {
            getOwners()
        }
        binding.bottomAddProduct.addProduct.textView.text = getString(R.string.add_product)
        Glide.with(this)
            .asBitmap()
            .load(product?.icon)
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    iconBitmap = resource
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                }
            })
        product?.images?.forEach {
            Glide.with(this)
                .asBitmap()
                .load(it.path)
                .into(object : CustomTarget<Bitmap>() {
                    override fun onResourceReady(
                        resource: Bitmap,
                        transition: Transition<in Bitmap>?
                    ) {
                        bitmaps.add(resource)
                    }

                    override fun onLoadCleared(placeholder: Drawable?) {
                    }
                })
        }
    }

    private fun setData() {
        addNewProduct.price = binding.bottomAddProduct.price.text.toString().ifEmpty { null }
        addNewProduct.minimumOrderNumber =
            binding.bottomAddProduct.minQty.text.toString().ifEmpty { null }
        addNewProduct.maximumOrderNumber =
            binding.bottomAddProduct.maxQty.text.toString().ifEmpty { null }
    }

    private fun setFiles() {
        files = arrayListOf()
        if (bitmaps.isEmpty()) {
            files = null
        } else {
            for (i in bitmaps) {
                val file = Helper.bitmapToFile(requireContext(), i, "image$i")
                files?.add(file!!)
            }
        }
    }

    private fun setIcon() {
        iconFile = if (iconBitmap == null) {
            null
        } else {
            Helper.bitmapToFile(requireContext(), iconBitmap, "icon")
        }
    }

    private fun setOwnerDropDown(owners: ArrayList<Owner>?) {
        if (owners.isNullOrEmpty() ) {
            owners?.add(0, Owner(user = User(name = " ")))
        }
        ownerDropDownAdapter = DropDownAdapter(
            requireContext(),
            com.techcubics.albarkahyperdashboard.R.layout.item_dropdown,
            owners!!
        )
        binding.bottomAddProduct.ownerList.setAdapter(ownerDropDownAdapter)
        binding.bottomAddProduct.ownerList.setOnItemClickListener { _, _, i, _ ->
            binding.bottomAddProduct.ownerList.setText(owners[i].user?.name, false)
            addNewProduct.ownerId = owners[i].id
            ownerId = owners[i].id
            getShopsByOwnerId()
        }

    }

    private fun setShopDropDown(shops: ArrayList<Shop>?) {
        if (shops.isNullOrEmpty()) {
            shops?.add(0, Shop(name = " "))
        }
        shopDropDownAdapter = DropDownAdapter(
            requireContext(),
            com.techcubics.albarkahyperdashboard.R.layout.item_dropdown,
            shops!!
        )
        binding.bottomAddProduct.shopList.setAdapter(shopDropDownAdapter)
        binding.bottomAddProduct.shopList.setOnItemClickListener { _, _, i, _ ->
            binding.bottomAddProduct.shopList.setText(shops[i].name, false)
            addNewProduct.shopId = shops[i].id
        }

    }

    private fun getShopsByOwnerId() {
        lifecycleScope.launch {
            ownerId?.let { viewModel.productIntent.send(ProductIntent.GetShopsByOwnerId(it)) }
        }
    }

    private fun setMsgLayout(msg: String?, lottieIcon: Int) {
        bottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
        bottomSheetBehaviorAddProduct?.state = BottomSheetBehavior.STATE_COLLAPSED
        binding.rvProducts.visibility = View.GONE
        binding.msgLayout.root.visibility = View.VISIBLE
        binding.msgLayout.icon.setAnimation(lottieIcon)
        binding.msgLayout.tvMessage.text = msg
    }

    private fun renderProducts(products: MutableList<Product>) {
        if (products.isEmpty()) {
            setMsgLayout(getString(R.string.message_empty_list_general), R.raw.lottie_empty)
        } else {
            binding.rvProducts.visibility = View.VISIBLE
            binding.msgLayout.root.visibility = View.GONE
        }
        productsAdapter.updateProducts(products)
        if (scrollToStart) {
            binding.rvProducts.scrollToPosition(0)
        } else {
            scrollToStart = true
        }
        binding.rvProducts.viewTreeObserver.addOnGlobalLayoutListener(object :
            ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                // Remove the listener to avoid multiple callbacks
                binding.rvProducts.viewTreeObserver.removeOnGlobalLayoutListener(this)
                Log.d("ptime", "appendProducts: 3- " + System.currentTimeMillis())

                // The RecyclerView has finished laying out all its items
                // Add your code here
            }
        })
    }

    override fun onClick(d: Int, type: String) {
        binding.search.searchProducts.hideKeyboard(requireActivity())
        lifecycleScope.launch {
            viewModel.productIntent.send(ProductIntent.GetProductDetails(d,isOwner))
        }
    }

    override fun onStart() {
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
        super.onStart()
    }

}
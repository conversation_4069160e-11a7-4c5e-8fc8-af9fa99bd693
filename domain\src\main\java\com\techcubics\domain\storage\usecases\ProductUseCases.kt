package com.techcubics.domain.storage.usecases

import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.orders.requests.DeleteImageRequest
import com.techcubics.domain.storage.enums.ProductAction
import com.techcubics.domain.storage.enums.ProductStatusTypes
import com.techcubics.domain.storage.models.AddColor
import com.techcubics.domain.storage.models.AddSize
import com.techcubics.domain.storage.models.Product
import com.techcubics.domain.storage.repositories.StorageRepo
import com.techcubics.domain.storage.requests.AddNewProduct
import com.techcubics.domain.storage.requests.AddNewProductRequest
import com.techcubics.domain.storage.requests.ProductsFilter
import com.techcubics.domain.storage.requests.ProductsFilterRequestBody
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.File

class GetProductsUseCase(private val storageRepo: StorageRepo) {
    suspend operator fun invoke(page: Int, filter: ProductsFilter): BaseResponse<MutableList<Product>>? {
        val request = ProductsFilterRequestBody(filter.sort,filter.search,filter.paginator,filter.isOwner)
        return storageRepo.getProducts(page, request)
    }

}

class GetProductDetailsUseCase(private val storageRepo: StorageRepo) {
    suspend operator fun invoke(productId: Int,isOwner:Int?=null)= storageRepo.getProductDetails(productId,isOwner)
}

class GetOwnersUseCase(private val storageRepo: StorageRepo) {
    suspend operator fun invoke() = storageRepo.getOwners()
}

class GetShopsByOwnerIdUseCase(private val storageRepo: StorageRepo) {
    suspend operator fun invoke(ownerId: Int) = storageRepo.getShops(ownerId)
}

class GetCategoriesUseCase(private val storageRepo: StorageRepo) {
    suspend operator fun invoke() = storageRepo.getCategories()
}

class GetSubCategoriesUseCase(private val storageRepo: StorageRepo) {
    suspend operator fun invoke(catId: Int) = storageRepo.getSubCategories(catId)
}

class DeleteImagesUseCase(private val storageRepo: StorageRepo) {
    suspend operator fun invoke(request: DeleteImageRequest) = storageRepo.deleteImages(request)
}

class StoreProductUseCase(private val storageRepo: StorageRepo) {
    suspend operator fun invoke(
        domainRequest: AddNewProduct,
        productAction: ProductAction
    ): BaseResponse<Product>? {
        val request = prepareAttributesPart(domainRequest)
        if (!domainRequest.images.isNullOrEmpty()) {
            iterateFiles(domainRequest.images!!, request)
        }
        if (domainRequest.icon != null) {
            request.icon = prepareFilePart("icon", domainRequest.icon!!)
        }
        if (!domainRequest.productSizes.isNullOrEmpty())
            iterateSizes(domainRequest.productSizes!!, request)
        if (!domainRequest.productColors.isNullOrEmpty())
            iterateColors(domainRequest.productColors!!, request)
        return if (productAction == ProductAction.Add) {
            storageRepo.storeProduct(request)
        } else {
            storageRepo.updateProduct(request)
        }
    }

    private fun prepareAttributesPart(domainRequest: AddNewProduct): AddNewProductRequest {
        val request = AddNewProductRequest()
        request.productId = domainRequest.productId
        request.categoryId = domainRequest.categoryId.toString()
            .toRequestBody("text/plain;charset=utf-8".toMediaType())
        request.subCategoryId = domainRequest.subCategoryId.toString()
            .toRequestBody("text/plain;charset=utf-8".toMediaType())
        request.ownerId =
            domainRequest.ownerId.toString().toRequestBody("text/plain;charset=utf-8".toMediaType())
        request.shopId =
            domainRequest.shopId.toString().toRequestBody("text/plain;charset=utf-8".toMediaType())
        request.minimumOrderNumber = domainRequest.minimumOrderNumber.toString()
            .toRequestBody("text/plain;charset=utf-8".toMediaType())
        request.maximumOrderNumber = domainRequest.maximumOrderNumber.toString()
            .toRequestBody("text/plain;charset=utf-8".toMediaType())
        request.nameAr =
            domainRequest.nameAr?.toRequestBody("text/plain;charset=utf-8".toMediaType())
        request.nameEn =
            domainRequest.nameEn?.toRequestBody("text/plain;charset=utf-8".toMediaType())
        request.descriptionAr = domainRequest.descriptionAr
            ?.toRequestBody("text/plain;charset=utf-8".toMediaType())
        request.descriptionEn = domainRequest.descriptionEn
            ?.toRequestBody("text/plain;charset=utf-8".toMediaType())
        request.video =
            domainRequest.video?.toRequestBody("text/plain;charset=utf-8".toMediaType())
        request.price =
            domainRequest.price.toString().toRequestBody("text/plain;charset=utf-8".toMediaType())
        return request
    }

    private fun iterateFiles(files: ArrayList<File>, request: AddNewProductRequest) {
        val listOfImages = ArrayList<MultipartBody.Part>()
        for (i in 0 until files.size) {
            listOfImages.add(prepareFilePart("images[$i]", files[i]))
        }
        request.images = listOfImages
    }

    private fun iterateSizes(sizes: ArrayList<AddSize>, request: AddNewProductRequest) {
        val sizesMap: HashMap<String, String?> = HashMap()

        for ((index, size) in sizes.withIndex()) {
            sizesMap["sizes[${index}][name:ar]"] = size.nameAr
            sizesMap["sizes[${index}][name:en]"] = size.nameEn
            sizesMap["sizes[${index}][price]"] = size.price.toString()
        }
        request.productSizes = sizesMap
    }

    private fun iterateColors(colors: ArrayList<AddColor>, request: AddNewProductRequest) {
        val colorsMap: HashMap<String, String?> = HashMap()

        for ((index, size) in colors.withIndex()) {
            colorsMap["colors[${index}][name:ar]"] = size.nameAr
            colorsMap["colors[${index}][name:en]"] = size.nameEn
            colorsMap["colors[${index}][code]"] = size.code
        }
        request.productColors = colorsMap
    }

    private fun prepareFilePart(partName: String, file: File): MultipartBody.Part {
        val requestFile = file.asRequestBody("image/*".toMediaType())
        return MultipartBody.Part.createFormData(partName, file.name, requestFile)
    }
}

class SetStatusUseCase(private val storageRepo: StorageRepo) {
    suspend operator fun invoke(id: Int, type: ProductStatusTypes): BaseResponse<Product>? {
        return when (type) {
            ProductStatusTypes.Active -> storageRepo.setActiveStatus(id)
            ProductStatusTypes.Wanted -> storageRepo.setWantedStatus(id)
        }

    }
}
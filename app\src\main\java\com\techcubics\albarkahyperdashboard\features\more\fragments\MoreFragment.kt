package com.techcubics.albarkahyperdashboard.features.more.fragments

import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.techcubics.albarkahyperdashboard.R
import com.techcubics.albarkahyperdashboard.databinding.FragmentMoreBinding
import com.techcubics.albarkahyperdashboard.features.auth.intents.AuthIntent
import com.techcubics.albarkahyperdashboard.features.auth.state.AuthViewState
import com.techcubics.albarkahyperdashboard.features.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyperdashboard.features.more.intents.MoreIntent
import com.techcubics.albarkahyperdashboard.features.more.states.MoreViewState
import com.techcubics.albarkahyperdashboard.features.more.viewmodels.MoreViewModel
import com.techcubics.albarkahyperdashboard.utils.components.PopupDialog
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.listeners.NavigationBarVisibilityListener
import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.data.auth.utils.Constants
import com.techcubics.data.auth.utils.LoginStateEnum
import com.techcubics.data.auth.utils.UserTypeEnum
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class MoreFragment : Fragment() {
    private var _binding: FragmentMoreBinding? = null
    private val binding get() = _binding!!
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var popupDialog: PopupDialog
    private var languagesStringList: MutableList<String> = mutableListOf()
    private val moreViewModel by viewModel<MoreViewModel>()
    private val authViewModel by viewModel<AuthViewModel>()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentMoreBinding.inflate(inflater, container, false)
        initViews()
        events()
        observeViews()
        return binding.root
    }


    private fun collectResponseFromProfile(MoreViewState: MoreViewState?) {

        when (MoreViewState) {
            is MoreViewState.LanguagesList -> {
                Helper.loadingAnimationVisibility(View.GONE, binding.actionLoadingAnimation.root)
                languagesStringList.clear()
                for (langCode in MoreViewState.languageList!!) {
                    languagesStringList.add(langCode.code)
                }
                if (languagesStringList.isNotEmpty()) {
                    popupDialog.showLanguageDialog(
                        requireContext(),
                        R.layout.dialog_language,
                        languagesStringList,
                        moreViewModel.popupMenuItemLiveData
                    )
                }
            }

            is MoreViewState.Error -> {
                Helper.loadingAnimationVisibility(View.GONE, binding.actionLoadingAnimation.root)
                Helper.showErrorDialog(requireContext(), MoreViewState.message)
            }

            is MoreViewState.Loading -> {
                Helper.loadingAnimationVisibility(View.VISIBLE, binding.actionLoadingAnimation.root)
            }

            else -> {}
        }
        moreViewModel.viewState.value = null

    }
    private fun observeViews() {
        lifecycleScope.launch { moreViewModel.viewState.collect { collectResponseFromProfile(it) } }

        lifecycleScope.launch {
            authViewModel.viewState.collect {
                when (it) {

                    is AuthViewState.Error -> {
                        Helper.loadingAnimationVisibility(
                            View.GONE,
                            binding.actionLoadingAnimation.root
                        )
                        if(!it.message.contains(Constants.unauthenticated)){
                            Helper.showErrorDialog(requireContext(), it.message)
                        }
                    }

                    is AuthViewState.BtnLoading -> {
                    }

                    is AuthViewState.BtnSuccess -> {

                    }
                    is AuthViewState.UnAutherized -> {
                        findNavController().navigate(R.id.toLogin)
                    }
                    is AuthViewState.Loading -> {
                        Helper.loadingAnimationVisibility(
                            View.VISIBLE,
                            binding.actionLoadingAnimation.root
                        )
                    }

                    is AuthViewState.LogoutMessage -> {
                        Helper.loadingAnimationVisibility(
                            View.GONE,
                            binding.actionLoadingAnimation.root
                        )
                        findNavController().navigate(R.id.toLogin)
                        sharedPreferencesManager.setLoginState(LoginStateEnum.Other.value)
                        sharedPreferencesManager.saveObject(Constants.USER, null)
                        sharedPreferencesManager.saveName(null)
                        sharedPreferencesManager.saveEmail(null)
                        sharedPreferencesManager.saveUserPhoto(null)
                        sharedPreferencesManager.savePhone(null)
                        sharedPreferencesManager.saveToken(null)
                        sharedPreferencesManager.saveUserType(UserTypeEnum.Owner.value)
                        sharedPreferencesManager.saveOwnerId(-1)
                    }


                    else -> {}
                }
            }
        }
    }

    private fun events() {
        binding.signInLayout.setOnClickListener {
            if (binding.signIn.text.equals(getString(com.techcubics.resources.R.string.signin))) {
                findNavController().navigate(R.id.toLogin)
            } else {
                lifecycleScope.launch { authViewModel.authIntent.send(AuthIntent.Logout) }
            }

        }

        binding.accountSetting.setOnClickListener {
            findNavController().navigate(R.id.toAccountSettingFragment)
        }

        binding.language.setOnClickListener {
            lifecycleScope.launch { moreViewModel.moreIntent.send(MoreIntent.GetLanguages) }
        }

        binding.owners.setOnClickListener {
            findNavController().navigate(R.id.toOwnersFragment)
        }

        binding.support.setOnClickListener {
            val action = MoreFragmentDirections.viewChatSupportRoomsFragment()
            findNavController().navigate(action)
        }

        binding.website.setOnClickListener {
            val websiteUrl = com.techcubics.data.common.Constants.TECHCUBIC_WEBSITE_URL
            startActivity(Intent(Intent.ACTION_VIEW).setData(Uri.parse(websiteUrl)))
        }

        binding.facebook.setOnClickListener {
            val websiteUrl = com.techcubics.data.common.Constants.TECHCUBIC_FACEBOOK_URL
            startActivity(Intent(Intent.ACTION_VIEW).setData(Uri.parse(websiteUrl)))
        }

        binding.whatsapp.setOnClickListener {
           openTechcubicsWhatsappChat()
        }
    }

    override fun onStart() {
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.VISIBLE)
        super.onStart()
    }

    private fun openTechcubicsWhatsappChat() {
//        val packageManager = requireContext().packageManager
        val intent = Intent(Intent.ACTION_VIEW)
        intent.setPackage("com.whatsapp")
        intent.data = Uri.parse(com.techcubics.data.common.Constants.TECHCUBIC_WHATSAPP_URL)
        requireContext().startActivity(intent)
//        if (intent.resolveActivity(packageManager) != null) {
//        } else {
//            Toast.makeText(requireContext(), "WhatsApp not installed", Toast.LENGTH_SHORT).show()
//        }
    }


    private fun initViews() {
        popupDialog = PopupDialog(requireContext())
        if (sharedPreferencesManager.getUserType()==UserTypeEnum.Owner.value){
            binding.support.visibility = View.GONE
        }else{
            binding.support.visibility = View.VISIBLE
        }
        initUserData()
    }

    private fun initUserData() {
        if (sharedPreferencesManager.getLoginState().equals(LoginStateEnum.LoggedIn.value)) {
            binding.userName.text = sharedPreferencesManager.getName()
            binding.phone.text = sharedPreferencesManager.getPhone()
            sharedPreferencesManager.getUserPhoto()?.let {
                Helper.loadImage(
                    requireContext(),
                    it,
                    binding.userPhoto
                )
            }
            binding.signIn.text = getString(com.techcubics.resources.R.string.logout)
        } else {
            binding.profileData.visibility = View.GONE
            binding.profileLayout
            binding.signIn.text = getString(com.techcubics.resources.R.string.signin)

        }

    }

}
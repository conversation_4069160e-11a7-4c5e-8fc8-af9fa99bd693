package com.techcubics.data.more.remote

import com.techcubics.data.more.utils.Constants
import com.techcubics.domain.auth.models.LoginResponse
import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.auth.models.User
import com.techcubics.domain.more.models.BannerData
import com.techcubics.domain.more.models.Language
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Response
import retrofit2.http.*

interface EndPoints {

    @Multipart
    @POST(Constants.update_profile)
    suspend fun updateProfile(
        @Path("userType") userType : String,
        @Part("name") name: RequestBody,
        @Part("phone") phone: RequestBody,
        @Part("email") email: RequestBody,
        @Part avatar: MultipartBody.Part?
    ): Response<BaseResponse<LoginResponse>>

    @GET(Constants.languages)
    suspend fun getLanguages(@Path("userType") userType : String):Response<BaseResponse<List<Language>>>

    @GET(Constants.banners)
    suspend fun getBanners(@Path("userType") userType : String): Response<BaseResponse<MutableList<BannerData>>>

    @GET(Constants.banners_home)
    suspend fun getHomeBanner(@Path("userType") userType : String) : Response<BaseResponse<MutableList<BannerData>>>

}
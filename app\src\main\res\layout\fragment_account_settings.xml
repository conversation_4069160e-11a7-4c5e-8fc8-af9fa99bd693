<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/editprofile_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_fragment"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/sc_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="30dp">

                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/profilePic"
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_gravity="bottom|center_horizontal"
                    android:src="@drawable/circle_gray" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:layout_gravity="center"
                    android:backgroundTint="#B4FAFAFA"
                    android:clickable="true"
                    app:cardElevation="0dp"
                    app:cardCornerRadius="50dp"/>

                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/iv_camera"
                    android:layout_width="35dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:src="@drawable/ic_camera" />
            </FrameLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="11dp"
                android:layout_marginEnd="16dp"
                android:gravity="center"
                android:text="@string/upload_profile_pic"
                android:textAppearance="@style/LargeBoldDarkText"
                android:textColor="@color/color_gray_14" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginEnd="24dp"
                    android:textColor="@color/dark_txt_color"
                    android:text="@string/user_name"
                    android:textSize="14sp"
                    android:textAppearance="@style/Medium2RegularDarkSearch" />


                <EditText
                    android:id="@+id/name"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginEnd="24dp"
                    android:background="@drawable/et_style_with_borders"
                    android:padding="3dp"
                    android:textAppearance="@style/Medium2RegularDarkSearch"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginEnd="24dp"
                    android:textColor="@color/dark_txt_color"
                    android:text="@string/email"
                    android:textSize="14sp"
                    android:textAppearance="@style/Medium2RegularDarkSearch" />


                <EditText
                    android:id="@+id/email"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginEnd="24dp"
                    android:background="@drawable/et_style_with_borders"
                    android:padding="3dp"
                    android:textAppearance="@style/Medium2RegularDarkSearch"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="24dp"
                android:layout_marginEnd="24dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/phone_number"
                    android:textColor="@color/dark_txt_color"
                    android:textSize="14sp"
                    android:textAppearance="@style/Medium2RegularDarkSearch" />

                <EditText
                    android:id="@+id/phone_number"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginTop="5dp"
                    android:background="@drawable/et_style_with_borders"
                    android:inputType="number"
                    android:layoutDirection="ltr"
                    android:padding="10dp"
                    android:textAppearance="@style/Medium2RegularDarkSearch"
                    android:textSize="14sp" />
            </LinearLayout>

            <TextView
                android:id="@+id/change_pass"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginEnd="24dp"
                android:layout_marginTop="32dp"
                android:text="@string/change_password"
                android:clickable="true"
                android:textSize="15sp"
                android:background="?attr/selectableItemBackground"
                android:textAppearance="@style/Medium2RegularDarkSearch"
                android:textColor="@color/color_blue_1" />
            <include
                android:id="@+id/sign_btn_progress"
                layout="@layout/btn_progress"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="40dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="55dp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <include
        android:id="@+id/action_loading_animation"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"
        />
</androidx.constraintlayout.widget.ConstraintLayout>

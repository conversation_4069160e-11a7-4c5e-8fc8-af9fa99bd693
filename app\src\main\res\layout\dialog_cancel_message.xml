<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_popup_dialog"
    android:minWidth="300dp"
    android:orientation="vertical"
    android:theme="@style/Theme.TextInputLayout">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ImageView
            android:id="@+id/close_img"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_close"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="8dp"
                android:text="@string/cancel_reason"
                android:textAppearance="@style/LargeBoldDarkText" />
            <EditText
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_margin="5dp"
                android:background="@drawable/et_style_with_grayborders"
                android:layout_marginStart="8dp"
                android:textAppearance="@style/Medium2RegularDarkSearch"
                android:layout_marginEnd="8dp"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_marginTop="20dp"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp">
                <include
                    android:id="@+id/cancel"
                    layout="@layout/btn_progress"
                    android:layout_width="0dp"
                    android:layout_height="45dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp" />
                <include
                    android:id="@+id/close"
                    layout="@layout/btn_progress"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="45dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    />
            </LinearLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</LinearLayout>

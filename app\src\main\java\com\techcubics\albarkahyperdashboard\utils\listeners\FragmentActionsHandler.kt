package com.techcubics.albarkahyperdashboard.utils.listeners

import android.view.View
import com.google.android.material.bottomsheet.BottomSheetBehavior

interface FragmentActionsHandler {
    fun onRefresh( ){}//check on opend fragment
    fun setStatus(status : String = ""){}//check on opend fragment
    //call the corresponding getList
    fun <T : View> setBottomSheet(bottomSheetBehavior: BottomSheetBehavior<T>?)

}
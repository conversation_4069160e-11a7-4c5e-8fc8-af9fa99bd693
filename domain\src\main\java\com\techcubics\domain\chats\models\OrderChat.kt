package com.techcubics.domain.chats.models

import com.google.gson.annotations.SerializedName

data class OrderChat(
    @SerializedName("conversion_id") var conversationId: Int? = null,
    @SerializedName("direction") override var direction: String? = null,
    @SerializedName("message") override var message: String? = null,
    @SerializedName("date") override var date: String? = null,
    @SerializedName("time") override var time: String? = null,
    @SerializedName("format_date"   ) override var formatDate   : String? = null
):Chat

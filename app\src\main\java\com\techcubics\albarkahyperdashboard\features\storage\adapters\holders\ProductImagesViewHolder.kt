package com.techcubics.albarkahyperdashboard.features.storage.adapters.holders

import android.content.Context
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.techcubics.albarkahyperdashboard.databinding.ItemProductImageBinding
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.listeners.OnItemClickListener
import com.techcubics.domain.storage.models.Image

class ProductImagesViewHolder(
    private val context: Context,
    private val binding: ItemProductImageBinding,
    private val listener:OnItemClickListener,
    private val images: MutableList<Image>

) : ViewHolder(binding.root) {

    fun setData(image: Image? = null) {
        image?.path?.let { Helper.loadImage(context, it, binding.productImg) }
        binding.root.setOnClickListener {
//            val action = AddNewProductFragmentDirections.viewImageViewPagerFragment(
//                images.toTypedArray(),
//                bindingAdapterPosition
//            )
//            it.findNavController().navigate(action)
        }
        binding.deleteImg.setOnClickListener {
            if (image?.pathID == -1) {
                listener.onClick(image.id, "new")
            } else if (image?.pathID == image?.id && image?.id!! > 0) {
                listener.onClick(image.id, "old")
            }
        }
    }
}
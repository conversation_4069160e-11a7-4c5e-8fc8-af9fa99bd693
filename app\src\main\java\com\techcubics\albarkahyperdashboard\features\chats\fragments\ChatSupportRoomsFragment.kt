package com.techcubics.albarkahyperdashboard.features.chats.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyperdashboard.databinding.FragmentChatOrderRoomsBinding
import com.techcubics.albarkahyperdashboard.features.chats.adapters.ChatRoomsAdapter
import com.techcubics.albarkahyperdashboard.features.chats.intents.ChatIntent
import com.techcubics.albarkahyperdashboard.features.chats.states.ChatState
import com.techcubics.albarkahyperdashboard.features.chats.viewmodels.ChatViewModel
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.listeners.NavigationBarVisibilityListener
import com.techcubics.data.auth.utils.Constants
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel


class ChatSupportRoomsFragment : Fragment() {
    private var _binding: FragmentChatOrderRoomsBinding? = null
    private val binding get() = _binding!!
    private val viewModel by viewModel<ChatViewModel>()
    private lateinit var rvScrollListener: RecyclerView.OnScrollListener
    private lateinit var chatRoomAdapter: ChatRoomsAdapter

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        if (_binding == null) {
            _binding = FragmentChatOrderRoomsBinding.inflate(inflater, container, false)
            initViews()
            events()
            observers()
        }
        return binding.root
    }

    override fun onStart() {
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
        super.onStart()
    }

    private fun events() {
        rvScrollListener = object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (!recyclerView.canScrollVertically(1)) {
                    getChatRooms()
                }
            }
        }
        binding.rvChatRooms.addOnScrollListener(rvScrollListener)
    }

    private fun observers() {
        lifecycleScope.launch { viewModel.state.collect { collectResponse(it) } }
    }

    private fun collectResponse(state: ChatState) {
        binding.loading.root.visibility = View.GONE
        binding.msgLayout.root.visibility = View.GONE
        binding.pagingLoadingImg.visibility = View.GONE
        binding.rvChatRooms.visibility = View.VISIBLE
        when (state) {
            is ChatState.Idle -> {}
            is ChatState.PageLoading -> binding.pagingLoadingImg.visibility = View.VISIBLE
            is ChatState.ProgressLoading -> binding.loading.root.visibility = View.VISIBLE
            is ChatState.HideRv -> binding.rvChatRooms.visibility = View.GONE
            is ChatState.Error -> {

                if(!state.error!!.contains(Constants.unauthenticated)){
                    Helper.showErrorDialog(requireContext(),state.error?:"")
                }
            }
            is ChatState.ServerError -> setMsgLayout(
                state.error,
                com.techcubics.resources.R.raw.lottie_error
            )

            is ChatState.ViewSupportChatRooms -> chatRoomAdapter.updateChatRooms(state.chatRooms)

            else -> {}
        }
    }

    private fun setMsgLayout(msg: String?, lottieIcon: Int) {
        binding.rvChatRooms.visibility = View.GONE
        binding.msgLayout.root.visibility = View.VISIBLE
        binding.msgLayout.icon.setAnimation(lottieIcon)
        binding.msgLayout.tvMessage.text = msg
    }

    private fun initViews() {
        binding.ownerCont.visibility = View.GONE
        binding.shopCont.visibility = View.GONE
        binding.toolbarTitle.tvTitle.text =
            getString(com.techcubics.resources.R.string.chat_support)
        setChatRoomAdapter()
    }


    override fun onResume() {
        super.onResume()
        getChatRooms()
    }

    private fun setChatRoomAdapter() {
        chatRoomAdapter = ChatRoomsAdapter(requireContext(), arrayListOf(), isSupport = true)
        binding.rvChatRooms.adapter = chatRoomAdapter
        binding.rvChatRooms.layoutManager = LinearLayoutManager(requireContext())
    }

    private fun getChatRooms() {
        lifecycleScope.launch {
            viewModel.chatIntent.send(
                ChatIntent.GetSupportChatRoomsIntent(
                    viewModel.pageState.value
                )
            )
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        binding.rvChatRooms.removeOnScrollListener(rvScrollListener)
    }
}
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:background="?attr/selectableItemBackground"
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <androidx.cardview.widget.CardView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    app:cardCornerRadius="5dp"
                    android:layout_marginStart="16dp"
                    >
                    <ImageView
                        android:id="@+id/language_image"
                        android:layout_width="40dp"
                        android:layout_height="30dp"
                        android:scaleType="fitXY"
                        android:src="@drawable/flag_egypt"
                        />

                </androidx.cardview.widget.CardView>
                <TextView
                    android:id="@+id/language_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingStart="18dp"
                    android:paddingTop="16dp"
                    android:paddingBottom="15dp"
                    android:text="مصر"
                    android:textAppearance="@style/Medium2RegularDarkSearch"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </LinearLayout>
        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

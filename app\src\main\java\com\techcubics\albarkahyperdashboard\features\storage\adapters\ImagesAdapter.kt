package com.techcubics.albarkahyperdashboard.features.storage.adapters

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyperdashboard.databinding.ItemImageBinding
import com.techcubics.albarkahyperdashboard.features.storage.adapters.holders.ImageViewHolder
import com.techcubics.albarkahyperdashboard.utils.listeners.OnItemsChangedListener
import com.techcubics.albarkahyperdashboard.features.storage.viewmodels.ProductViewModel
import com.techcubics.domain.storage.models.Image
import java.util.regex.Matcher
import java.util.regex.Pattern

class ImagesAdapter(
    val context: Context,
    var images: List<Image>,
    private var video: String?,
    val viewModel: ProductViewModel?,
    private val listener: OnItemsChangedListener?
) :
    RecyclerView.Adapter<ImageViewHolder>() {

    constructor(context: Context, images: List<Image>)
            : this(context, images, null, null, null)

    fun updateImagesAndVideo(images: List<Image>, video: String?) {
        notifyItemRangeRemoved(0, itemCount)
        this.images = images
        this.video = video
        notifyItemRangeInserted(0, itemCount)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ImageViewHolder {
        return ImageViewHolder(
            context,
            ItemImageBinding.inflate(
                LayoutInflater.from(context),
                parent,
                false
            ),
            listener,
            viewModel
        )
    }

    override fun onBindViewHolder(holder: ImageViewHolder, position: Int) {
        if (video != null && position >= images.size) {
            holder.setVideo(extractVideoId())
        } else {
            holder.setData(images[position])
        }
    }

    private fun extractVideoId(): String {
        val pattern = "(?<=youtu.be/|watch\\?v=|/videos/|embed/)[^#&?]*"
        val compiledPattern: Pattern = Pattern.compile(pattern)
        val matcher: Matcher = compiledPattern.matcher(video!!)
        return if (matcher.find()) {
            matcher.group()
        } else {
            "error"
        }
    }

    override fun getItemCount(): Int {
        return if (video != null) {
            images.size + 1
        } else {
            images.size
        }

    }
}

// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    dependencies {
        classpath 'com.google.gms:google-services:4.4.1'
        classpath 'com.android.tools.build:gradle:8.4.2'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.1'
        classpath 'androidx.navigation:navigation-safe-args-gradle-plugin:2.7.7'
    }
    repositories {
        google()
        mavenCentral()
    }
}
plugins {
    id 'com.android.application' version '8.4.2' apply false
    id 'com.android.library' version '8.4.2' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.24' apply false
}
task clean(type: Delete) {
    delete rootProject.buildDir
}
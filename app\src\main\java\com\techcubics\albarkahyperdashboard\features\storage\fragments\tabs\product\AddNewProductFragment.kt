package com.techcubics.albarkahyperdashboard.features.storage.fragments.tabs.product

import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.os.Bundle
import android.provider.MediaStore
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.content.res.AppCompatResources.getDrawable
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.dhaval2404.colorpicker.ColorPickerDialog
import com.github.dhaval2404.colorpicker.model.ColorShape
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.techcubics.albarkahyperdashboard.R
import com.techcubics.albarkahyperdashboard.databinding.FragmentAddNewProductBinding
import com.techcubics.albarkahyperdashboard.features.storage.adapters.DropDownAdapter
import com.techcubics.albarkahyperdashboard.features.storage.adapters.ProductImagesAdapter
import com.techcubics.albarkahyperdashboard.features.storage.intents.ProductIntent
import com.techcubics.albarkahyperdashboard.features.storage.states.ProductsStates
import com.techcubics.albarkahyperdashboard.features.storage.viewmodels.ProductViewModel
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper.bitmapToFile
import com.techcubics.albarkahyperdashboard.utils.listeners.NavigationBarVisibilityListener
import com.techcubics.albarkahyperdashboard.utils.listeners.OnItemClickListener
import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.data.auth.utils.Constants
import com.techcubics.data.auth.utils.UserTypeEnum
import com.techcubics.domain.auth.models.User
import com.techcubics.domain.orders.requests.DeleteImageRequest
import com.techcubics.domain.storage.enums.ProductAction
import com.techcubics.domain.storage.models.Category
import com.techcubics.domain.storage.models.Image
import com.techcubics.domain.storage.models.Owner
import com.techcubics.domain.storage.models.Product
import com.techcubics.domain.storage.models.Shop
import com.techcubics.domain.storage.requests.AddNewProduct
import com.techcubics.resources.R.color
import com.techcubics.resources.R.drawable
import com.techcubics.resources.R.string
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.io.File
import java.io.IOException
import kotlin.properties.Delegates


class AddNewProductFragment : Fragment(), OnItemClickListener {
    private var catId: Int? = null
    private var ownerId: Int? = null
    private var _binding: FragmentAddNewProductBinding? = null
    private val binding get() = _binding!!
    private var sizeBottomSheetBehavior: BottomSheetBehavior<ConstraintLayout>? = null
    private var colorBottomSheetBehavior: BottomSheetBehavior<ConstraintLayout>? = null
    private var mDefaultColor by Delegates.notNull<Int>()
    private var bitmaps: ArrayList<Bitmap> = arrayListOf()
    private var imagesPath: MutableList<String> = mutableListOf()
    private var imagesForDisplay: MutableList<Image> = mutableListOf()
    private var iconBitmap: Bitmap? = null
    private var files: ArrayList<File>? = null
    private var ownerDropDownAdapter: DropDownAdapter<Owner>? = null
    private var shopDropDownAdapter: DropDownAdapter<Shop>? = null
    private var categoryDropDownAdapter: DropDownAdapter<Category>? = null
    private var subCategoryDropDownAdapter: DropDownAdapter<Category>? = null
    private var iconFile: File? = null
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var imagesAdapter: ProductImagesAdapter
    private val viewModel by viewModel<ProductViewModel>()
    private var isBottomSheetOpened = false
    private val addNewProduct = AddNewProduct()
    private var product: Product? = null
    private val imagesIds = arrayListOf<Int>()
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentAddNewProductBinding.inflate(inflater, container, false)
        initViews()
        events()
        observeViews()
        return binding.root
    }

    private fun initViews() {
        bitmaps = arrayListOf()
        mDefaultColor = requireContext().getColor(color.app_color)
        initSize()
        initColor()
        setImagesAdapter()
        if (sharedPreferencesManager.getUserType() == UserTypeEnum.Owner.value) {
            binding.ownerCont.visibility = View.GONE
            ownerId = sharedPreferencesManager.getOwnerId()
            getShopsByOwnerId()
        } else {
            getOwners()
        }
        getCategories()
        val bundle = arguments ?: return
        val args = AddNewProductFragmentArgs.fromBundle(bundle)
        product = args.product

        if (product != null) {
            addNewProduct.productId = product?.productId
            binding.addProduct.textView.text = getString(string.edit)
            binding.toolbar.tvTitle.text = getString(string.edit)
            if (sharedPreferencesManager.getUserType() == UserTypeEnum.Admin.value) {
                ownerId = product?.owner?.id
            }
            catId = product?.category?.id
            binding.ownerList.setText(product?.owner?.user?.name, false)
            binding.categoryList.setText(product?.category?.name, false)
            binding.subcategoryList.setText(product?.subCategory?.name, false)
            binding.shopList.setText(product?.shop?.name, false)
            binding.videoUrl.setText(product?.video)
            binding.nameAr.setText(product?.translations?.find { t -> t.locale == "ar" }?.name)
            binding.nameEn.setText(product?.translations?.find { t -> t.locale == "en" }?.name)
            binding.descAr.setText(product?.translations?.find { t -> t.locale == "ar" }?.description)
            binding.descEn.setText(product?.translations?.find { t -> t.locale == "en" }?.description)
            binding.minQty.setText(product?.minimumOrderNumber.toString())
            binding.maxQty.setText(product?.maximumOrderNumber.toString())
            binding.price.setText(product?.price.toString())
            getShopsByOwnerId()
            catId?.let { getSubCategoriesByCatId(it) }
            addNewProduct.ownerId = ownerId
            addNewProduct.categoryId = catId
            addNewProduct.shopId = product?.shop?.shopId
            addNewProduct.subCategoryId = product?.subCategory?.id
            addNewProduct.video = product?.video
            product?.icon?.let { Helper.loadImage(requireContext(), it, binding.icon) }
            product?.images?.let { imagesForDisplay.addAll(it) }
            imagesAdapter.updateImages(imagesForDisplay)
        } else {
            binding.addProduct.textView.text = getString(string.add_product)
            binding.toolbar.tvTitle.text = getString(string.add_product)
        }
        showHideUploadImage()
//        requireActivity().onBackPressedDispatcher.addCallback(
//            requireActivity(),
//            onBackPressedCallback
//        )

    }

    private fun setCategoryDropDown(categories: ArrayList<Category>?) {
        if (categories.isNullOrEmpty() ) {
            categories?.add(0, Category(name = " "))
        }
        categoryDropDownAdapter =
            DropDownAdapter(requireContext(), R.layout.item_dropdown, categories!!)
        binding.categoryList.setAdapter(categoryDropDownAdapter)
        binding.categoryList.setOnItemClickListener { _, _, i, _ ->
            binding.categoryList.setText(categories[i].name, false)
            addNewProduct.categoryId = categories[i].id
            catId = categories[i].id
            catId?.let { getSubCategoriesByCatId(it) }
        }
    }

    private fun setSubCategoryDropDown(subCategories: ArrayList<Category>?) {
        if (subCategories.isNullOrEmpty() ) {
            subCategories?.add(0, Category(name = " "))
        }
        subCategoryDropDownAdapter =
            DropDownAdapter(requireContext(), R.layout.item_dropdown, subCategories!!)
        binding.subcategoryList.setAdapter(subCategoryDropDownAdapter)
        binding.subcategoryList.setOnItemClickListener { _, _, i, _ ->
            binding.subcategoryList.setText(subCategories[i].name, false)
            addNewProduct.subCategoryId = subCategories[i].id
        }
    }

    private fun getSubCategoriesByCatId(catId: Int) {
        lifecycleScope.launch {
            viewModel.productIntent.send(ProductIntent.GetSubCategoriesByCatId(catId))
        }
    }

    private fun getCategories() {
        lifecycleScope.launch {
            viewModel.productIntent.send(ProductIntent.GetCategories)
        }
    }

    private fun setOwnerDropDown(owners: ArrayList<Owner>?) {
        if (owners.isNullOrEmpty() ) {
            owners?.add(0, Owner(user = User(name = " ")))
        }
        ownerDropDownAdapter = DropDownAdapter(requireContext(), R.layout.item_dropdown, owners!!)
        binding.ownerList.setAdapter(ownerDropDownAdapter)
        binding.ownerList.setOnItemClickListener { _, _, i, _ ->
            binding.ownerList.setText(owners[i].user?.name, false)
            addNewProduct.ownerId = owners[i].id
            ownerId = owners[i].id
            getShopsByOwnerId()
        }

    }

    private fun setShopDropDown(shops: ArrayList<Shop>?) {
        if (shops.isNullOrEmpty()) {
            shops?.add(0, Shop(name = " "))
        }
        shopDropDownAdapter = DropDownAdapter(requireContext(), R.layout.item_dropdown, shops!!)
        binding.shopList.setAdapter(shopDropDownAdapter)
        binding.shopList.setOnItemClickListener { _, _, i, _ ->
            binding.shopList.setText(shops[i].name, false)
            addNewProduct.shopId = shops[i].id
        }

    }

    private fun getShopsByOwnerId() {
        lifecycleScope.launch {
            ownerId?.let { viewModel.productIntent.send(ProductIntent.GetShopsByOwnerId(it)) }
        }
    }

//    private val onBackPressedCallback = object : OnBackPressedCallback(true) {
//        override fun handleOnBackPressed() {
//            if (isBottomSheetOpened) {
//                colorBottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
//                sizeBottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
//                isBottomSheetOpened = false
//
//            } else {
//                findNavController().popBackStack()
//            }
//        }
//
//    }

    private fun getOwners() {
        lifecycleScope.launch {
            viewModel.productIntent.send(ProductIntent.GetOwners)
        }
    }

    private fun setImagesAdapter() {
        imagesAdapter = ProductImagesAdapter(arrayListOf(), this)
        binding.rvImgs.adapter = imagesAdapter
        binding.rvImgs.layoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
    }

    private fun initColor() {
        binding.addColors.textView.text = getString(string.add_color)
        binding.addColors.textView.setTextColor(requireContext().getColor(color.app_color))
        binding.addColors.constraintsLayout.background =
            getDrawable(requireContext(), drawable.bg_btn_notfilled_ripple)
        colorBottomSheetBehavior = BottomSheetBehavior.from(binding.bottomAddColors.root)
        colorBottomSheetBehavior?.state = BottomSheetBehavior.STATE_HIDDEN
    }

    private val getContentForImages =
        registerForActivityResult(ActivityResultContracts.GetMultipleContents()) { uris ->
            val newImagesPath = uris.map { it.toString() }
            if (newImagesPath.isNotEmpty()) {
                imagesPath = imagesPath.toMutableList().apply { addAll(newImagesPath) }
                    .distinct() as MutableList<String>
                val newImages = imagesPath.mapIndexed { index, path -> Image(index, -1, path) }
                imagesForDisplay.addAll(newImages)
                imagesAdapter.updateImages(imagesForDisplay)
            }
            showHideUploadImage()
            bitmaps = uris.mapNotNull { uri ->
                try {
                    MediaStore.Images.Media.getBitmap(requireContext().contentResolver, uri)
                } catch (e: IOException) {
                    e.printStackTrace()
                    null
                }
            } as ArrayList<Bitmap>

        }

    private val getContent = registerForActivityResult(ActivityResultContracts.GetContent()) { uri ->
            Helper.loadImage(requireContext(), uri.toString(), binding.icon)
            try {
                iconBitmap= MediaStore.Images.Media.getBitmap(requireContext().contentResolver, uri)
            } catch (e: IOException) {
                iconBitmap = null
                e.printStackTrace()
            }
        }

    private fun initSize() {
        binding.addSizes.textView.text = getString(string.add_size)
        binding.addSizes.textView.setTextColor(requireContext().getColor(color.app_color))
        binding.addSizes.constraintsLayout.background =
            getDrawable(requireContext(), drawable.bg_btn_notfilled_ripple)
        sizeBottomSheetBehavior = BottomSheetBehavior.from(binding.bottomAddSizes.root)
        sizeBottomSheetBehavior?.state = BottomSheetBehavior.STATE_HIDDEN
        binding.bottomAddSizes.add.textView.text = getString(string.add)

    }

    private fun showHideUploadImage() {
        if (imagesForDisplay.size > 0) {
            binding.uplaodImages2.visibility = View.GONE
            binding.uplaodImages.visibility = View.VISIBLE
        } else {
            binding.uplaodImages2.visibility = View.VISIBLE
            binding.uplaodImages.visibility = View.INVISIBLE
        }
    }

    private fun events() {
        binding.toolbar.mainToolbar.setNavigationOnClickListener {
            findNavController().popBackStack()
        }
        binding.addSizes.root.setOnClickListener {
            isBottomSheetOpened = true
            sizeBottomSheetBehavior?.state = BottomSheetBehavior.STATE_EXPANDED
        }
        binding.addColors.root.setOnClickListener {
            isBottomSheetOpened = true
            colorBottomSheetBehavior?.state = BottomSheetBehavior.STATE_EXPANDED
        }
        binding.bottomAddSizes.closeIcon.setOnClickListener {
            sizeBottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
        }
        binding.bottomAddColors.closeIcon.setOnClickListener {
            colorBottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
        }
        binding.bottomAddColors.colorCodeCont.setOnClickListener {
            ColorPickerDialog
                .Builder(requireActivity())
                .setTitle("Pick Color")
                .setColorShape(ColorShape.CIRCLE)
                .setDefaultColor(mDefaultColor)
                .setColorListener { color, colorHex ->
                    mDefaultColor = color
                    binding.bottomAddColors.color.setBackgroundColor(color)
                    //TODO:save hex color
                }
                .show()
        }
        binding.uplaodImages.setOnClickListener {
            if (ContextCompat.checkSelfPermission(
                    requireContext(),
                    android.Manifest.permission.READ_EXTERNAL_STORAGE
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                getContentForImages.launch("image/*")
            } else {
                ActivityCompat.requestPermissions(
                    requireActivity(), arrayOf(android.Manifest.permission.READ_EXTERNAL_STORAGE),
                    1
                )
            }
        }
        binding.uplaodImages2.setOnClickListener {
            if (ContextCompat.checkSelfPermission(
                    requireContext(),
                    android.Manifest.permission.READ_EXTERNAL_STORAGE
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                getContentForImages.launch("image/*")
            } else {
                ActivityCompat.requestPermissions(
                    requireActivity(), arrayOf(android.Manifest.permission.READ_EXTERNAL_STORAGE),
                    1
                )
            }
        }
        binding.uplaodIcon.setOnClickListener {
            if (ContextCompat.checkSelfPermission(
                    requireContext(),
                    android.Manifest.permission.READ_EXTERNAL_STORAGE
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                getContent.launch("image/*")
            } else {
                ActivityCompat.requestPermissions(
                    requireActivity(), arrayOf(android.Manifest.permission.READ_EXTERNAL_STORAGE),
                    1
                )
            }
        }

        binding.addProduct.root.setOnClickListener {
            setFiles()
            setIcon()
            addNewProduct.images = files
            addNewProduct.icon = iconFile
            addNewProduct.productColors = arrayListOf()
            addNewProduct.productSizes = arrayListOf()
            setData()
            val type = if (product != null) {
                ProductAction.Edit
            } else {
                ProductAction.Add
            }

            lifecycleScope.launch {
                if (imagesIds.isEmpty()) {
                    viewModel.productIntent.send(
                        ProductIntent.StoreUpdateProduct(
                            addNewProduct,
                            type
                        )
                    )
                } else {
                    val request = DeleteImageRequest(
                        ownerId!!,
                        addNewProduct.shopId!!,
                        imagesIds,
                        product?.productId
                    )
                    viewModel.productIntent.send(
                        ProductIntent.DeleteImagesAndUpdateProduct(
                            request,
                            addNewProduct,
                            type
                        )
                    )
                }
            }
        }

    }

    private fun setData() {
        addNewProduct.video = binding.videoUrl.text.toString().ifEmpty { null }
        addNewProduct.nameAr = binding.nameAr.text.toString().ifEmpty { null }
        addNewProduct.nameEn = binding.nameEn.text.toString().ifEmpty { null }
        addNewProduct.descriptionAr = binding.descAr.text.toString().ifEmpty { null }
        addNewProduct.descriptionEn = binding.descEn.text.toString().ifEmpty { null }
        addNewProduct.price = binding.price.text.toString().ifEmpty { null }
        addNewProduct.minimumOrderNumber = binding.minQty.text.toString().ifEmpty { null }
        addNewProduct.maximumOrderNumber = binding.maxQty.text.toString().ifEmpty { null }
    }

    private fun setFiles() {
        files = arrayListOf()
        if (bitmaps.isEmpty()) {
            files = null
        } else {
            for (i in bitmaps) {
                val file = bitmapToFile(requireContext(), i, "image$i")
                files?.add(file!!)
            }
        }
    }

    private fun setIcon() {
        iconFile = if (iconBitmap == null) {
            null
        } else {
            bitmapToFile(requireContext(), iconBitmap, "icon")
        }
    }

    private fun observeViews() {
        lifecycleScope.launch {
            viewModel.productState.collect {
                binding.loading.root.visibility = View.GONE
                binding.msgLayout.root.visibility = View.GONE
                when (it) {
                    is ProductsStates.Idle -> {}
                    is ProductsStates.Loading -> {
                        binding.loading.root.visibility = View.VISIBLE
                    }

                    is ProductsStates.Error -> {
                        if(!it.error.contains(Constants.unauthenticated)){
                            Helper.showErrorDialog(requireContext(), it.error)
                        }
                    }

                    is ProductsStates.ViewCategories -> setCategoryDropDown(it.categories)
                    is ProductsStates.ViewOwners -> setOwnerDropDown(it.owners)
                    is ProductsStates.ViewShops -> setShopDropDown(it.shops)
                    is ProductsStates.ViewSubCategories -> setSubCategoryDropDown(it.subCategories)
                    ProductsStates.Success -> {
                        val msg = if (product != null) {
                            getString(string.product_updated)

                        } else {
                            getString(string.add_product_success)
                        }
                        Helper.showSuccessDialog(requireContext(), msg)
                        findNavController().popBackStack()
                    }

                    else -> {}
                }
            }
        }
    }

    override fun onClick(d: Int, type: String) {
        when (type) {
            "new" -> {
                imagesPath.removeAt(d)
                bitmaps.removeAt(d)
                imagesForDisplay.removeIf { it.pathID == -1 && it.id == d }
            }

            "old" -> {
                imagesForDisplay.removeIf { it.pathID == it.id && it.id == d }
                imagesIds.add(d)
            }
        }
        imagesForDisplay.let { imagesAdapter.updateImages(it) }
        showHideUploadImage()
    }

    override fun onStart() {
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
        super.onStart()
    }
}
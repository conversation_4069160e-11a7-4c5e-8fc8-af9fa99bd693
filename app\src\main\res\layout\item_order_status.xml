<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="16dp"
    app:cardElevation="5dp"
    app:cardPreventCornerOverlap="true"
    app:cardUseCompatPadding="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="5dp">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="15dp">
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="0dp"
                    android:layout_weight="1.5"
                    android:layout_height="wrap_content">
                    <TextView
                        android:id="@+id/status_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/Medium2RegularGray"
                        tools:text="completed"/>
                    <TextView
                        android:id="@+id/status_count"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textAlignment="viewEnd"
                        android:gravity="end"
                        android:textAppearance="@style/Medium2BoldDarkText"
                        tools:text="20"/>

                </LinearLayout>
                <ImageView
                    android:id="@+id/status_img"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_marginStart="15dp"
                    android:layout_marginBottom="15dp"
                    android:src="@drawable/ic_completed"
                    android:layout_height="50dp"
                    android:gravity="center_vertical"
                    />
            </LinearLayout>
            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:layout_marginBottom="20dp"
                android:progressBackgroundTint="@color/color_gray_34"
                android:progressTint="@color/pgreen"
                style="@android:style/Widget.ProgressBar.Horizontal"
                android:progress="70"
                android:layout_marginStart="15dp"
                android:layout_marginEnd="15dp"
                android:max="100" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
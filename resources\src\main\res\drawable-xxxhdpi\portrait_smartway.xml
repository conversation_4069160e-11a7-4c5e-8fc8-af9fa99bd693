<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="129dp"
    android:height="74dp"
    android:viewportWidth="129"
    android:viewportHeight="74">
  <group>
    <clip-path
        android:pathData="M26.75,66.05V51.12H30.89L31.03,52.44C31.62,51.96 32.29,51.57 33.03,51.26C33.77,50.96 34.57,50.81 35.42,50.81C36.31,50.81 37,50.96 37.5,51.26C38,51.57 38.39,51.99 38.67,52.54C39.28,52.03 39.97,51.62 40.74,51.29C41.51,50.97 42.42,50.81 43.45,50.81C44.99,50.81 46.12,51.26 46.84,52.16C47.56,53.06 47.92,54.44 47.92,56.29V66.05H43.51V56.88C43.51,56.06 43.35,55.47 43.05,55.1C42.74,54.74 42.25,54.55 41.56,54.55C41.13,54.55 40.72,54.65 40.31,54.85C39.9,55.05 39.56,55.33 39.28,55.69C39.32,55.92 39.34,56.14 39.35,56.35C39.36,56.56 39.36,56.81 39.36,57.11V66.05H35.19V56.82C35.19,56.06 35.08,55.5 34.85,55.12C34.61,54.74 34.17,54.56 33.52,54.56C33.06,54.56 32.63,54.69 32.22,54.96C31.81,55.24 31.44,55.55 31.11,55.91V66.05H26.75V66.05ZM54.32,66.36C53.48,66.36 52.71,66.2 52,65.86C51.28,65.52 50.71,65 50.29,64.3C49.86,63.6 49.65,62.72 49.65,61.67C49.65,60.14 50.11,58.94 51.02,58.07C51.94,57.19 53.32,56.76 55.15,56.76H58.82V56.41C58.82,55.66 58.6,55.12 58.15,54.79C57.71,54.47 56.87,54.3 55.65,54.3C54.15,54.3 52.67,54.57 51.23,55.09V51.81C51.88,51.52 52.67,51.28 53.61,51.09C54.54,50.9 55.53,50.81 56.57,50.81C58.6,50.81 60.2,51.28 61.36,52.22C62.52,53.17 63.1,54.67 63.1,56.73V66.05H59.18L58.95,64.79C58.47,65.29 57.86,65.68 57.11,65.95C56.36,66.22 55.43,66.36 54.32,66.36ZM55.74,63.24C56.44,63.24 57.05,63.11 57.57,62.84C58.09,62.56 58.5,62.22 58.82,61.8V59.69H55.65C54.34,59.69 53.68,60.29 53.68,61.48C53.68,62.03 53.84,62.46 54.18,62.77C54.51,63.09 55.03,63.24 55.74,63.24ZM65.8,66.05V51.12H70.05L70.19,52.51C70.76,52.11 71.47,51.74 72.33,51.42C73.18,51.09 74.03,50.89 74.89,50.8V54.61C74.4,54.68 73.87,54.78 73.29,54.91C72.7,55.05 72.15,55.21 71.62,55.4C71.09,55.59 70.64,55.79 70.27,56V66.04H65.8V66.05ZM82,66.36C80.37,66.36 79.17,65.89 78.4,64.94C77.63,64 77.25,62.7 77.25,61.04V54.96H75.47V51.12H77.25V48L81.72,46.65V51.12H84.92L84.72,54.96H81.72V60.69C81.72,61.41 81.88,61.91 82.2,62.19C82.51,62.47 82.98,62.62 83.61,62.62C84.15,62.62 84.7,62.51 85.25,62.3V65.73C84.38,66.15 83.3,66.36 82,66.36ZM22.92,11.25C17.21,7.77 11.44,8.24 6.23,12.64C1.52,16.6 -0.74,22.11 0.21,28.77C0.99,34.19 3.46,38.95 5.66,43.77C5.86,44.2 6.52,44.61 6.94,44.58C7.54,44.53 8.24,44.22 8.68,43.76C9.32,43.07 9.09,42.14 8.75,41.29C7.34,37.81 5.91,34.34 4.57,30.82C3.72,28.57 3.53,26.21 4.03,23.77C5.54,16.4 12.14,11.87 18.57,13.81C25,15.75 28.7,23.28 26.7,30.53C26.2,32.37 25.42,34.11 24.75,35.89C23.2,40.03 21.65,44.17 20.04,48.28C19.92,48.59 19.39,48.88 19.05,48.88C15.92,48.93 12.8,48.89 9.67,48.9C7.78,48.92 6.27,49.81 5.36,51.72C3.62,55.4 5.84,59.57 9.62,59.67C12.52,59.75 15.42,59.7 18.32,59.69C19.06,59.68 19.76,59.82 19.73,60.83C19.71,61.76 19.03,61.88 18.34,61.88C13.76,61.87 9.18,61.87 4.6,61.88V66.13C9.23,66.11 13.86,66.15 18.49,66.15C21.45,66.14 23.56,63.86 23.54,60.73C23.52,57.62 21.48,55.44 18.53,55.42C15.7,55.41 12.87,55.41 10.04,55.42C9.32,55.42 8.6,55.33 8.6,54.3C8.61,53.27 9.33,53.2 10.05,53.21C13.25,53.21 16.45,53.21 19.65,53.21H23C23.49,51.03 23.73,48.94 24.42,47.06C25.88,43.1 27.69,39.31 29.11,35.34C29.98,32.91 30.82,30.31 30.96,27.73C31.38,20.42 28.61,14.72 22.92,11.25ZM24.01,26.4C24.01,20.9 19.91,16.44 14.88,16.44C14.5,16.44 14.2,16.78 14.2,17.2C14.2,17.63 14.51,17.97 14.88,17.97C19.17,17.97 22.65,21.75 22.65,26.39C22.65,26.82 22.96,27.16 23.33,27.16C23.7,27.16 24.01,26.82 24.01,26.4Z"/>
    <path
        android:pathData="M87.22,7.16H-1.89V67.39H87.22V7.16Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="42.67"
            android:startY="67.39"
            android:endX="42.67"
            android:endY="7.16"
            android:type="linear">
          <item android:offset="0" android:color="#FF7055D3"/>
          <item android:offset="0.54" android:color="#FF4935AF"/>
          <item android:offset="1" android:color="#FF2F2496"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M15.53,4.25L6.97,5.48V12.07C6.97,12.07 9.35,14.07 15.53,14.07C21.71,14.07 24.09,12.07 24.09,12.07V5.48L15.53,4.25Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="6.97"
          android:startY="9.16"
          android:endX="24.09"
          android:endY="9.16"
          android:type="linear">
        <item android:offset="0" android:color="#FF7055D3"/>
        <item android:offset="0.54" android:color="#FF4935AF"/>
        <item android:offset="1" android:color="#FF2F2496"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M21.24,5.07C21.24,9.03 18.74,12.34 15.47,12.71C14.64,12.81 13.71,12.86 12.68,12.86C10.29,12.86 8.47,12.56 7.13,12.19C7.76,12.63 10.27,14.07 15.53,14.07C21.71,14.07 24.09,12.07 24.09,12.07V5.48L21.24,5.07Z"
      android:fillColor="#2F2496"/>
  <path
      android:pathData="M15.53,0L1.7,4.25V4.8L15.53,9.05L29.35,4.8V4.25L15.53,0Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="11.19"
          android:startY="-3.27"
          android:endX="19.56"
          android:endY="8.46"
          android:type="linear">
        <item android:offset="0" android:color="#FF7055D3"/>
        <item android:offset="0.54" android:color="#FF4935AF"/>
        <item android:offset="1" android:color="#FF2F2496"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M15.53,0L1.7,4.25L15.53,8.5L29.35,4.25L15.53,0Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="1.7"
          android:startY="4.25"
          android:endX="29.35"
          android:endY="4.25"
          android:type="linear">
        <item android:offset="0" android:color="#FF7055D3"/>
        <item android:offset="0.54" android:color="#FF4935AF"/>
        <item android:offset="1" android:color="#FF2F2496"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M11.31,7.21L16.81,0.39L15.53,0L1.7,4.25L11.31,7.21Z"
      android:strokeAlpha="0.4"
      android:fillAlpha="0.4">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="5.22"
          android:startY="0.81"
          android:endX="21.75"
          android:endY="5.63"
          android:type="linear">
        <item android:offset="0" android:color="#00FDFDFF"/>
        <item android:offset="0.91" android:color="#E8FDFDFF"/>
        <item android:offset="1" android:color="#FFFDFDFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M25.92,13.13L25.78,13.13C25.78,13.12 25.79,12.4 25.78,11.24C25.77,10.87 25.76,10.71 25.75,10.56C25.73,10.27 25.72,10.07 25.77,8.17C25.77,8.06 25.78,7.95 25.78,7.83C25.82,7.09 25.86,6.17 25.4,5.7C25.05,5.34 24.55,5.32 24.06,5.31C23.87,5.3 23.69,5.3 23.52,5.27C22.64,5.14 21.75,5.03 20.88,4.91L20.24,4.83C18.12,4.56 16.3,4.34 16.29,4.34L16.3,4.17C16.32,4.17 18.14,4.38 20.25,4.66L20.9,4.74C21.76,4.85 22.66,4.97 23.54,5.1C23.71,5.12 23.88,5.13 24.07,5.13C24.58,5.15 25.1,5.16 25.49,5.56C26.01,6.09 25.96,7.06 25.93,7.83C25.92,7.95 25.92,8.07 25.92,8.18C25.87,10.06 25.88,10.26 25.9,10.55C25.91,10.69 25.92,10.86 25.92,11.24C25.94,12.4 25.92,13.13 25.92,13.13Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.29"
          android:startY="8.65"
          android:endX="25.95"
          android:endY="8.65"
          android:type="linear">
        <item android:offset="0" android:color="#FFE48800"/>
        <item android:offset="0" android:color="#FFE48901"/>
        <item android:offset="0.1" android:color="#FFECAF31"/>
        <item android:offset="0.17" android:color="#FFF0C64F"/>
        <item android:offset="0.21" android:color="#FFF2CF5A"/>
        <item android:offset="0.28" android:color="#FFEDC947"/>
        <item android:offset="0.41" android:color="#FFE0B916"/>
        <item android:offset="0.47" android:color="#FFDAB200"/>
        <item android:offset="0.73" android:color="#FFDF9F00"/>
        <item android:offset="1" android:color="#FFE48800"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M16.46,4.25C16.46,4.53 16.04,4.75 15.53,4.75C15.02,4.75 14.6,4.53 14.6,4.25C14.6,3.98 15.02,3.76 15.53,3.76C16.04,3.76 16.46,3.98 16.46,4.25Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="15.53"
          android:startY="-0.96"
          android:endX="15.53"
          android:endY="5.16"
          android:type="linear">
        <item android:offset="0" android:color="#FF6B32E7"/>
        <item android:offset="0.54" android:color="#FF1D176D"/>
        <item android:offset="1" android:color="#FF000C17"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M26.32,12.42C26.32,12.73 26.11,12.98 25.85,12.98C25.59,12.98 25.38,12.73 25.38,12.42C25.38,12.1 25.59,11.85 25.85,11.85C26.11,11.85 26.32,12.1 26.32,12.42Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="25.45"
          android:startY="10.27"
          android:endX="27.33"
          android:endY="18.19"
          android:type="linear">
        <item android:offset="0" android:color="#FFE48800"/>
        <item android:offset="0" android:color="#FFE48901"/>
        <item android:offset="0.1" android:color="#FFECAF31"/>
        <item android:offset="0.17" android:color="#FFF0C64F"/>
        <item android:offset="0.21" android:color="#FFF2CF5A"/>
        <item android:offset="0.28" android:color="#FFEDC947"/>
        <item android:offset="0.41" android:color="#FFE0B916"/>
        <item android:offset="0.47" android:color="#FFDAB200"/>
        <item android:offset="0.73" android:color="#FFDF9F00"/>
        <item android:offset="1" android:color="#FFE48800"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M25.84,12.71C25.47,12.71 25.4,12.63 25.4,12.63V13.09H26.28V12.63C26.28,12.63 26.21,12.71 25.84,12.71Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="25.4"
          android:startY="12.86"
          android:endX="26.28"
          android:endY="12.86"
          android:type="linear">
        <item android:offset="0" android:color="#FFF1934C"/>
        <item android:offset="0.5" android:color="#FFEB6D05"/>
        <item android:offset="1" android:color="#FFD15918"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M26.49,13.25C26.34,13.01 26.11,12.86 25.86,12.86C25.58,12.86 25.32,13.04 25.18,13.33C24.83,14.04 25.1,15.09 25.48,15.69C25.51,15.73 25.53,15.77 25.55,15.81C25.56,15.81 25.56,15.82 25.57,15.83C25.58,15.86 25.6,15.89 25.61,15.92C25.62,15.93 25.62,15.94 25.62,15.95C25.64,15.98 25.65,16.01 25.67,16.04C25.67,16.05 25.67,16.05 25.68,16.06C25.69,16.1 25.71,16.13 25.72,16.17C25.78,16.31 25.83,16.45 25.87,16.57C26.09,16.34 26.24,16.04 26.39,15.74C26.54,15.42 26.67,15.08 26.72,14.72C26.85,13.75 26.49,13.24 26.49,13.25Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="25.17"
          android:startY="10.33"
          android:endX="27.05"
          android:endY="18.27"
          android:type="linear">
        <item android:offset="0" android:color="#FFE48800"/>
        <item android:offset="0" android:color="#FFE48901"/>
        <item android:offset="0.1" android:color="#FFECAF31"/>
        <item android:offset="0.17" android:color="#FFF0C64F"/>
        <item android:offset="0.21" android:color="#FFF2CF5A"/>
        <item android:offset="0.28" android:color="#FFEDC947"/>
        <item android:offset="0.41" android:color="#FFE0B916"/>
        <item android:offset="0.47" android:color="#FFDAB200"/>
        <item android:offset="0.73" android:color="#FFDF9F00"/>
        <item android:offset="1" android:color="#FFE48800"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M25.61,13.05C25.61,13.05 25.59,13.08 25.55,13.13C25.51,13.19 25.47,13.27 25.42,13.38C25.39,13.43 25.37,13.49 25.35,13.55C25.34,13.58 25.33,13.62 25.32,13.65C25.31,13.68 25.3,13.72 25.29,13.76C25.28,13.79 25.28,13.83 25.27,13.87C25.26,13.91 25.26,13.94 25.26,13.98L25.25,14.04V14.1L25.25,14.22C25.25,14.3 25.26,14.38 25.27,14.45C25.28,14.53 25.29,14.6 25.31,14.68C25.33,14.75 25.35,14.82 25.37,14.88C25.4,14.94 25.42,15 25.44,15.06C25.45,15.08 25.46,15.11 25.47,15.13C25.49,15.16 25.5,15.18 25.51,15.2C25.53,15.24 25.55,15.28 25.56,15.31C25.59,15.37 25.61,15.41 25.61,15.41C25.61,15.41 25.59,15.38 25.55,15.32C25.53,15.3 25.51,15.26 25.49,15.22C25.47,15.2 25.46,15.18 25.44,15.16C25.43,15.13 25.42,15.11 25.41,15.08C25.38,15.03 25.35,14.97 25.32,14.91C25.3,14.84 25.28,14.77 25.25,14.7C25.23,14.63 25.22,14.55 25.2,14.47C25.19,14.39 25.18,14.31 25.17,14.22L25.17,14.1L25.18,14.04L25.18,13.98C25.19,13.93 25.2,13.89 25.2,13.85C25.21,13.82 25.22,13.78 25.23,13.74C25.24,13.7 25.25,13.67 25.26,13.63C25.27,13.6 25.29,13.56 25.3,13.53C25.33,13.47 25.35,13.41 25.38,13.36C25.41,13.3 25.44,13.26 25.47,13.22C25.49,13.18 25.52,13.15 25.54,13.13C25.58,13.07 25.61,13.05 25.61,13.05Z"
      android:fillColor="#FD8A04"/>
  <path
      android:pathData="M25.77,13.05C25.77,13.05 25.76,13.09 25.75,13.16C25.74,13.2 25.74,13.25 25.73,13.3C25.72,13.35 25.71,13.41 25.7,13.47C25.69,13.6 25.67,13.75 25.66,13.91C25.65,13.99 25.65,14.07 25.64,14.15C25.64,14.24 25.64,14.32 25.64,14.41C25.64,14.49 25.64,14.57 25.64,14.66C25.65,14.74 25.66,14.83 25.67,14.9C25.68,14.98 25.68,15.06 25.7,15.13C25.71,15.2 25.73,15.27 25.74,15.33C25.76,15.39 25.78,15.44 25.79,15.49C25.81,15.54 25.83,15.58 25.84,15.61C25.87,15.68 25.88,15.72 25.88,15.72C25.88,15.72 25.86,15.68 25.83,15.62C25.81,15.59 25.79,15.55 25.77,15.51C25.75,15.46 25.73,15.4 25.7,15.34C25.69,15.28 25.67,15.22 25.65,15.15C25.63,15.07 25.62,14.99 25.6,14.92C25.59,14.84 25.58,14.75 25.58,14.67C25.57,14.58 25.57,14.49 25.57,14.41C25.57,14.32 25.57,14.23 25.58,14.15C25.58,14.06 25.59,13.98 25.6,13.9C25.6,13.82 25.62,13.74 25.63,13.67C25.64,13.59 25.65,13.52 25.66,13.46C25.69,13.33 25.72,13.23 25.73,13.16C25.76,13.09 25.77,13.05 25.77,13.05Z"
      android:fillColor="#FD8A04"/>
  <path
      android:pathData="M25.88,13.35C25.88,13.35 25.89,13.39 25.91,13.46C25.93,13.53 25.95,13.63 25.97,13.76C25.98,13.82 25.99,13.89 26,13.96C26.01,14.03 26.02,14.1 26.03,14.18C26.04,14.34 26.05,14.5 26.06,14.67C26.06,14.76 26.06,14.84 26.06,14.92C26.06,15 26.05,15.08 26.05,15.16C26.05,15.24 26.04,15.32 26.03,15.39C26.03,15.46 26.02,15.53 26.01,15.59C26,15.71 25.98,15.82 25.97,15.89C25.96,15.96 25.95,16 25.95,16C25.95,16 25.95,15.96 25.96,15.89C25.96,15.85 25.96,15.8 25.97,15.75C25.97,15.7 25.97,15.65 25.97,15.58C25.98,15.52 25.98,15.46 25.98,15.38C25.98,15.31 25.99,15.24 25.99,15.16C25.99,15.08 25.99,15 25.99,14.92C25.98,14.84 25.99,14.75 25.98,14.67C25.98,14.51 25.97,14.34 25.96,14.19C25.95,14.03 25.94,13.89 25.93,13.76C25.91,13.64 25.9,13.53 25.89,13.46C25.89,13.39 25.88,13.35 25.88,13.35Z"
      android:fillColor="#FD8A04"/>
  <path
      android:pathData="M25.95,13.02C25.95,13.02 25.97,13.05 26,13.11C26.02,13.17 26.06,13.26 26.1,13.37C26.13,13.48 26.17,13.61 26.21,13.75C26.22,13.82 26.24,13.9 26.25,13.97C26.26,14.05 26.27,14.13 26.28,14.21C26.29,14.29 26.3,14.36 26.3,14.44C26.3,14.52 26.3,14.6 26.3,14.67C26.3,14.75 26.3,14.82 26.29,14.89C26.29,14.92 26.29,14.95 26.28,14.98C26.28,15.02 26.27,15.04 26.27,15.07C26.26,15.13 26.26,15.19 26.25,15.23C26.24,15.28 26.23,15.32 26.22,15.35C26.2,15.42 26.19,15.46 26.19,15.46C26.19,15.46 26.2,15.42 26.21,15.35C26.21,15.32 26.21,15.28 26.22,15.23C26.22,15.18 26.23,15.13 26.23,15.07C26.23,15.04 26.24,15.01 26.24,14.98C26.24,14.95 26.24,14.91 26.24,14.88C26.24,14.81 26.24,14.74 26.24,14.67C26.24,14.6 26.24,14.52 26.23,14.45C26.23,14.37 26.22,14.29 26.21,14.22C26.21,14.14 26.19,14.07 26.18,13.99C26.17,13.91 26.16,13.84 26.15,13.77C26.13,13.7 26.12,13.63 26.1,13.57C26.09,13.5 26.08,13.44 26.06,13.38C26.03,13.27 26.01,13.18 25.99,13.12C25.96,13.05 25.95,13.02 25.95,13.02Z"
      android:fillColor="#FD8A04"/>
  <path
      android:pathData="M26.31,13.35C26.31,13.35 26.37,13.5 26.43,13.73C26.44,13.79 26.46,13.85 26.47,13.92C26.48,13.99 26.49,14.06 26.5,14.13C26.51,14.21 26.52,14.29 26.52,14.37C26.53,14.45 26.53,14.53 26.53,14.61C26.52,14.69 26.53,14.77 26.52,14.85C26.51,14.93 26.5,15.01 26.48,15.08C26.47,15.16 26.46,15.23 26.44,15.29C26.42,15.36 26.4,15.42 26.38,15.48C26.35,15.53 26.33,15.58 26.31,15.62C26.29,15.67 26.27,15.7 26.25,15.73C26.22,15.79 26.2,15.82 26.2,15.82C26.2,15.82 26.22,15.78 26.24,15.72C26.26,15.69 26.27,15.65 26.29,15.61C26.31,15.56 26.32,15.51 26.34,15.46C26.36,15.4 26.37,15.34 26.39,15.27C26.4,15.21 26.41,15.14 26.42,15.07C26.44,15 26.44,14.92 26.45,14.84C26.46,14.77 26.46,14.69 26.46,14.61C26.46,14.53 26.46,14.45 26.46,14.37C26.45,14.29 26.45,14.22 26.44,14.14C26.44,14.07 26.43,14 26.42,13.93C26.41,13.86 26.4,13.8 26.39,13.74C26.37,13.62 26.36,13.52 26.34,13.46C26.32,13.39 26.31,13.35 26.31,13.35Z"
      android:fillColor="#FD8A04"/>
  <group>
    <clip-path
        android:pathData="M104.73,49.91L100.67,65.9H100.16L96.31,55.54L93.66,65.9H93.18L87.23,49.91H91.44L94.9,59.83C95.02,59.39 95.62,57.1 96.04,54.82L94.24,49.91H98.45L101.9,59.83C102.12,59.01 104.07,51.72 103.23,49.91H104.73ZM118.47,65.9C116.97,65.9 115.65,64.81 115.05,63.14C114.24,64.98 112.19,66.24 109.99,66.24C107.41,66.24 105.66,64.46 105.66,61.77C105.66,58.94 109.39,56.66 114.68,56.32V54.31C114.68,52.23 113.24,50.43 111.29,50.43C109.18,50.43 108.94,52.71 107.62,52.71C105.82,52.71 106.17,50.9 106.21,50.7C108.34,51.31 109.61,49.92 112.31,49.92C115.92,49.92 118.47,52.23 118.47,55.57V65.9ZM114.69,56.66C110.3,57.07 109.42,57.92 109.42,61.77C109.42,63.99 110.21,65.48 111.56,65.48C113.06,65.48 114.69,63.64 114.69,61.7V56.66ZM129,49.91L124.01,69.51C123.38,71.89 122.39,73.02 119.92,73.02L120.13,71.08C122.9,73.46 123.56,70.05 124.04,68.08L124.52,66.2L118.47,49.91H122.68L126.14,59.83C126.39,59.05 128.28,52.03 127.47,49.91H129Z"/>
    <path
        android:pathData="M129.91,45.44H86.77V73.93H129.91V45.44Z"
        android:fillColor="#2F2496"/>
  </group>
  <group>
    <clip-path
        android:pathData="M6.37,69.48H7.28L8.95,73.93H8.01L6.8,70.48L5.58,73.93H4.66L6.37,69.48ZM5.61,72.26H7.93V73.04H5.61V72.26ZM26.14,70.84C26.01,70.66 25.85,70.51 25.67,70.41C25.49,70.31 25.3,70.25 25.12,70.25C24.95,70.25 24.79,70.29 24.65,70.36C24.51,70.43 24.38,70.53 24.27,70.66C24.16,70.79 24.07,70.95 24.01,71.12C23.95,71.29 23.92,71.48 23.92,71.69C23.92,71.88 23.95,72.07 24.01,72.24C24.07,72.42 24.16,72.57 24.27,72.7C24.38,72.83 24.51,72.93 24.65,73.01C24.79,73.08 24.95,73.12 25.12,73.12C25.3,73.12 25.48,73.07 25.66,72.97C25.84,72.88 26,72.75 26.14,72.59L26.66,73.21C26.52,73.37 26.37,73.51 26.19,73.62C26.02,73.74 25.84,73.84 25.65,73.9C25.45,73.97 25.26,74 25.07,74C24.78,74 24.5,73.94 24.26,73.82C24.01,73.71 23.79,73.54 23.6,73.33C23.42,73.12 23.27,72.88 23.17,72.6C23.06,72.32 23.01,72.02 23.01,71.69C23.01,71.37 23.06,71.07 23.17,70.79C23.27,70.52 23.42,70.28 23.61,70.07C23.8,69.86 24.03,69.7 24.28,69.58C24.53,69.47 24.81,69.41 25.11,69.41C25.3,69.41 25.49,69.44 25.68,69.5C25.87,69.56 26.04,69.65 26.21,69.75C26.38,69.86 26.52,70 26.66,70.15L26.14,70.84ZM42.45,69.48H43.36L45.03,73.93H44.09L42.88,70.48L41.66,73.93H40.74L42.45,69.48ZM41.69,72.26H44.01V73.04H41.69V72.26ZM61.14,69.48C61.45,69.48 61.73,69.53 61.98,69.64C62.24,69.75 62.45,69.9 62.64,70.1C62.82,70.29 62.96,70.53 63.06,70.8C63.17,71.07 63.22,71.37 63.22,71.7C63.22,72.03 63.17,72.33 63.06,72.6C62.96,72.87 62.82,73.11 62.63,73.3C62.44,73.5 62.22,73.66 61.97,73.77C61.71,73.87 61.43,73.93 61.11,73.93H59.48V69.48H61.14ZM61.17,73.11C61.33,73.11 61.49,73.08 61.63,73.01C61.76,72.94 61.89,72.84 61.99,72.72C62.09,72.59 62.17,72.45 62.23,72.27C62.28,72.1 62.31,71.92 62.31,71.71C62.31,71.5 62.28,71.31 62.22,71.14C62.16,70.97 62.08,70.82 61.98,70.69C61.87,70.56 61.74,70.46 61.6,70.39C61.46,70.32 61.3,70.29 61.13,70.29H60.37V73.11H61.17V73.11ZM77.83,69.48H80.82V70.29H78.72V71.29H80.61V72.1H78.72V73.12H80.89V73.93H77.83V69.48ZM95.62,69.48H96.6L97.83,72.25L99.04,69.48H100.02V73.93H99.22V70.86L98.11,73.48H97.54L96.41,70.86V73.93H95.62V69.48ZM114.48,69.48H115.37L116.38,71.51L117.36,69.48H118.25L116.43,73.28H116.31L114.48,69.48ZM115.93,72H116.82V73.93H115.93V72Z"/>
    <path
        android:pathData="M118.46,67.76H3.67V76.25H118.46V67.76Z"
        android:fillColor="#E48900"/>
  </group>
</vector>

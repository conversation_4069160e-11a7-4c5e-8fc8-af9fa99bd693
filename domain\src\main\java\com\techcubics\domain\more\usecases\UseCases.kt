package com.techcubics.domain.more.usecases

import com.techcubics.domain.auth.models.UpdateProfileRequest
import com.techcubics.domain.auth.models.User
import com.techcubics.domain.auth.repositories.AuthRepo
import com.techcubics.domain.auth.requests.*
import com.techcubics.domain.auth.requests.LoginRequest
import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.more.models.BannerData
import com.techcubics.domain.more.repositories.MoreRepo
import com.techcubics.domain.more.requests.AccountSettingRequest


class LanguageUseCase(private val repo: MoreRepo){
    suspend operator fun invoke() = repo.getLanguages()
}

class AccountSettingUsesCase(private val repo: MoreRepo) {
    suspend operator fun invoke(accountSettingRequest: AccountSettingRequest) = repo.updateProfileCall(accountSettingRequest)
}
class GetHomeBannersUseCase(private val repo: MoreRepo) {
    suspend operator fun invoke() = repo.getHomeBanner()
}
class GetBannersUseCase(private val repo: MoreRepo) {
    suspend operator fun invoke() = repo.getBanners()
}



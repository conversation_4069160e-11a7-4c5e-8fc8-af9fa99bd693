<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:padding="10dp"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <ImageView
                    android:id="@+id/product_image"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_height="90dp"
                    android:adjustViewBounds="true"
                    android:src="@drawable/portrait_placeholder"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/guideline"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_weight="1.5"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical"
                    android:layout_height="wrap_content">
                    <TextView
                        android:gravity="start"
                        android:textAlignment="viewStart"
                        android:id="@+id/product_name"
                        android:layout_width="wrap_content"
                        android:layout_marginStart="10dp"
                        android:textColor="@color/color_gray_35"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/Medium2BoldDarkText"
                        tools:text="محممدددددددد"/>
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:orientation="horizontal"
                        >
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularGray"
                            android:textColor="@color/color_gray_36"
                            android:text="@string/amount"/>
                        <TextView
                            android:gravity="start"
                            android:textAlignment="viewStart"
                            android:id="@+id/amount"
                            android:layout_marginStart="10dp"
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textColor="@color/pgreen"
                            android:textAppearance="@style/Medium2RegularGray"
                            tools:text="30"/>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_marginStart="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="2">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textColor="@color/color_gray_36"
                            android:textAppearance="@style/Medium2RegularGray"
                            android:text="@string/price"/>
                        <TextView
                            android:gravity="start"
                            android:textAlignment="viewStart"
                            android:id="@+id/price"
                            android:layout_marginStart="10dp"
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:textColor="@color/pgreen"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularGray"
                            tools:text="20"/>
                        <TextView
                            android:gravity="start"
                            android:textAlignment="viewStart"
                            android:textColor="@color/pgreen"
                            android:layout_marginStart="5dp"
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularGray"
                            android:text="@string/currency_name"/>
                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@drawable/divider"
                android:layout_marginTop="5dp"/>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

package com.techcubics.domain.storage.repositories

import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.orders.requests.DeleteImageRequest
import com.techcubics.domain.storage.models.Category
import com.techcubics.domain.storage.models.Discount
import com.techcubics.domain.storage.models.Owner
import com.techcubics.domain.storage.models.Product
import com.techcubics.domain.storage.models.Shop
import com.techcubics.domain.storage.requests.AddNewDiscount
import com.techcubics.domain.storage.requests.AddNewProductRequest
import com.techcubics.domain.storage.requests.ProductsFilterRequestBody

interface StorageRepo {
    suspend fun getProducts(page: Int, filter: ProductsFilterRequestBody): BaseResponse<MutableList<Product>>?
    suspend fun setActiveStatus(id: Int): BaseResponse<Product>?
    suspend fun setWantedStatus(id: Int): BaseResponse<Product>?
    suspend fun getProductDetails(productId: Int,isOwner:Int?): BaseResponse<Product>?
    suspend fun getOwners(): BaseResponse<ArrayList<Owner>>?
    suspend fun getShops(ownerId:Int): BaseResponse<ArrayList<Shop>>?
    suspend fun getCategories(): BaseResponse<ArrayList<Category>>?
    suspend fun getSubCategories(catId:Int): BaseResponse<ArrayList<Category>>?
    suspend fun storeProduct(request: AddNewProductRequest): BaseResponse<Product>?
    suspend fun updateProduct(request: AddNewProductRequest): BaseResponse<Product>?
    suspend fun deleteImages(request: DeleteImageRequest): BaseResponse<Product>?
    suspend fun storeDiscount(request: AddNewDiscount): BaseResponse<Discount>?
    suspend fun updateDiscount(request: AddNewDiscount): BaseResponse<Discount>?
    suspend fun setDiscountActiveStatus(id: Int): BaseResponse<Discount>?
    suspend fun getDiscountDetails(discountId: Int): BaseResponse<Discount>?
    suspend fun getDiscounts(page: Int, request: ProductsFilterRequestBody): BaseResponse<MutableList<Discount>>?
    suspend fun getProductsByOwnerAndShopId(ownerId: Int, shopId: Int): BaseResponse<ArrayList<Product>>?
}
<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardCornerRadius="5dp"
    android:elevation="12dp"
    android:layout_margin="5dp"

    xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:orientation="horizontal">
        <ImageView
            android:id="@+id/img"
            android:layout_width="0dp"
            android:layout_weight="0.5"
            android:layout_margin="6dp"
            android:layout_height="match_parent"
            android:src="@drawable/ic_city" />
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.5"
            android:orientation="vertical">
            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:textAlignment="viewStart"
                android:gravity="start"
                android:layout_height="wrap_content"
                tools:text="فواتيرى"
                android:textAppearance="@style/Medium2BoldDarkText"
                android:textColor="@color/color_gray_29"/>
            <TextView
                android:id="@+id/count"
                android:layout_width="match_parent"
                android:textAlignment="viewStart"
                android:gravity="start"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                tools:text="20"
                android:textAppearance="@style/LargeBoldDarkText"
                android:textColor="@color/color_gray_36"/>
        </LinearLayout>
    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
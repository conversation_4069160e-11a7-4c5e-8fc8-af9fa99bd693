package com.techcubics.albarkahyperdashboard.utils.di

import android.content.Context
import com.techcubics.albarkahyperdashboard.features.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyperdashboard.features.bills.viewmodels.OrdersViewModel
import com.techcubics.albarkahyperdashboard.features.chats.viewmodels.ChatViewModel
import com.techcubics.albarkahyperdashboard.features.storage.viewmodels.StorageViewModel
import com.techcubics.data.storage.remote.repoImpl.StorageRepoImpl
import com.techcubics.domain.storage.repositories.StorageRepo
import com.techcubics.domain.storage.usecases.GetProductsUseCase
import com.techcubics.domain.storage.usecases.SetStatusUseCase
import com.techcubics.albarkahyperdashboard.features.more.viewmodels.MoreViewModel
import com.techcubics.albarkahyperdashboard.features.owner.viewmodels.OwnersViewModel
import com.techcubics.albarkahyperdashboard.features.reports.viewmodels.ReportsViewModel
import com.techcubics.albarkahyperdashboard.features.storage.viewmodels.DiscountViewModel
import com.techcubics.albarkahyperdashboard.features.storage.viewmodels.ProductViewModel

import com.techcubics.data.common.RetrofitBuilder
import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.data.auth.remote.repoImp.AuthRepoImpl
import com.techcubics.data.chat.remote.repoimpl.ChatRepoImpl
import com.techcubics.data.more.remote.repoImp.MoreRepoImpl
import com.techcubics.data.bills.repoImp.BillsRepoImpl
import com.techcubics.data.owner.repoImpl.OwnersRepoImpl
import com.techcubics.data.report.repoImpl.ReportsRepoImpl
import com.techcubics.domain.auth.repositories.AuthRepo
import com.techcubics.domain.auth.usecases.CheckAuthUseCase
import com.techcubics.domain.auth.usecases.DeleteAccountUseCase
import com.techcubics.domain.auth.usecases.ForgetPasswordUseCse
import com.techcubics.domain.auth.usecases.LoginUseCase
import com.techcubics.domain.auth.usecases.LogoutUseCase
import com.techcubics.domain.auth.usecases.UpdatePasswordUseCse
import com.techcubics.domain.chats.repo.ChatRepo
import com.techcubics.domain.chats.usecases.*
import com.techcubics.domain.more.repositories.MoreRepo
import com.techcubics.domain.more.usecases.AccountSettingUsesCase
import com.techcubics.domain.more.usecases.LanguageUseCase
import com.techcubics.domain.storage.usecases.GetProductDetailsUseCase
import com.techcubics.domain.orders.repositories.BillsRepo
import com.techcubics.domain.orders.usecases.CancelOrderUseCase
import com.techcubics.domain.orders.usecases.GetGovernorates
import com.techcubics.domain.orders.usecases.GetRegions
import com.techcubics.domain.orders.usecases.OrderDetailsUseCase
import com.techcubics.domain.orders.usecases.OrderStatusUseCase
import com.techcubics.domain.orders.usecases.OrdersByFilterUseCase
import com.techcubics.domain.orders.usecases.ReturnOrderUseCase
import com.techcubics.domain.orders.usecases.UpdateOrderStatusUseCase
import com.techcubics.domain.owner.repositories.OwnersRepo
import com.techcubics.domain.owner.usecases.CreateOwnerUseCase
import com.techcubics.domain.owner.usecases.GetOwnersInMoreUseCase
import com.techcubics.domain.owner.usecases.OwnerDetailsUseCase
import com.techcubics.domain.owner.usecases.OwnersByFilterUseCase
import com.techcubics.domain.owner.usecases.UpdateOwnerPasswordUseCase
import com.techcubics.domain.owner.usecases.UpdateOwnerStatusUseCase
import com.techcubics.domain.owner.usecases.UpdateOwnerUseCase
import com.techcubics.domain.reports.repositories.ReportsRepo
import com.techcubics.domain.reports.usecases.GetReportsUseCase
import com.techcubics.domain.storage.usecases.DeleteImagesUseCase
import com.techcubics.domain.storage.usecases.GetCategoriesUseCase
import com.techcubics.domain.storage.usecases.GetDiscountDetailsUseCase
import com.techcubics.domain.storage.usecases.GetDiscountsUseCase
import com.techcubics.domain.storage.usecases.GetOwnersUseCase
import com.techcubics.domain.storage.usecases.GetProductsByOwnerAndShopIdUseCase
import com.techcubics.domain.storage.usecases.GetShopsByOwnerIdUseCase
import com.techcubics.domain.storage.usecases.GetSubCategoriesUseCase
import com.techcubics.domain.storage.usecases.SetDiscountStatusUseCase
import com.techcubics.domain.storage.usecases.StoreDiscountUseCase
import com.techcubics.domain.storage.usecases.StoreProductUseCase
import org.koin.android.ext.koin.androidContext
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.dsl.module


val viewModelModule = module {
    viewModel { AuthViewModel(get(), get(), get(), get(), get(),get()) }
    viewModel { StorageViewModel(get(), get(), get(), get(), get(), get(), get(), get(), get(), get()) }
    viewModel {MoreViewModel(get(), get())}
    viewModel {ProductViewModel(get(), get(),get(),get(),get(),get(),get(),get())}
    viewModel { DiscountViewModel(get(), get(),get(),get(),get(),get()) }
    viewModel { ChatViewModel(get(), get(),get(),get(),get(),get(),get(),get()) }
    viewModel { OrdersViewModel(get(),get(),get(),get(),get(),get(),get(),get()) }
    viewModel { OwnersViewModel(get(),get(),get(),get(),get(),get()) }
    viewModel { ReportsViewModel(get(),get(),get()) }
}

val repositoryModule = module {
    single<AuthRepo> { AuthRepoImpl(get(), get()) }
    single<StorageRepo> { StorageRepoImpl(get(), get()) }
    single<MoreRepo> { MoreRepoImpl(get(), get()) }
    single<BillsRepo> { BillsRepoImpl(get(), get()) }
    single<ChatRepo> { ChatRepoImpl(get(), get()) }
    single<OwnersRepo> { OwnersRepoImpl(get(), get()) }
    single<ReportsRepo> { ReportsRepoImpl(get(),get()) }
}
    val useCasesModule = module {
        single { LoginUseCase(get()) }
        single { ForgetPasswordUseCse(get()) }
        single { LogoutUseCase(get()) }
        single { DeleteAccountUseCase(get()) }
        single { CheckAuthUseCase(get()) }
        single { UpdatePasswordUseCse(get()) }
        single { GetProductsUseCase(get()) }
        single { GetProductDetailsUseCase(get()) }
        single { SetStatusUseCase(get()) }
        single { AccountSettingUsesCase(get()) }
        single { LanguageUseCase(get()) }
        single { OrdersByFilterUseCase(get()) }
        single { OrderDetailsUseCase(get()) }
        single { GetOwnersUseCase(get()) }
        single { GetShopsByOwnerIdUseCase(get()) }
        single { GetCategoriesUseCase(get()) }
        single { GetSubCategoriesUseCase(get()) }
        single { StoreProductUseCase(get()) }
        single { OrderStatusUseCase(get()) }
        single { UpdateOrderStatusUseCase(get()) }
        single { CancelOrderUseCase(get()) }
        single { ReturnOrderUseCase(get()) }
        single { DeleteImagesUseCase(get()) }
        single { GetDiscountsUseCase(get()) }
        single { GetDiscountDetailsUseCase(get()) }
        single { StoreDiscountUseCase(get()) }
        single { SetDiscountStatusUseCase(get()) }
        single { GetProductsByOwnerAndShopIdUseCase(get()) }
        single { GetOrdersChatRoomsUseCase(get()) }
        single { GetOrderChatsUseCase(get()) }
        single { SendOrderChatMessageUseCase(get()) }
        single { GetSupportChatRoomsUseCase(get()) }
        single { GetSupportChatsUseCase(get()) }
        single { SendSupportChatMessageUseCase(get()) }
        single { GetGovernorates(get()) }
        single { GetRegions(get()) }
        single { GetOwnersInMoreUseCase(get()) }
        single { OwnersByFilterUseCase(get()) }
        single { UpdateOwnerUseCase(get()) }
        single { OwnerDetailsUseCase(get()) }
        single { UpdateOwnerStatusUseCase(get()) }
        single { UpdateOwnerPasswordUseCase(get()) }
        single { CreateOwnerUseCase(get()) }
        single { GetReportsUseCase(get()) }
    }

    val networkModule = module {
        single { RetrofitBuilder(SharedPreferencesManager(get())) }
    }

    val sharedPreferenceModule = module {
        single { androidContext().getSharedPreferences("albarkaDashboard", Context.MODE_PRIVATE) }
        single {
            SharedPreferencesManager(get())
        }

    }

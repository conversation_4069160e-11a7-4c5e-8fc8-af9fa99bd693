package com.techcubics.albarkahyperdashboard.features.chats.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.techcubics.albarkahyperdashboard.databinding.FragmentOrderChatsBinding
import com.techcubics.albarkahyperdashboard.features.chats.adapters.ChatsAdapter
import com.techcubics.albarkahyperdashboard.features.chats.intents.ChatIntent
import com.techcubics.albarkahyperdashboard.features.chats.states.ChatState
import com.techcubics.albarkahyperdashboard.features.chats.viewmodels.ChatViewModel
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.listeners.NavigationBarVisibilityListener
import com.techcubics.data.auth.utils.Constants
import com.techcubics.domain.chats.models.SupportChat
import com.techcubics.domain.chats.request.SendMessageRequest
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel

class SupportChatsFragment : Fragment() {

    private var _binding: FragmentOrderChatsBinding? = null
    private val binding get() = _binding!!
    private lateinit var orderChatsAdapter: ChatsAdapter
    private val viewModel by viewModel<ChatViewModel>()
    private var chatId: Int = -1
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentOrderChatsBinding.inflate(inflater, container, false)
        initViews()
        observers()
        events()
        return binding.root
    }

//    override fun onResume() {
//        super.onResume()
//        val intentFilter = IntentFilter("com.google.firebase.messaging.DATA_MESSAGE")
//        activity?.registerReceiver(myBroadcastReceiver, intentFilter)

//    }

//    private val myBroadcastReceiver = object : BroadcastReceiver() {
//        override fun onReceive(context: Context, intent: Intent) {
//            chatId = intent.getIntExtra(Constants.CHAT_ID, -1)
//            if (chatId != -1) {
//                getSupportChatHistory(chatId)
//            }
//        }
//    }

//    override fun onPause() {
//        super.onPause()
//        activity?.unregisterReceiver(myBroadcastReceiver)
//    }

    fun initViews() {
        val bundle = arguments ?: return
        val args = SupportChatsFragmentArgs.fromBundle(bundle)
        chatId = args.chatId
        setChatsAdapter()
        if (chatId != -1) {
            getSupportChats()
        }
        binding.toolbar.tvTitle.text = resources.getText(com.techcubics.resources.R.string.chat)
        binding.rvChats.visibility = View.GONE
    }

    private fun getSupportChats() {
        lifecycleScope.launch {
            viewModel.chatIntent.send(
                ChatIntent.GetSupportChatsIntent(chatId, true)
            )
        }
    }

    private fun observers() {
        lifecycleScope.launch {
            viewModel.state.collect {
                binding.includeChatSendBox.btnSend.isEnabled = true
                showHidePlaceHolder(show = false)
                binding.loading.root.visibility = View.GONE
                when (it) {
                    is ChatState.Idle -> {}
                    is ChatState.ProgressLoading -> {
                        binding.loading.root.visibility = View.VISIBLE
                    }

                    is ChatState.ViewSupportChats -> {
                        chatId = it.chatResponse.conversation?.conversationId!!
                        showData(it.chatResponse.messages)
                    }

                    is ChatState.Error -> {
                        if(!it.error!!.contains(Constants.unauthenticated)){
                            Helper.showErrorDialog(requireContext(),it.error?:"")
                        }
                    }

                    is ChatState.ServerError -> {
                        showHidePlaceHolder(true, LottieIconEnum.Error, it.error)
                    }

                    is ChatState.SendSupportChatMessageResponse -> {}

                    else -> {}
                }
            }
        }
    }

    private fun events() {
        binding.toolbar.mainToolbar.setNavigationOnClickListener {
            findNavController().popBackStack()
        }
        binding.includeChatSendBox.btnSend.setOnClickListener {
            if (binding.includeChatSendBox.txtMessage.text.isNotEmpty() && binding.includeChatSendBox.txtMessage.text.isNotBlank()) {
                binding.includeChatSendBox.btnSend.isEnabled = false
                sendMessage(binding.includeChatSendBox.txtMessage.text.trim().toString())
            }
        }
    }

    private fun sendMessage(msg: String) {
        binding.includeChatSendBox.txtMessage.setText("")
        lifecycleScope.launch {
            val request = SendMessageRequest(chatId, msg)
            viewModel.chatIntent.send(
                ChatIntent.SendSupportChatMessageIntent(request)
            )
        }
    }

    private fun showData(chats: MutableList<SupportChat>?) {
        if (chats.isNullOrEmpty()) {
            binding.rvChats.visibility = View.GONE
            showHidePlaceHolder(
                true, LottieIconEnum.Empty,
                getString(com.techcubics.resources.R.string.message_empty_list_general)
            )

        } else {
            binding.rvChats.visibility = View.VISIBLE
            showHidePlaceHolder(false)
            orderChatsAdapter.updateChats(chats)
            binding.rvChats.postDelayed({
                binding.rvChats.scrollToPosition(0)
            }, 100)
        }
    }

    private fun setChatsAdapter() {
        orderChatsAdapter = ChatsAdapter()
        binding.rvChats.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.VERTICAL, true)
        binding.rvChats.adapter = orderChatsAdapter
    }

    private fun showHidePlaceHolder(
        show: Boolean,
        type: LottieIconEnum? = null,
        message: String? = null
    ) {

        if (show) {
            binding.placeholder.root.visibility = View.VISIBLE
            when (type) {
                LottieIconEnum.Empty -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.resources.R.raw.empty_box_lottie)
                    binding.placeholder.tvMessage.text = message
                }

                LottieIconEnum.Error -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.resources.R.raw.lottie_error)
                    binding.placeholder.tvMessage.text = message
                }

                else -> throw IllegalStateException("error")
            }
        } else {

            binding.placeholder.root.visibility = View.GONE
        }
    }


    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }

}
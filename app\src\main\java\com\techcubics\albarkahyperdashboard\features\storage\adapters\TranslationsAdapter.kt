package com.techcubics.albarkahyperdashboard.features.storage.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyperdashboard.databinding.ItemTranslationBinding
import com.techcubics.albarkahyperdashboard.features.storage.adapters.holders.TranslationViewHolder
import com.techcubics.domain.storage.models.Translations

class TranslationsAdapter(private var translations: List<Translations>) :
    RecyclerView.Adapter<TranslationViewHolder>() {

    fun updateTranslations(translations: List<Translations>) {
        this.translations = translations
        notifyItemRangeChanged(0, itemCount)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TranslationViewHolder {
        return TranslationViewHolder(
            ItemTranslationBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }

    override fun onBindViewHolder(holder: TranslationViewHolder, position: Int) {
        holder.hideDivider(translations.size - 1 == position)
        holder.setData(translations[position])
    }

    override fun getItemCount(): Int {
        return translations.size
    }
}

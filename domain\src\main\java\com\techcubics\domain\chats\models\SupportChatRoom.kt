package com.techcubics.domain.chats.models

import com.google.gson.annotations.SerializedName
import com.techcubics.domain.orders.models.Customer

data class SupportChatRoom(
    @SerializedName("conversation_id") override var conversationId: Int? = null,
    @SerializedName("sender_info") override var customer: Customer? = Customer(),
    @SerializedName("receiver_info") var admin: AdminInfo? = AdminInfo(),
    @SerializedName("message_not_read") override var messageNotRead: Int? = null,
    @SerializedName("last_message") override var lastMessage: String? = null,
    @SerializedName("date_time") var dateTime: String? = null,
    @SerializedName("format_date") override var formatDate: String? = null,
    @SerializedName("format_date_1") var formatDate1: String? = null
) : ChatResponse

interface ChatResponse {
    var conversationId: Int?
    var customer: Customer?
    var lastMessage: String?
    var formatDate: String?
    var messageNotRead: Int?
}

data class AdminInfo(

    @SerializedName("id") var id: Int? = null,
    @SerializedName("name") var name: String? = null,
    @SerializedName("phone") var phone: String? = null,
    @SerializedName("email") var email: String? = null,
    @SerializedName("avatar") var avatar: String? = null

)
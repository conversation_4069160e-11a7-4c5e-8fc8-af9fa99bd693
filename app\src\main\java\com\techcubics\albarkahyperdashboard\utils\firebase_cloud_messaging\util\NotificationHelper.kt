package com.techcubics.albarkahyperdashboard.utils.firebase_cloud_messaging.util

import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.graphics.BitmapFactory
import android.os.Bundle
import androidx.core.app.NotificationCompat
import androidx.navigation.NavDeepLinkBuilder
import com.techcubics.resources.R


object NotificationHelper {


//    private const val TAG = "NotificationHelper"

    fun show(mContext: Context, notificationObject: NotificationObject) {

//        val c = NotificationChannel(
//            mContext.getString(R.string.fcm_notfication_channel_id),
//            "channel1",
//            NotificationManager.IMPORTANCE_HIGH
//        )
//        c.description = mContext.getString(R.string.fcm_notfication_channel_description)
//
//        val m: NotificationManager = mContext.getSystemService(NotificationManager::class.java)
//        m.createNotificationChannel(c)

        val pendingIntent = pendingIntent(mContext, notificationObject)

        val builder = NotificationCompat.Builder(
            mContext,
//            mContext.getString(R.string.fcm_notfication_channel_id)
        )
            .setContentTitle(notificationObject.title)
            .setContentText(notificationObject.body)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setCategory(NotificationCompat.CATEGORY_MESSAGE)
            .setLargeIcon(
                BitmapFactory.decodeResource(
                    mContext.resources,
                    R.mipmap.ic_launcher
                )
            )
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
//            .setStyle(
//                NotificationCompat.BigTextStyle()
//                    .bigText(notificationObject.body).setBigContentTitle(
//                        notificationObject.title
//                    ).setSummaryText(mContext.getString(R.string.fcm_notficiation_channel_summary))
//            )

        builder.setSmallIcon(R.mipmap.ic_launcher)
        builder.color = mContext.getColor(R.color.color_gray_4)

        val mNotificationManager: NotificationManager =
            mContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        mNotificationManager.notify(System.currentTimeMillis().toString(), 1, builder.build())


    }

    private fun pendingIntent(
        context: Context,
        notificationObject: NotificationObject
    ): PendingIntent {

        val bundle = Bundle()
//        val destination = when (notificationObject.info.notificationType) {
//            NotificationTypes.General.value -> {
//                com.techcubics.smartway_android.R.id.homeFragment
//            }
//            else -> {
//                com.techcubics.smartway_android.R.id.notificationsFragment
//            }

//        }



        return NavDeepLinkBuilder(context)
//            .setComponentName(MainActivity::class.java)
//            .setGraph(com.techcubics.smartway_android.R.navigation.main_nav_graph)
//            .setDestination(destination, bundle)
            .createPendingIntent()
    }


}
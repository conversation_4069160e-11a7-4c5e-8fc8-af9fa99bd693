<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_dialog"
    android:clipToPadding="true"
    android:elevation="16dp"
    android:paddingHorizontal="16dp"
    android:paddingVertical="24dp"
    android:paddingBottom="10dp"
    android:theme="@style/Theme.TextInputLayout"
    app:behavior_hideable="false"
    app:behavior_peekHeight="0dp"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    tools:context=".features.storage.fragments.tabs.product.CodedProductsFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:textAlignment="viewStart"
            android:theme="@style/LargeBoldDarkText"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="لاب توب" />

        <LinearLayout
            android:id="@+id/cat_cont"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="3dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="@id/tv_title"
            app:layout_constraintTop_toBottomOf="@id/tv_title">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="5dp"
                app:cardCornerRadius="50dp">

                <ImageView
                    android:id="@+id/cat_image"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:contentDescription="@string/img_cnt_desc"
                    android:src="@drawable/portrait_contactus" />
            </com.google.android.material.card.MaterialCardView>

            <TextView
                android:id="@+id/category"
                style="@style/SmallMediumDarkText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="جبن" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/subcat_cont"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginStart="16dp"
            android:layout_marginBottom="3dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="@id/cat_cont"
            app:layout_constraintStart_toEndOf="@id/cat_cont"
            app:layout_constraintTop_toTopOf="@id/cat_cont">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="5dp"
                app:cardCornerRadius="50dp">

                <ImageView
                    android:id="@+id/subcat_image"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:contentDescription="@string/img_cnt_desc"
                    android:src="@drawable/portrait_contactus" />
            </com.google.android.material.card.MaterialCardView>

            <TextView
                android:id="@+id/subcategory"
                style="@style/SmallMediumDarkText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="جبن1" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/cont"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cat_cont">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/owner_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:layout_weight="1"
                android:hint="@string/owner_name"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp">

                <AutoCompleteTextView
                    android:id="@+id/owner_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/shop_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:layout_weight="1"
                android:hint="@string/shop_name"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp">

                <AutoCompleteTextView
                    android:id="@+id/shop_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>
        </LinearLayout>


        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/price_cont"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="6dp"
            android:hint="@string/price"
            app:boxCornerRadiusBottomEnd="10dp"
            app:boxCornerRadiusBottomStart="10dp"
            app:boxCornerRadiusTopEnd="10dp"
            app:boxCornerRadiusTopStart="10dp"
            app:layout_constraintTop_toBottomOf="@id/cont">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/price"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="numberDecimal"
                android:imeOptions="actionDone"
                android:textAppearance="@style/SmallRegularDarkText"
                tools:ignore="LabelFor" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/min_qty_cont"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="6dp"
            android:hint="@string/min_num"
            app:boxCornerRadiusBottomEnd="10dp"
            app:boxCornerRadiusBottomStart="10dp"
            app:boxCornerRadiusTopEnd="10dp"
            app:boxCornerRadiusTopStart="10dp"
            app:layout_constraintEnd_toStartOf="@id/max_qty_cont"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/price_cont">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/min_qty"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="number"
                android:imeOptions="actionDone"
                android:textAppearance="@style/SmallRegularDarkText"
                tools:ignore="LabelFor" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/max_qty_cont"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="6dp"
            android:hint="@string/max_num"
            app:boxCornerRadiusBottomEnd="10dp"
            app:boxCornerRadiusBottomStart="10dp"
            app:boxCornerRadiusTopEnd="10dp"
            app:boxCornerRadiusTopStart="10dp"
            app:layout_constraintBottom_toBottomOf="@id/min_qty_cont"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/min_qty_cont"
            app:layout_constraintTop_toTopOf="@id/min_qty_cont">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/max_qty"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="number"
                android:imeOptions="actionDone"
                android:textAppearance="@style/SmallRegularDarkText"
                tools:ignore="LabelFor" />

        </com.google.android.material.textfield.TextInputLayout>

        <include
            android:id="@+id/add_product"
            layout="@layout/btn_search_progress"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_marginVertical="6dp"
            android:layout_marginHorizontal="6dp"
            android:layout_marginBottom="100dp"
            app:layout_constraintTop_toBottomOf="@id/min_qty_cont" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>

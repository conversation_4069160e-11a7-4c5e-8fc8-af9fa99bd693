package com.techcubics.albarkahyperdashboard.utils.components

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.lifecycle.MutableLiveData
import com.techcubics.albarkahyperdashboard.R
import com.techcubics.data.bills.utils.BillsStatusEnum

class BottomSheetAlertDialog {

    private lateinit var dialog: Dialog
    private lateinit var mcontext: Context

    fun init(context: Context) {
        dialog = Dialog(context)
       mcontext = context
    }

    @SuppressLint("ResourceType")
    fun showDialog(data: BottomSheetData, popupMenuItemLiveData: MutableLiveData<String?>){
        dialog.setContentView(R.layout.bottomsheet_layout)
        val customerLayout : LinearLayout = dialog.findViewById(R.id.customer_layout)
        val header : TextView = dialog.findViewById(R.id.header)
        val customerName : TextView = dialog.findViewById(R.id.customer_name)
        val facilityName : TextView = dialog.findViewById(R.id.facility_name)
        val phone : TextView = dialog.findViewById(R.id.phone)
        val address : TextView = dialog.findViewById(R.id.address)
        val close : LinearLayout = dialog.findViewById(R.id.close)
        val statusLayout : LinearLayout = dialog.findViewById(R.id.status_layout)
        val facilitySname : TextView = dialog.findViewById(R.id.facility_sname)
        val delivered : RadioButton = dialog.findViewById(R.id.delivered)
        val refused : RadioButton = dialog.findViewById(R.id.refused)
        val cancelled : RadioButton = dialog.findViewById(R.id.cancelled)

        if(data.TAG.equals("Customer")){
            customerLayout.visibility = View.VISIBLE
            statusLayout.visibility = View.GONE
            header.text = data.customer_data
            customerName.text = data.customer_name
            facilityName.text = data.facility_name
            phone.text = data.phone
            phone.setTextColor(ContextCompat.getColor(mcontext, com.techcubics.resources.R.color.color_blue_4))
            address.text = data.address
        }else if(data.TAG.equals("Status")){
            customerLayout.visibility = View.GONE
            statusLayout.visibility = View.VISIBLE
            facilitySname.text = data.facility_name
            checkRedioEvents(data.order_id,delivered,refused,cancelled,popupMenuItemLiveData)
        }

        close.setOnClickListener {
            onDismiss()
        }
        dialog.show()
        dialog.window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT,ViewGroup.LayoutParams.WRAP_CONTENT)
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog.window?.attributes?.windowAnimations = com.techcubics.resources.R.style.dialog_animation
        dialog.window?.setGravity(Gravity.BOTTOM)
        if (dialog.window != null) {
            dialog.setCancelable(true)

        }

    }

    private fun checkRedioEvents(
        orderId: String?,
        delivered: RadioButton,
        refused: RadioButton,
        cancelled: RadioButton,
        popupMenuItemLiveData: MutableLiveData<String?>
    ) {
        delivered.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                popupMenuItemLiveData.postValue(BillsStatusEnum.Delivered.value+","+orderId)
                onDismiss()
            }
        }
        refused.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                popupMenuItemLiveData.postValue(BillsStatusEnum.Returned.value+","+orderId)
                onDismiss()
            }
        }
        cancelled.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                popupMenuItemLiveData.postValue(BillsStatusEnum.Canceled.value+","+orderId)
                onDismiss()
            }
        }
    }

    fun onDismiss():Boolean{
        dialog.dismiss()
        return true

    }
}
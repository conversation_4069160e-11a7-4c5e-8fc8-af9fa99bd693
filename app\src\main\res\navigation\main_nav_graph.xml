<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_nav_graph"
    app:startDestination="@id/billStatusFragment">

    <fragment
        android:id="@+id/loginFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.auth.fragments.LoginFragment"
        android:label="fragment_login"
        tools:layout="@layout/fragment_login" />
    <action
        android:id="@+id/nav_to_forgetpass"
        app:destination="@id/forgetPasswordFragment" />
    <fragment
        android:id="@+id/forgetPasswordFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.auth.fragments.ForgetPasswordFragment"
        android:label="fragment_forget_password"
        tools:layout="@layout/fragment_forget_password" />
    <fragment
        android:id="@+id/billStatusFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.bills.fragments.BillsStatusFragment"
        android:label="fragment_orders_status"
        tools:layout="@layout/fragment_bills_status">
    </fragment>
    <fragment
        android:id="@+id/storageFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.storage.fragments.StorageFragment"
        android:label="fragment_store"
        tools:layout="@layout/fragment_storage">
        <action
            android:id="@+id/view_productDetailsFragment"
            app:destination="@id/productDetailsFragment" />
        <action
            android:id="@+id/view_discountDetailsFragment"
            app:destination="@id/discountDetailsFragment" />
        <action
            android:id="@+id/view_codedProducts"
            app:destination="@+id/codedProductsFragment" />
        <action
            android:id="@+id/view_addNewDiscountFragment"
            app:destination="@id/addNewDiscountFragment" />
    </fragment>
    <fragment
        android:id="@+id/chatOrderRoomsFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.chats.fragments.ChatOrderRoomsFragment"
        android:label="fragment_chat_order_rooms"
        tools:layout="@layout/fragment_chat_order_rooms">
        <action
            android:id="@+id/view_orderChatsFragment"
            app:destination="@id/orderChatsFragment" />
    </fragment>
    <fragment
        android:id="@+id/moreFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.more.fragments.MoreFragment"
        android:label="fragment_profile"
        tools:layout="@layout/fragment_more">
        <action
            android:id="@+id/toAccountSettingFragment"
            app:destination="@id/accountSettingsFragment" />
        <action
            android:id="@+id/view_chatSupportRoomsFragment"
            app:destination="@id/chatSupportRoomsFragment" />
        <action
            android:id="@+id/toOwnersFragment"
            app:destination="@id/ownersFragment" />

    </fragment>
    <action
        android:id="@+id/toLogin"
        app:destination="@id/loginFragment" />
    <action
        android:id="@+id/toOrders"
        app:destination="@id/billStatusFragment" />
    <fragment
        android:id="@+id/accountSettingsFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.more.fragments.AccountSettingsFragment"
        android:label="AccountSettingsFragment" />
    <fragment
        android:id="@+id/youtubeFullScreenFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.storage.fragments.tabs.product.YoutubeFullScreenFragment"
        android:label="fragment_youtube_full_screen"
        tools:layout="@layout/fragment_youtube_full_screen">
        <argument
            android:name="seconds"
            app:argType="float"
            android:defaultValue="0" />
        <argument
            android:name="vidId"
            app:argType="string" />
    </fragment>
    <fragment
        android:id="@+id/productDetailsFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.storage.fragments.tabs.product.ProductDetailsFragment"
        android:label="ProductDetailsFragment">
        <action
            android:id="@+id/view_youtubeFullScreenFragment"
            app:destination="@id/youtubeFullScreenFragment" />
        <argument
            android:name="productId"
            app:argType="integer" />
    </fragment>
    <fragment
        android:id="@+id/addNewProductFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.storage.fragments.tabs.product.AddNewProductFragment"
        android:label="fragment_add_new_product"
        tools:layout="@layout/fragment_add_new_product" >
        <argument
            android:name="product"
            app:argType="com.techcubics.domain.storage.models.Product"
            app:nullable="true" />
    </fragment>
    <action android:id="@+id/view_AddNewProductFragment"
        app:destination="@id/addNewProductFragment" />
    <fragment
        android:id="@+id/orderDetailsFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.bills.fragments.BillDetailsFragment"
        android:label="OrderDetailsFragment" >
        <argument
            android:name="id"
            app:argType="string"
            android:defaultValue="-1" />
    </fragment>
    <fragment
        android:id="@+id/codedProductsFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.storage.fragments.tabs.product.CodedProductsFragment"
        android:label="fragment_coded_products"
        tools:layout="@layout/fragment_coded_products" />
    <fragment
        android:id="@+id/discountDetailsFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.storage.fragments.tabs.discount.DiscountDetailsFragment"
        android:label="fragment_discount_details"
        tools:layout="@layout/fragment_discount_details">
        <argument
            android:name="discountId"
            app:argType="integer" />
        <action
            android:id="@+id/view_addNewDiscountFragment"
            app:destination="@id/addNewDiscountFragment" />
    </fragment>
    <fragment
        android:id="@+id/addNewDiscountFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.storage.fragments.tabs.discount.AddNewDiscountFragment"
        android:label="fragment_add_new_discount"
        tools:layout="@layout/fragment_add_new_discount">
        <argument
            android:name="discount"
            app:argType="com.techcubics.domain.storage.models.Discount"
            app:nullable="true" />
    </fragment>
    <fragment
        android:id="@+id/orderChatsFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.chats.fragments.OrderChatsFragment"
        android:label="OrderChatsFragment">
        <argument
            android:name="chatId"
            app:argType="integer" />
        <argument
            android:name="ownerId"
            app:argType="integer" />
        <argument
            android:name="shopId"
            app:argType="integer" />
    </fragment>
    <fragment
        android:id="@+id/reportsFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.reports.fragments.ReportsFragment"
        android:label="fragment_reports"
        tools:layout="@layout/fragment_reports" />
    <fragment
        android:id="@+id/chatSupportRoomsFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.chats.fragments.ChatSupportRoomsFragment"
        android:label="ChatSupportRoomsFragment">
        <action
            android:id="@+id/view_supportChatsFragment"
            app:destination="@id/supportChatsFragment" />
    </fragment>
    <fragment
        android:id="@+id/supportChatsFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.chats.fragments.SupportChatsFragment"
        android:label="SupportChatsFragment">
        <argument
            android:name="chatId"
            app:argType="integer" />
    </fragment>
    <fragment
        android:id="@+id/ownersFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.owner.fragments.OwnersFragment"
        android:label="OwnersFragment" />
    <fragment
        android:id="@+id/newBillsFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.bills.fragments.NewBillsFragment"
        android:label="NewBillsFragment" >
    </fragment>
    <action
        android:id="@+id/viewOrderDetails"
        app:destination="@id/orderDetailsFragment"
        app:popUpToInclusive="true" />
    <fragment
        android:id="@+id/billsPreparingFragment"
        android:name="com.techcubics.albarkahyperdashboard.features.bills.fragments.BillsPreparingFragment"
        android:label="BillsPreparingFragment">
    </fragment>
</navigation>
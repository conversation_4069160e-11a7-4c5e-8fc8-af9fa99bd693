<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.AppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:elevation="0.5dp"
    android:backgroundTint="@color/app_color"
    android:background="@color/app_color">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/main_toolbar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        app:navigationIcon="@drawable/ic_arrow_back_24">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:maxLines="2"
                android:ellipsize="end"
                style="@style/LargeBoldWhiteText"
                android:gravity="start"
                android:textAlignment="viewStart"
                android:textColor="@color/white"
                android:layout_marginHorizontal="10dp"
                tools:text="Title" />

            <ImageButton
                android:id="@+id/btnSearch"
                android:layout_width="@dimen/icons_size_height_weight_9"
                android:layout_height="@dimen/icons_size_height_weight_9"
                android:background="?attr/selectableItemBackground"
                android:src="@drawable/ic_search"
                android:layout_gravity="start|center_vertical"
                android:visibility="gone"
                android:contentDescription="@string/img_cnt_desc" />

        </androidx.appcompat.widget.LinearLayoutCompat>


    </androidx.appcompat.widget.Toolbar>


</com.google.android.material.appbar.AppBarLayout>


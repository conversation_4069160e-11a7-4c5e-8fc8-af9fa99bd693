package com.techcubics.albarkahyperdashboard.features.storage.adapters.holders

import android.content.Context
import androidx.navigation.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyperdashboard.databinding.ItemProductBinding
import com.techcubics.albarkahyperdashboard.features.storage.adapters.ProductsAdapter
import com.techcubics.albarkahyperdashboard.features.storage.fragments.StorageFragmentDirections
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.listeners.OnItemClickListener
import com.techcubics.domain.storage.enums.ProductStatusTypes
import com.techcubics.domain.storage.models.Product
import java.util.Locale

class ProductViewHolder(
    private val binding: ItemProductBinding,
    private val context: Context,
    private val listener: OnItemClickListener
) : RecyclerView.ViewHolder(binding.root) {
   fun setData(product: Product?) {
        product?.icon?.let { Helper.loadImage(context, it, binding.productImage) }
        product?.category?.image?.let { Helper.loadImage(context, it, binding.catImage) }
        product?.shop?.logo?.let { Helper.loadImage(context, it, binding.shopImage) }
        binding.category.text = product?.category?.name
        binding.shopName.text = product?.shop?.name
        binding.pName.text = product?.name
        "Product#${product?.id}".also { binding.pCode.text = it }
        "${
            java.text.NumberFormat.getNumberInstance(Locale.ENGLISH).format(product?.price)
        }${context.getString(com.techcubics.resources.R.string.currency_name)}".also {
            binding.price.text = it
        }

        binding.activeStatus.isChecked = product?.status ?: false
        binding.wantedStatus.isChecked = product?.wanted ?: false
        binding.activeStatus.setOnClickListener {
            product?.id?.let { it1 ->
                listener.onClick(it1, ProductStatusTypes.Active)
            }
            (bindingAdapter as ProductsAdapter).products?.get(bindingAdapterPosition)?.status = binding.activeStatus.isChecked
            (bindingAdapter as ProductsAdapter).notifyItemChanged(bindingAdapterPosition)
        }
        binding.wantedStatus.setOnClickListener {
            product?.id?.let { it1 ->
                listener.onClick(it1, ProductStatusTypes.Wanted)
            }
            (bindingAdapter as ProductsAdapter).products?.get(bindingAdapterPosition)?.wanted = binding.wantedStatus.isChecked
            (bindingAdapter as ProductsAdapter).notifyItemChanged(bindingAdapterPosition)
        }
        binding.root.setOnClickListener {
            product?.productId?.let { id ->
                val action = StorageFragmentDirections.viewProductDetailsFragment(id)
                it.findNavController().navigate(action)
            }

        }
        binding.edit.setOnClickListener {
            product?.productId?.let { id ->
                val action = StorageFragmentDirections.viewProductDetailsFragment(id)
                it.findNavController().navigate(action)
            }
        }
    }
}
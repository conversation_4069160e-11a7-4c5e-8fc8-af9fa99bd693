package com.techcubics.albarkahyperdashboard.utils.components.general

import androidx.fragment.app.FragmentActivity
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.YouTubePlayer
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.listeners.AbstractYouTubePlayerListener
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.listeners.YouTubePlayerCallback
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.listeners.YouTubePlayerFullScreenListener
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.listeners.YouTubePlayerListener
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.options.IFramePlayerOptions
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.utils.loadOrCueVideo
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.views.YouTubePlayerView
import com.pierfrancescosoffritti.androidyoutubeplayer.core.ui.DefaultPlayerUiController

class YouTubeHelper(
    private val activity: FragmentActivity,
    private val videoId: String,
    private val youtubeView: YouTubePlayerView,
    private var currentSeconds: Float = 0f
) {


    private fun setLifecycleObserver() {
        activity.lifecycle.addObserver(youtubeView)
    }

    private fun setYoutubeListener(isLoad: Boolean): YouTubePlayerListener {
        return object : AbstractYouTubePlayerListener() {
            override fun onReady(youTubePlayer: YouTubePlayer) {
                val defaultPlayerUiController =
                    DefaultPlayerUiController(youtubeView, youTubePlayer)
                youtubeView.setCustomPlayerUi(defaultPlayerUiController.rootView)
                if (isLoad){
                    youTubePlayer.loadOrCueVideo(activity.lifecycle,videoId, currentSeconds)
                }else {
                    youTubePlayer.cueVideo(videoId, currentSeconds)
                }
            }

            override fun onCurrentSecond(youTubePlayer: YouTubePlayer, second: Float) {
                super.onCurrentSecond(youTubePlayer, second)
                currentSeconds = second
            }
        }
    }

    private fun setOptions(): IFramePlayerOptions {
        return IFramePlayerOptions.Builder().controls(0).build()
    }

    fun initialize(isLoad:Boolean) {
        setLifecycleObserver()
        youtubeView.initialize(setYoutubeListener(isLoad), setOptions())
    }

    fun setCurrentSeconds(seconds: Float) {
        this.currentSeconds = seconds
    }

    fun setCurrentSeekTime() {
        youtubeView.getYouTubePlayerWhenReady(object :
            YouTubePlayerCallback {
            override fun onYouTubePlayer(youTubePlayer: YouTubePlayer) {
                youTubePlayer.play()
                youTubePlayer.seekTo(<EMAIL>)
            }
        })
    }

    fun setFullScreenListener(fullScreen: ((Float) -> Unit)?=null, exitFullScreen:((Float)->Unit)?=null) {
         youtubeView.addFullScreenListener(object :
            YouTubePlayerFullScreenListener {
            override fun onYouTubePlayerEnterFullScreen() {
                fullScreen?.let { it(currentSeconds) }
            }

            override fun onYouTubePlayerExitFullScreen() {
                exitFullScreen?.let { it(currentSeconds) }
            }

        })
    }
}
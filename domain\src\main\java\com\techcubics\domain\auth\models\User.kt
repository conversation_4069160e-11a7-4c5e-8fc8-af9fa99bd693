package com.techcubics.domain.auth.models

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.techcubics.domain.orders.models.Governorate
import kotlinx.parcelize.Parcelize

@Parcelize
data class LoginResponse(
    @SerializedName("id") var id: Int? = null,
    @SerializedName("name") val name: String? = null,
    @SerializedName("email") val email: String? = null,
    @SerializedName("user") var user: User? = User(),
    @SerializedName("address") var address: String? = null,
    @SerializedName("brand_name") var brandName: String? = null,
    @SerializedName("phone") var phone: String? = null,
    @SerializedName("token") var token: String? = null,
    @SerializedName("expires_at") var expiresAt: String? = null,
    @SerializedName("avatar") val avatar: String? = null
) : Parcelable

@Parcelize
data class User(
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("email")
    val email: String? = null,
    @SerializedName("phone")
    val phone: String? = null,
    @SerializedName("avatar")
    val avatar: String? = null,
    @SerializedName("expires_at")
    val expiresAt: String? = null,
    @SerializedName("id")
    val id: Int? = null,
    @SerializedName("token")
    val token: String? = null
) : Parcelable



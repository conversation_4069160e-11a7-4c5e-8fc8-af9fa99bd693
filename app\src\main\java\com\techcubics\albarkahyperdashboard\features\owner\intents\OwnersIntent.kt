package com.techcubics.albarkahyperdashboard.features.owner.intents

import com.techcubics.domain.orders.requests.OrdersFilterRequest
import com.techcubics.domain.owner.requests.OwnerFilterRequest
import com.techcubics.domain.owner.requests.OwnerRequest
import com.techcubics.domain.storage.requests.ProductsFilter


sealed class OwnersIntent {
    data class OwnersFilter(val page: Int, val request: OwnerFilterRequest) : OwnersIntent()
    data class GetOwners(val page : Int) : OwnersIntent()
    data class GetOwnerDetails(val id : String) : OwnersIntent()
    data class ChangeOwnerStatus(val id : String) : OwnersIntent()
    data class UpdateOwner(val id : String,val request : OwnerRequest) : OwnersIntent()
    data class CreateOwner(val request: OwnerRequest) : OwnersIntent()
}
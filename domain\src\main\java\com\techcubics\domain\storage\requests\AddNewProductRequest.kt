package com.techcubics.domain.storage.requests

import com.google.gson.annotations.SerializedName
import com.techcubics.domain.storage.models.AddColor
import com.techcubics.domain.storage.models.AddSize
import okhttp3.MultipartBody
import okhttp3.RequestBody
import java.io.File

data class AddNewProduct(
    var productId:Int? =null,
    @SerializedName("category_id") var categoryId: Int?=null,
    @SerializedName("sub_category_id") var subCategoryId: Int?=null,
    @SerializedName("owner_id") var ownerId: Int?=null,
    @SerializedName("shop_id") var shopId: Int?=null,
    @SerializedName("price") var price: String?=null,
    @SerializedName("minimum_order_number") var minimumOrderNumber: String?=null,
    @SerializedName("maximum_order_number") var maximumOrderNumber: String?=null,
    @SerializedName("ar[name]") var nameAr: String?=null,
    @SerializedName("ar[description]") var descriptionAr: String?=null,
    @SerializedName("en[name]") var nameEn: String?=null,
    @SerializedName("en[description]") var descriptionEn: String?=null,
    @SerializedName("video") var video: String?=null,
    @SerializedName("sizes") var productSizes: ArrayList<AddSize> ?=null,
    @SerializedName("colors") var productColors: ArrayList<AddColor>?=null,
    var images: ArrayList<File>? = null,
    var icon: File? = null
)

data class AddNewProductRequest(
    var productId:Int? =null,
    @SerializedName("category_id") var categoryId: RequestBody? = null,
    @SerializedName("sub_category_id") var subCategoryId: RequestBody? = null,
    @SerializedName("owner_id") var ownerId: RequestBody? = null,
    @SerializedName("shop_id") var shopId: RequestBody? = null,
    @SerializedName("price") var price: RequestBody? = null,
    @SerializedName("minimum_order_number") var minimumOrderNumber: RequestBody? = null,
    @SerializedName("maximum_order_number") var maximumOrderNumber: RequestBody? = null,
    @SerializedName("ar[name]") var nameAr: RequestBody? = null,
    @SerializedName("ar[description]") var descriptionAr: RequestBody? = null,
    @SerializedName("en[name]") var nameEn: RequestBody? = null,
    @SerializedName("en[description]") var descriptionEn: RequestBody? = null,
    @SerializedName("video") var video: RequestBody? = null,
    @SerializedName("sizes") var productSizes: HashMap<String,String?>?=null,
    @SerializedName("colors") var productColors: HashMap<String,String?> ?=null,
    var images: ArrayList<MultipartBody.Part>? = null,
    var icon: MultipartBody.Part? = null
)

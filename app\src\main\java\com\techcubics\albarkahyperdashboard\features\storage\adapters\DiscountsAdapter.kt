package com.techcubics.albarkahyperdashboard.features.storage.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView.Adapter
import com.techcubics.albarkahyperdashboard.databinding.ItemDiscountBinding
import com.techcubics.albarkahyperdashboard.features.storage.adapters.holders.DiscountViewHolder
import com.techcubics.albarkahyperdashboard.utils.listeners.OnItemClickListener
import com.techcubics.domain.storage.models.Discount
import com.techcubics.domain.storage.models.Product

class DiscountsAdapter(
    var discounts: MutableList<Discount>?,
    private val listener: OnItemClickListener
    ) : Adapter<DiscountViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DiscountViewHolder {
            val binding =
                ItemDiscountBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            return   DiscountViewHolder(binding, parent.context, listener)
    }

    override fun getItemCount(): Int {
        return discounts?.size ?: 0
    }

    override fun onBindViewHolder(holder: DiscountViewHolder, position: Int) {
            holder.setData(discounts?.get(position))
    }

    fun updateDiscounts(discounts: MutableList<Discount>?) {
        val diffResult = DiffUtil.calculateDiff(DiscountsDiffUtilCallback(this.discounts, discounts))
        this.discounts?.clear()
        discounts?.let { this.discounts?.addAll(it) }
        diffResult.dispatchUpdatesTo(this)
    }
}

class DiscountsDiffUtilCallback(
    private val oldList: List<Discount>?,
    private val newList: List<Discount>?
) : DiffUtil.Callback() {
    override fun getOldListSize(): Int {
        return oldList?.size?:0
    }

    override fun getNewListSize(): Int {
        return newList?.size?:0
    }

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return oldList?.get(oldItemPosition)?.id == newList?.get(newItemPosition)?.id
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return when (oldList?.size) {
            newList?.size -> true
            else -> false
        }
    }

    override fun getChangePayload(oldItemPosition: Int, newItemPosition: Int): Any {
        return when (oldItemPosition) {
            newItemPosition -> true
            else -> false
        }
    }

}
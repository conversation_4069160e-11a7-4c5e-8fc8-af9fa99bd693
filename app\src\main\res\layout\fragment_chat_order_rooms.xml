<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:theme="@style/Theme.Colored"
    android:background="@color/color_gray_3"
    tools:context=".features.chats.fragments.ChatOrderRoomsFragment">

    <include
        android:id="@+id/toolbar_title"
        layout="@layout/toolbar_title"
        android:elevation="16dp"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/cont"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar_title">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/owner_cont"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="6dp"
            android:layout_weight="1"
            android:hint="@string/owner_name"
            app:boxBackgroundColor="@color/white"
            app:boxCornerRadiusBottomEnd="10dp"
            app:boxCornerRadiusBottomStart="10dp"
            app:boxCornerRadiusTopEnd="10dp"
            app:boxCornerRadiusTopStart="10dp">

            <AutoCompleteTextView
                android:id="@+id/owner_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:completionThreshold="1"
                android:ellipsize="end"
                android:gravity="start"
                android:inputType="none"
                android:maxLines="1"
                android:textAlignment="viewStart"
                android:textAppearance="@style/SmallRegularDarkText"
                tools:ignore="LabelFor" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/shop_cont"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="6dp"
            android:layout_weight="1"
            android:hint="@string/shop_name"
            app:boxBackgroundColor="@color/white"
            app:boxCornerRadiusBottomEnd="10dp"
            app:boxCornerRadiusBottomStart="10dp"
            app:boxCornerRadiusTopEnd="10dp"
            app:boxCornerRadiusTopStart="10dp">

            <AutoCompleteTextView
                android:id="@+id/shop_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:inputType="none"
                android:maxLines="1"
                android:textAppearance="@style/SmallRegularDarkText"
                tools:ignore="LabelFor" />

        </com.google.android.material.textfield.TextInputLayout>
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_chat_rooms"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_margin="5dp"
        app:layout_constraintBottom_toTopOf="@id/pagingLoadingImg"
        app:layout_constraintTop_toBottomOf="@id/cont"
        tools:listitem="@layout/item_chat_room" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/pagingLoadingImg"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:visibility="gone"
        app:layout_anchorGravity="bottom"
        app:layout_constraintBottom_toBottomOf="parent"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_rawRes="@raw/lottie_pager_loading" />

    <include
        android:id="@+id/msg_layout"
        layout="@layout/include_placeholder"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <include
        android:id="@+id/loading"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
package com.techcubics.domain.storage.usecases

import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.storage.enums.ProductAction
import com.techcubics.domain.storage.enums.ProductStatusTypes
import com.techcubics.domain.storage.models.Discount
import com.techcubics.domain.storage.models.Product
import com.techcubics.domain.storage.repositories.StorageRepo
import com.techcubics.domain.storage.requests.AddNewDiscount
import com.techcubics.domain.storage.requests.ProductsFilter
import com.techcubics.domain.storage.requests.ProductsFilterRequestBody

class GetDiscountsUseCase(private val storageRepo: StorageRepo) {
    suspend operator fun invoke(
        page: Int,
        filter: ProductsFilter
    ): BaseResponse<MutableList<Discount>>? {
        val request = ProductsFilterRequestBody(filter.sort, filter.search, filter.paginator)
        return storageRepo.getDiscounts(page, request)
    }

}

class GetDiscountDetailsUseCase(private val storageRepo: StorageRepo) {
    suspend operator fun invoke(discountId: Int) = storageRepo.getDiscountDetails(discountId)
}

class StoreDiscountUseCase(private val storageRepo: StorageRepo) {
    suspend operator fun invoke(
        request: AddNewDiscount,
        productAction: ProductAction
    ): BaseResponse<Discount>? {
        return if (productAction == ProductAction.Add) {
            storageRepo.storeDiscount(request)
        } else {
            storageRepo.updateDiscount(request)
        }
    }
}

class SetDiscountStatusUseCase(private val storageRepo: StorageRepo) {
    suspend operator fun invoke(id: Int) = storageRepo.setDiscountActiveStatus(id)

}
class GetProductsByOwnerAndShopIdUseCase(private val storageRepo: StorageRepo) {
    suspend operator fun invoke(ownerId: Int,shopId:Int) = storageRepo.getProductsByOwnerAndShopId(ownerId,shopId)

}

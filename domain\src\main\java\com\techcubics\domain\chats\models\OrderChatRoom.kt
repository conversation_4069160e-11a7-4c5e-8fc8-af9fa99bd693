package com.techcubics.domain.chats.models

import com.google.gson.annotations.SerializedName
import com.techcubics.domain.orders.models.Customer
import com.techcubics.domain.orders.models.Order

data class OrderChatRoom(
    @SerializedName("conversation_id") override var conversationId: Int? = null,
    @SerializedName("sender_id") var senderId: Int? = null,
    @SerializedName("receiver_id") var receiverId: Int? = null,
    @SerializedName("order") var order: Order? = Order(),
    @SerializedName("customer") override var customer: Customer? = Customer(),
    @SerializedName("last_time_message") var lastTimeMessage: String? = null,
    @SerializedName("last_time") override var formatDate: String? = null,
    @SerializedName("last_message") override var lastMessage: String? = null,
    @SerializedName("count_unread") override var messageNotRead: Int? = null
) : ChatResponse

package com.techcubics.albarkahyperdashboard.features.storage.intents

import com.techcubics.domain.orders.requests.DeleteImageRequest
import com.techcubics.domain.storage.enums.ProductAction
import com.techcubics.domain.storage.enums.ProductStatusTypes
import com.techcubics.domain.storage.requests.AddNewProduct
import com.techcubics.domain.storage.requests.ProductsFilter

sealed class ProductIntent {
    data class GetProducts(val page: Int, val filter: ProductsFilter) : ProductIntent()
    object GetOwners : ProductIntent()
    data class GetShopsByOwnerId(val ownerId: Int) : ProductIntent()
    object GetCategories : ProductIntent()
    data class GetSubCategoriesByCatId(val catId: Int) : ProductIntent()
    data class GetProductDetails(val productId: Int,val isOwner:Int?=null) : ProductIntent()
    data class SetStatus(val productId: Int, val type: ProductStatusTypes) : ProductIntent()
    data class StoreUpdateProduct(val request: AddNewProduct, val action: ProductAction) :
        ProductIntent()

    data class DeleteImagesAndUpdateProduct(
        val deleteImageRequest: DeleteImageRequest,
        val request: AddNewProduct,
        val action: ProductAction
    ) : ProductIntent()
}

<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/tvCustomLayout"
    app:cardCornerRadius="12dp"
    android:backgroundTint="@color/color_red_4"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="wrap_content"
    android:elevation="10dp">
    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_height="match_parent">
        <TextView
            android:id="@+id/message"
            android:layout_width="0dp"
            android:layout_weight="1.6"
            android:layout_marginStart="8dp"
            android:layout_height="wrap_content"
            tools:text="خخخخخخخخخخخخخممممممخخخخخلا يمكنك تغيير حالة الطلب"
            android:textColor="@color/white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/img"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginEnd="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            android:textAppearance="@style/Medium2BoldDarkText"/>
        <ImageView
            android:layout_width="0dp"
            android:layout_weight="0.4"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:rotation="30"
            android:id="@+id/img"
            android:layout_height="85dp"
            android:src="@drawable/ic_alert"
            />
    </LinearLayout>



</com.google.android.material.card.MaterialCardView>
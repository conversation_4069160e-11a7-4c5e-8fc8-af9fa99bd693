package com.techcubics.albarkahyperdashboard.features.storage.adapters.holders

import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyperdashboard.databinding.ItemCodedProductBinding
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.listeners.OnItemClickListener
import com.techcubics.domain.storage.models.Product

class CodedProductsViewHolder(
    private val binding: ItemCodedProductBinding,
    private val context: Context,
    private val listener: OnItemClickListener
) : RecyclerView.ViewHolder(binding.root) {
    fun setData(product: Product?) {
        product?.icon?.let { Helper.loadImage(context, it, binding.productImage) }
        product?.category?.image?.let { Helper.loadImage(context, it, binding.catImage) }
        binding.category.text = product?.category?.name
        binding.pName.text = product?.name
        binding.addProduct.setOnClickListener {
            product?.productId?.let { it1 -> listener.onClick(it1,"") }
        }
    }
}
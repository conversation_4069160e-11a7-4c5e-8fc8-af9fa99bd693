package com.techcubics.domain.auth.usecases

import com.techcubics.domain.auth.repositories.AuthRepo
import com.techcubics.domain.auth.requests.*
import com.techcubics.domain.auth.requests.LoginRequest

class LoginUseCase(private val repo: AuthRepo) {
    suspend operator fun invoke(loginRequest : LoginRequest,userType : String) = repo.login(loginRequest,userType)
}
class ForgetPasswordUseCse(private val repo: AuthRepo) {
    suspend operator fun invoke(request : ForgetPasswordByEmailRequest) = repo.forgetPasswordEmailCall(request)
}
class UpdatePasswordUseCse(private val repo: AuthRepo) {
    suspend operator fun invoke(request : UpdatePasswordRequest) = repo.updatePasswordResetCall(request)
}

class LogoutUseCase(private val repo: AuthRepo){
    suspend operator fun invoke() = repo.logout()
}

class DeleteAccountUseCase(private val repo: AuthRepo){
    suspend operator fun invoke(note : String) = repo.deleteAccount(note)
}
class CheckAuthUseCase(private val repo: AuthRepo){
    suspend operator fun invoke() = repo.checkAuth()
}


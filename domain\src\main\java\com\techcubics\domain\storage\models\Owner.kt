package com.techcubics.domain.storage.models

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.techcubics.domain.auth.models.User
import kotlinx.parcelize.Parcelize

@Parcelize
data class Owner(
    @SerializedName("id") var id: Int? = null,
    @SerializedName("owner_id") var ownerId: Int? = null,
    @SerializedName("user") var user: User? = User(),
    @SerializedName("address") var address: String? = null,
    @SerializedName("brand_name") var brandName: String? = null,
    @SerializedName("phone") var phone: String? = null,
    @SerializedName("status") var status : String? = null,
    @SerializedName("count_shops") var countShops: Int? = null
) : Parcelable
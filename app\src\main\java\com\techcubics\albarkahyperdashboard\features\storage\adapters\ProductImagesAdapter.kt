package com.techcubics.albarkahyperdashboard.features.storage.adapters

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView.Adapter
import com.techcubics.albarkahyperdashboard.databinding.ItemProductImageBinding
import com.techcubics.albarkahyperdashboard.features.storage.adapters.holders.ProductImagesViewHolder
import com.techcubics.albarkahyperdashboard.utils.listeners.OnItemClickListener
import com.techcubics.domain.storage.models.Image

class ProductImagesAdapter(private var images: MutableList<Image>, private val listener: OnItemClickListener) :
    Adapter<ProductImagesViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductImagesViewHolder {
        val binding =
            ItemProductImageBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ProductImagesViewHolder(parent.context, binding,listener ,images)
    }

    override fun getItemCount(): Int {
        return images.size
    }

    override fun onBindViewHolder(holder: ProductImagesViewHolder, position: Int) {
        holder.setData(image = images[position])
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateImages(images: MutableList<Image>) {
        this.images = images
        notifyDataSetChanged()
    }
}
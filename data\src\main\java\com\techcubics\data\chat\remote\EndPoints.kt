package com.techcubics.data.chat.remote

import com.techcubics.data.chat.utils.ChatConstants
import com.techcubics.domain.chats.models.OrderChat
import com.techcubics.domain.chats.models.OrderChatRoom
import com.techcubics.domain.chats.models.SupportChat
import com.techcubics.domain.chats.models.SupportChatResponse
import com.techcubics.domain.chats.models.SupportChatRoom
import com.techcubics.domain.chats.request.SendMessageRequest
import com.techcubics.domain.common.BaseResponse
import retrofit2.Response
import retrofit2.http.*

interface EndPoints {
    @GET(ChatConstants.orders_chat_rooms)
    suspend fun getOrdersChatRooms(
        @Path("userType") userType : String,
        @Query("page") page: Int,
        @Query("owner_id") ownerId: Int,
        @Query("shop_id") shopId: Int,
        @Query("paginator") paginator: Int = 1
    ): Response<BaseResponse<ArrayList<OrderChatRoom>>>

    @GET(ChatConstants.order_chats)
    suspend fun getOrderChats(
        @Path("userType") userType : String,
        @Path("id") id: Int,
        @Query("owner_id") ownerId: Int,
        @Query("shop_id") shopId: Int
    ): Response<BaseResponse<ArrayList<OrderChat>>>

    @POST(ChatConstants.order_chat_send_msg)
    suspend fun sendOrderChatMessage(
        @Path("userType") userType : String,
        @Body request: SendMessageRequest,
        @Query("owner_id") ownerId: Int,
        @Query("shop_id") shopId: Int
    ): Response<BaseResponse<OrderChat>>

    @GET(ChatConstants.support_chat_rooms)
    suspend fun getSupportChatRooms(
        @Path("userType") userType : String,
        @Query("page") page: Int, @Query("paginator") paginator: Int = 1
    ): Response<BaseResponse<ArrayList<SupportChatRoom>>>

    @GET(ChatConstants.support_chats)
    suspend fun getSupportChats(@Path("userType") userType : String, @Path("id") id: Int): Response<BaseResponse<SupportChatResponse>>

    @POST(ChatConstants.support_chat_send_msg)
    suspend fun sendSupportChatMessage(
        @Path("userType") userType : String,
        @Body request: SendMessageRequest
    ): Response<BaseResponse<SupportChat>>
}
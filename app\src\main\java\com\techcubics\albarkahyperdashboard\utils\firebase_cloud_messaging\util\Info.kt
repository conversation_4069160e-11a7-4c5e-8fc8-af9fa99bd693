package com.techcubics.albarkahyperdashboard.utils.firebase_cloud_messaging.util



//sealed class Info {
//    val notificationType: String
//        get() = when (this) {
//            is CourseInfo -> NotificationTypes.Course.value
//            is GeneralCouponInfo -> NotificationTypes.GeneralCoupon.value
//            is ChatInfo -> NotificationTypes.LiveChat.value
//            is CouponInfo -> NotificationTypes.Coupon.value
//            is GeneralInfo -> NotificationTypes.General.value
//        }
//
//    data class CourseInfo(val courseId: Int, val teacherId: Int) : Info()
//
//    data class CouponInfo(val courseId: Int, val teacherId: Int,val code: String) : Info()
//
//    data class ChatInfo(val chatId: Int) : Info()
//
//    data class GeneralCouponInfo(val code: String) : Info()
//
//    object GeneralInfo : Info()
//}
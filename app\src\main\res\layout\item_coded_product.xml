<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:clickable="true"
    android:layout_marginVertical="4dp"
    android:focusable="true"
    app:cardCornerRadius="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="5dp">

        <ImageView
            android:id="@+id/product_image"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:adjustViewBounds="true"
            app:layout_constraintHeight_min="100dp"
            android:src="@drawable/portrait_placeholder"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/guideline"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />



        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintGuide_percent="0.35"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/p_name"
            style="@style/Medium1BoldDarkText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="5dp"
            android:layout_marginTop="10dp"
            android:textAlignment="viewStart"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/guideline"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="زيتون - 125 مل" />

        <LinearLayout
            android:id="@+id/cat_cont"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:layout_marginTop="10dp"
            app:layout_constraintStart_toStartOf="@id/p_name"
            app:layout_constraintTop_toBottomOf="@id/p_name">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="5dp"
                    app:cardCornerRadius="50dp">

                    <ImageView
                        android:id="@+id/cat_image"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:src="@drawable/portrait_contactus" />
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:id="@+id/category"
                    style="@style/SmallMediumDarkText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="جبن" />
        </LinearLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/add_product"
            style="@style/ExtraSmallBoldWhiteText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:contentDescription="@string/img_cnt_desc"
            android:backgroundTint="@color/app_color"
            android:text="@string/add_product"
            android:layout_marginEnd="5dp"
            app:icon="@drawable/ic_add"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintTop_toBottomOf="@id/cat_cont"
            app:layout_constraintStart_toStartOf="@id/cat_cont" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/card_view"
    app:strokeColor="@color/color_blue_4"
    android:focusable="true"
    app:cardCornerRadius="13dp"
    android:elevation="8dp"
    app:strokeWidth="1dp"
    app:cardPreventCornerOverlap="true"
    app:cardUseCompatPadding="true">
    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:padding="15dp"
        android:layout_height="wrap_content">
        <TextView
            android:gravity="start"
            android:textAlignment="viewStart"
            android:id="@+id/shop_name"
            android:textColor="@color/color_gray_35"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textAppearance="@style/Medium2BoldDarkText"
            tools:text="محممدددددددد"/>
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginStart="5dp"
            android:gravity="center_vertical"
            android:layout_marginTop="8dp">
            <ImageView
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginStart="8dp"
                android:src="@drawable/ic_goole_map"/>
            <TextView
                android:gravity="start"
                android:textAlignment="viewStart"
                android:layout_marginStart="8dp"
                android:id="@+id/location"
                tools:text="@string/choosen_location"
                android:layout_width="wrap_content"
                android:singleLine="false"
                android:layout_height="wrap_content"
                android:textAppearance="@style/Medium2RegularDarkSearch" />

        </LinearLayout>
        <TextView
            android:gravity="start"
            android:layout_marginTop="6dp"
            android:layout_marginStart="6dp"
            android:textAlignment="viewStart"
            android:id="@+id/date"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textAppearance="@style/Medium2RegularDarkSearch"
            tools:text="20/20/2020"/>
        <TextView
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@drawable/divider"
            android:layout_marginTop="5dp"/>

        <TextView
            android:gravity="start"
            android:textAlignment="viewStart"
            android:id="@+id/order_code"
            android:textColor="@color/color_gray_35"
            android:layout_marginTop="5dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:textAppearance="@style/Medium2RegularDarkSearch"
            tools:text="Order#00000164"
            />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <LinearLayout
                android:layout_marginTop="3dp"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="2">
                    <TextView
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/Medium2RegularDarkSearch"
                        android:text="@string/products_count"/>
                    <TextView
                        android:gravity="start"
                        android:textAlignment="viewStart"
                        android:id="@+id/products_count"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/Medium2RegularDarkSearch"
                        tools:text="30"/>
                </LinearLayout>
                <LinearLayout
                    android:layout_marginTop="3dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="2">
                    <TextView
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/Medium2RegularDarkSearch"
                        android:text="@string/payment_status"/>
                    <TextView
                        android:id="@+id/pay_status"
                        android:layout_width="0dp"
                        android:textAlignment="viewStart"
                        android:gravity="start"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/Medium2RegularDarkSearch"
                        tools:text="----"/>
                </LinearLayout>
                <LinearLayout
                    android:layout_marginTop="3dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="2">
                    <TextView
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/Medium2RegularDarkSearch"
                        android:text="@string/total"/>
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:orientation="horizontal"
                        android:layout_height="wrap_content">
                        <TextView
                            android:gravity="start"
                            android:textAlignment="viewStart"
                            android:id="@+id/total"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            tools:text="20"/>
                        <TextView
                            android:gravity="start"
                            android:textAlignment="viewStart"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:textSize="12sp"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            android:text="@string/currency_name"/>
                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:gravity="end"
                android:layout_width="0dp"
                android:padding="1dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/status_layout"
                    android:layout_width="120dp"
                    android:visibility="gone"
                    app:cardCornerRadius="5dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:strokeWidth="1dp"
                    app:strokeColor="@color/color_gray_36"
                    android:background="?attr/selectableItemBackground"
                    android:layout_height="35dp">
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">
                        <TextView
                            android:id="@+id/status_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="15dp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            android:textColor="@color/color_gray_36"
                            app:layout_constraintEnd_toEndOf="parent"
                            android:textAppearance="@style/Medium2BoldDarkText"
                            android:text="@string/preparing"
                            />
                        <ImageView
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:src="@drawable/ic_arrow_down"
                            app:tint="@color/color_gray_36" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/bill_layout"
                    android:layout_width="120dp"
                    app:cardCornerRadius="5dp"
                    android:layout_marginTop="5dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="?attr/selectableItemBackground"
                    app:cardBackgroundColor="@color/color_blue_4"
                    android:layout_height="35dp">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:textColor="@color/white"
                        android:textAppearance="@style/Medium2BoldDarkText"
                        android:text="@string/order"/>

                </com.google.android.material.card.MaterialCardView>
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/customer_layout"
                    android:layout_width="120dp"
                    app:cardCornerRadius="5dp"
                    android:layout_marginTop="5dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="?attr/selectableItemBackground"
                    app:cardBackgroundColor="@color/pyellow"
                    android:layout_height="35dp">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:textColor="@color/color_gray_35"
                        android:textAppearance="@style/Medium2BoldDarkText"
                        android:text="@string/customer"/>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>
        </LinearLayout>


    </LinearLayout>
</com.google.android.material.card.MaterialCardView>
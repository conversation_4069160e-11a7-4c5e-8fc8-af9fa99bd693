<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".features.storage.fragments.StorageFragment">

    <include android:id="@+id/toolbar_fragment"
        layout="@layout/toolbar_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"/>


    <include
        android:id="@+id/tabs"
        layout="@layout/include_storage_tabs"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar_fragment" />

    <include
        android:id="@+id/loading"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/msg_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/animation"
            android:layout_width="200dp"
            android:layout_height="200dp"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_speed="1" />

        <TextView
            android:id="@+id/tvMessage"
            style="@style/SmallMediumDarkText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/message_empty_list_general"
            android:textAlignment="center"
            tools:text="no Data found" />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>


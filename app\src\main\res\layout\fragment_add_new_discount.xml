<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:theme="@style/Theme.Colored"
    tools:context=".features.storage.fragments.tabs.product.AddNewProductFragment">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_fragment" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipToPadding="true"
        android:paddingBottom="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/cont"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/owner_cont"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="6dp"
                    android:layout_weight="1"
                    android:hint="@string/owner_name"
                    app:boxCornerRadiusBottomEnd="10dp"
                    app:boxCornerRadiusBottomStart="10dp"
                    app:boxCornerRadiusTopEnd="10dp"
                    app:boxCornerRadiusTopStart="10dp">

                    <AutoCompleteTextView
                        android:id="@+id/owner_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none"
                        android:textAppearance="@style/SmallRegularDarkText"
                        tools:ignore="LabelFor" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/shop_cont"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="6dp"
                    android:layout_weight="1"
                    android:hint="@string/shop_name"
                    app:boxCornerRadiusBottomEnd="10dp"
                    app:boxCornerRadiusBottomStart="10dp"
                    app:boxCornerRadiusTopEnd="10dp"
                    app:boxCornerRadiusTopStart="10dp">

                    <AutoCompleteTextView
                        android:id="@+id/shop_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none"
                        android:textAppearance="@style/SmallRegularDarkText"
                        tools:ignore="LabelFor" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/product_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/product_name"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cont">

                <AutoCompleteTextView
                    android:id="@+id/product_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>


            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/price_before_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/price_before"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintTop_toBottomOf="@id/product_cont">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/price_before"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:enabled="false"
                    android:inputType="none"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/percent_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/percent"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintTop_toBottomOf="@id/price_before_cont">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/percent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="numberDecimal"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/price_after_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/price_after"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintTop_toBottomOf="@id/percent_cont">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/price_after"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:enabled="false"
                    android:inputType="none"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/min_qty_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/min_num"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintEnd_toStartOf="@id/max_qty_cont"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/price_after_cont">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/min_qty"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/max_qty_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/max_num"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintBottom_toBottomOf="@id/min_qty_cont"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/min_qty_cont"
                app:layout_constraintTop_toTopOf="@id/min_qty_cont">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/max_qty"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/start_date_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/start_date_title"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:endIconDrawable="@drawable/ic_date"
                app:endIconMode="custom"
                app:endIconTint="@color/app_color"
                app:layout_constraintEnd_toStartOf="@id/end_date_cont"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/min_qty_cont">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/start_date"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:focusable="false"
                    android:inputType="none"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/end_date_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/end_date_title"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:endIconDrawable="@drawable/ic_date"
                app:endIconMode="custom"
                app:endIconTint="@color/app_color"
                app:layout_constraintBottom_toBottomOf="@id/start_date_cont"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/start_date_cont"
                app:layout_constraintTop_toTopOf="@id/start_date_cont">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/end_date"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:focusable="false"
                    android:inputType="none"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>

            <include
                android:id="@+id/add_discount"
                layout="@layout/btn_search_progress"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:layout_marginHorizontal="6dp"
                android:layout_marginVertical="16dp"
                app:layout_constraintTop_toBottomOf="@id/start_date_cont" />


        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <include
        android:id="@+id/loading"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:elevation="20dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/msg_layout"
        layout="@layout/include_placeholder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
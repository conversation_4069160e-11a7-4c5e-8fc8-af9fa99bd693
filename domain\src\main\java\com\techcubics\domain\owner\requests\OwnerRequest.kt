package com.techcubics.domain.owner.requests

import com.google.gson.annotations.SerializedName

data class OwnerRequest(
    @SerializedName("email") var email : String? = null,
    @SerializedName("phone") var phone : String? = null,
    @SerializedName("password") var password : String? = null,
    @SerializedName("password_confirmation") var passwordConfirmation : String? = null,
    @SerializedName("branch_name") var branchName : String? = null,
    @SerializedName("name") var name : String? = null,
    @SerializedName("country_id") var countryId : String? = null,
    @SerializedName("governorate_id") var governorateId : String? = null,
    @SerializedName("avatar") var avatar : String? = null
    )

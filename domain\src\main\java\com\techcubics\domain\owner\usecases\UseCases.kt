package com.techcubics.domain.owner.usecases

import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.orders.models.Order
import com.techcubics.domain.orders.requests.OrdersFilterRequest
import com.techcubics.domain.owner.repositories.OwnersRepo
import com.techcubics.domain.owner.requests.OwnerFilterRequest
import com.techcubics.domain.owner.requests.OwnerFilterRequestBody
import com.techcubics.domain.owner.requests.OwnerRequest
import com.techcubics.domain.owner.requests.UpdateOwnerPasswordRequest
import com.techcubics.domain.storage.models.Owner


class OwnersByFilterUseCase(private val repo: OwnersRepo){
    suspend operator fun invoke(page: Int,filter: OwnerFilterRequest) : BaseResponse<MutableList<Owner>>? {
        val request = OwnerFilterRequestBody(search = filter.search, paginator = filter.paginator)
        return repo.ownersByFilter(page,request)
    }
}
class OwnerDetailsUseCase(private val repo: OwnersRepo){
    suspend operator fun invoke(id:String) = repo.ownerDetails(id)
}
class UpdateOwnerStatusUseCase(private val repo: OwnersRepo){
    suspend operator fun invoke(id : String) = repo.updateOwnerStatus(id)
}

class UpdateOwnerUseCase(private val repo: OwnersRepo){
    suspend operator fun invoke(id : String,request : OwnerRequest) = repo.updateOwner(id,request)
}

class GetOwnersInMoreUseCase(private val repo: OwnersRepo){
    suspend operator fun invoke(page : Int? = null) = repo.getOwners(page)
}

class CreateOwnerUseCase(private val repo: OwnersRepo){
    suspend operator fun invoke(request : OwnerRequest) = repo.createOwner(request)
}

class UpdateOwnerPasswordUseCase(private val repo: OwnersRepo){
    suspend operator fun invoke(request : UpdateOwnerPasswordRequest) = repo.updateOwnerPassword(request)
}
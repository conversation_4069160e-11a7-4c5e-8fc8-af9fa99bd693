package com.techcubics.domain.orders.repositories

import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.orders.models.Governorate
import com.techcubics.domain.orders.models.Order
import com.techcubics.domain.orders.models.OrderStatus
import com.techcubics.domain.orders.models.Region
import com.techcubics.domain.orders.requests.OrdersFilterRequestBody

interface BillsRepo {
    suspend fun getOrderByFilter(page: Int,
                                 filter: OrdersFilterRequestBody
    ):BaseResponse<MutableList<Order>>?
    suspend fun getOrderDetails(id : String):BaseResponse<Order>?
    suspend fun getOrdersStatus() : BaseResponse<MutableList<OrderStatus>>?
    suspend fun updateOrderStatus(id :String) : BaseResponse<Order>?
    suspend fun cancelOrder(id :String,reasonMessage : String) : BaseResponse<Order>?
    suspend fun returnOrder(id :String,reasonMessage : String) : BaseResponse<Order>?
    suspend fun getGovernorate(countryId : String = "1") : BaseResponse<MutableList<Governorate>>?
    suspend fun getRegionsByGovernorate( governorateId : String,countryId : String = "1") : BaseResponse<MutableList<Region>>?

}
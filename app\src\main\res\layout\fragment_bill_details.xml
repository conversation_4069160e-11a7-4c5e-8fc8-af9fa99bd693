<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@color/color_gray_3"
    android:layout_height="match_parent"
    tools:context=".features.bills.fragments.BillsStatusFragment">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_home"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/mainRV"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="80dp"
            android:orientation="vertical">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/order_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:elevation="8dp"
                android:focusable="true"
                app:cardCornerRadius="2dp"
                android:layout_margin="5dp"
                app:cardPreventCornerOverlap="true"
                app:cardUseCompatPadding="true"
                app:layout_constraintTop_toBottomOf="@id/toolbar"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="5dp">
                    <TextView
                        android:layout_marginStart="5dp"
                        android:gravity="start"
                        android:textAlignment="viewStart"
                        android:id="@+id/shop_name"
                        android:textColor="@color/color_gray_35"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/Medium2BoldDarkText"
                        tools:text="محممدددددددد"/>
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginStart="8dp"
                        android:gravity="center_vertical"
                        android:layout_marginTop="8dp">
                        <ImageView
                            android:layout_width="25dp"
                            android:layout_height="25dp"
                            android:layout_marginStart="5dp"
                            android:src="@drawable/ic_goole_map"/>
                        <TextView
                            android:gravity="start"
                            android:textAlignment="viewStart"
                            android:layout_marginStart="8dp"
                            android:id="@+id/location"
                            tools:text="@string/choosen_location"
                            android:layout_width="wrap_content"
                            android:singleLine="false"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch" />

                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">
                        <TextView
                            android:gravity="start"
                            android:layout_marginTop="5dp"
                            android:layout_marginStart="6dp"
                            android:textAlignment="viewStart"
                            android:id="@+id/date"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            tools:text="20/20/2020"/>
                        <TextView
                            android:layout_marginTop="5dp"
                            android:layout_marginStart="6dp"
                            android:id="@+id/products_count"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:layout_marginEnd="25dp"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/LargeBoldDarkText"
                            android:textColor="@color/color_blue_4"
                            tools:text="20"/>


                    </LinearLayout>
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@drawable/divider"
                        android:layout_marginTop="5dp"/>
                    <androidx.recyclerview.widget.RecyclerView
                        android:layout_marginTop="8dp"
                        android:id="@+id/productsRV"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        tools:itemCount="2"
                        tools:listitem="@layout/item_product_inorder"/>
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="5dp"
                        android:orientation="horizontal"
                        >
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            android:textColor="@color/color_gray_36"
                            android:text="@string/order_note"/>
                        <TextView
                            android:gravity="start"
                            android:textAlignment="viewStart"
                            android:id="@+id/notes"
                            android:layout_marginStart="10dp"
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            tools:text="30"/>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="5dp"
                        android:orientation="horizontal"
                        >
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            android:textColor="@color/color_gray_36"
                            android:text="@string/total_bill_before_discount"/>
                        <TextView
                            android:gravity="start"
                            android:textAlignment="viewStart"
                            android:id="@+id/total_bill_before_discount"
                            android:layout_marginStart="10dp"
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            tools:text="30"/>
                        <TextView
                            android:gravity="start"
                            android:textAlignment="viewStart"
                            android:layout_marginStart="5dp"
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            android:text="@string/currency_name"/>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="5dp"
                        android:orientation="horizontal"
                        >
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            android:textColor="@color/color_gray_36"
                            android:text="@string/delivery_price_"/>
                        <TextView
                            android:gravity="start"
                            android:textAlignment="viewStart"
                            android:id="@+id/delivery_price"
                            android:layout_marginStart="10dp"
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            tools:text="30"/>
                        <TextView
                            android:gravity="start"
                            android:textAlignment="viewStart"
                            android:layout_marginStart="5dp"
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            android:text="@string/currency_name"/>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="5dp"
                        android:orientation="horizontal"
                        >
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            android:textColor="@color/color_gray_36"
                            android:text="@string/instant_discount"/>
                        <TextView
                            android:gravity="start"
                            android:textAlignment="viewStart"
                            android:id="@+id/instant_discount"
                            android:layout_marginStart="10dp"
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            tools:text="30"/>
                        <TextView
                            android:gravity="start"
                            android:textAlignment="viewStart"
                            android:layout_marginStart="5dp"
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            android:text="@string/currency_name"/>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="5dp"
                        android:orientation="horizontal"
                        >
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            android:textColor="@color/color_gray_36"
                            android:text="@string/coupon_price"/>
                        <TextView
                            android:gravity="start"
                            android:textAlignment="viewStart"
                            android:id="@+id/coupon_price"
                            android:layout_marginStart="10dp"
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            tools:text="30"/>
                        <TextView
                            android:id="@+id/pound"
                            android:gravity="start"
                            android:textAlignment="viewStart"
                            android:layout_marginStart="5dp"
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            android:text="@string/currency_name"/>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="5dp"
                        android:orientation="horizontal"
                        >
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            android:textColor="@color/color_gray_36"
                            android:text="@string/coupon_code_title"/>
                        <TextView
                            android:gravity="start"
                            android:textAlignment="viewStart"
                            android:id="@+id/coupon_code"
                            android:layout_marginStart="10dp"
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            tools:text="30"/>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="5dp"
                        android:orientation="horizontal"
                        >
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            android:textColor="@color/color_gray_36"
                            android:text="@string/payment_method"/>
                        <TextView
                            android:gravity="start"
                            android:textAlignment="viewStart"
                            android:id="@+id/payment_method"
                            android:layout_marginStart="10dp"
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            tools:text="30"/>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="5dp"
                        android:orientation="horizontal"
                        >
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            android:textColor="@color/pred"
                            android:text="@string/total_bill"/>
                        <TextView
                            android:gravity="start"
                            android:textAlignment="viewStart"
                            android:id="@+id/total"
                            android:layout_marginStart="10dp"
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:textColor="@color/pred"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            tools:text="30"/>
                        <TextView
                            android:gravity="start"
                            android:textAlignment="viewStart"
                            android:textColor="@color/pred"
                            android:layout_marginStart="5dp"
                            android:layout_width="wrap_content"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            android:text="@string/currency_name"/>
                    </LinearLayout>
                    <LinearLayout
                        android:id="@+id/status_btns"
                        android:layout_width="match_parent"
                        android:orientation="horizontal"
                        android:layout_marginTop="16dp"
                        android:layout_marginBottom="16dp"
                        android:layout_height="wrap_content">
                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:id="@+id/ok"
                            android:clickable="true"
                            android:focusable="true"
                            android:background="?attr/selectableItemBackground"
                            app:cardBackgroundColor="@color/pgreen"
                            android:layout_height="50dp"
                            android:layout_marginEnd="16dp">
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_gravity="center"
                                android:layout_height="wrap_content"
                                android:text="@string/ok"
                                android:textAppearance="@style/Medium2BoldDarkText"
                                android:textColor="@color/white"/>
                        </com.google.android.material.card.MaterialCardView>
                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="0dp"
                            android:clickable="true"
                            android:focusable="true"
                            android:id="@+id/cancel"
                            android:background="?attr/selectableItemBackground"
                            android:layout_weight="1"
                            app:cardBackgroundColor="@color/pred"
                            android:layout_height="50dp">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_gravity="center"
                            android:layout_height="wrap_content"
                            android:text="@string/cancel"
                            android:textAppearance="@style/Medium2BoldDarkText"
                            android:textColor="@color/white"/>
                        </com.google.android.material.card.MaterialCardView>
                    </LinearLayout>
                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <include
        android:id="@+id/action_loading_animation"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/layout"
        layout="@layout/include_placeholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"

        />
</androidx.constraintlayout.widget.ConstraintLayout>
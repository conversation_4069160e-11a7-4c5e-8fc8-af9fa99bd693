package com.techcubics.data.auth.remote.repoImp

import com.techcubics.domain.auth.repositories.AuthRepo
import com.techcubics.domain.auth.requests.*
import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.common.RepositoryResponse
import com.techcubics.data.auth.remote.EndPoints
import com.techcubics.data.common.RetrofitBuilder
import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.data.auth.utils.Constants.userType
import com.techcubics.domain.auth.models.LoginResponse
import com.techcubics.domain.auth.responses.ForgetPasswordResponseData
import com.techcubics.domain.auth.requests.LoginRequest
import com.techcubics.domain.auth.models.User

class AuthRepoImpl(private val retrofitBuilder: RetrofitBuilder, private val sharedPreferencesManager: SharedPreferencesManager) : <PERSON><PERSON><PERSON><PERSON><PERSON>,
    RepositoryResponse {

    private val api=retrofitBuilder.start()?.create(EndPoints::class.java)

    override suspend fun login(request: LoginRequest, userType: String): BaseResponse<LoginResponse>? {
        return try {

            val result = api
                ?.login(request,userType)
            if(result?.body()?.data != null){
                sharedPreferencesManager.saveToken(result.body()?.data?.token!!)
                sharedPreferencesManager.saveID(result.body()?.data?.id)
            }
            baseResponse(result)

        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun forgetPasswordEmailCall(request: ForgetPasswordByEmailRequest): BaseResponse<ForgetPasswordResponseData>? {
        return try {
            val result =
                api?.forgetPasswordByEmail(sharedPreferencesManager.getUserType(),request)
            baseResponse(result)

        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun logout(): BaseResponse<String>? {
        return try {
            val result = api?.logout(sharedPreferencesManager.getUserType())
            baseResponse(result)

        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun deleteAccount(note : String): BaseResponse<String>? {
        try {
            val result = api?.deleteAccount(sharedPreferencesManager.getUserType(),note)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun updatePasswordResetCall(request: UpdatePasswordRequest): BaseResponse<Nothing>? {
        try {
            val result = api
                ?.updatePasswordReset(sharedPreferencesManager.getUserType(),request)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun checkAuth(): BaseResponse<Any>? {
        try {
            val result = api?.checkAuth(sharedPreferencesManager.getUserType())
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

}
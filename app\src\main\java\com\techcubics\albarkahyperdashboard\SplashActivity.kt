package com.techcubics.albarkahyperdashboard

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class SplashActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_splash)
        initViews()
        observeViews()
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        if (hasFocus) Helper.hideSystemUI(window)
        else Helper.showSystemUI(window)
    }

    private fun observeViews() {

    }

    private fun initViews() {
        lifecycleScope.launch {
            delay(1500)
            startActivity(Intent(this@SplashActivity, MainActivity::class.java))
            finish()
        }
    }


}
package com.techcubics.domain.more.repositories

import com.techcubics.domain.auth.models.LoginResponse
import com.techcubics.domain.auth.models.UpdateProfileRequest
import com.techcubics.domain.auth.requests.*
import com.techcubics.domain.auth.responses.ForgetPasswordResponseData
import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.auth.requests.LoginRequest
import com.techcubics.domain.auth.models.User
import com.techcubics.domain.more.models.BannerData
import com.techcubics.domain.more.models.Language
import com.techcubics.domain.more.requests.AccountSettingRequest

interface MoreRepo {
    suspend fun updateProfileCall(accountSettingRequest: AccountSettingRequest):BaseResponse<LoginResponse>?
    suspend fun getHomeBanner() : BaseResponse<MutableList<BannerData>>?
    suspend fun getBanners(): BaseResponse<MutableList<BannerData>>?
    suspend fun getLanguages() : BaseResponse<List<Language>>?

}
package com.techcubics.data.bills.repoImp

import android.util.Log
import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.data.bills.remote.EndPoints
import com.techcubics.data.common.RetrofitBuilder
import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.common.RepositoryResponse
import com.techcubics.domain.orders.models.Governorate
import com.techcubics.domain.orders.models.Order
import com.techcubics.domain.orders.models.OrderStatus
import com.techcubics.domain.orders.models.Region
import com.techcubics.domain.orders.repositories.BillsRepo
import com.techcubics.domain.orders.requests.OrdersFilterRequestBody

class BillsRepoImpl(private val retrofitBuilder: RetrofitBuilder, private val sharedPreferencesManager: SharedPreferencesManager) : BillsRepo,
    RepositoryResponse {

    private val api=retrofitBuilder.start()?.create(EndPoints::class.java)


    override suspend fun getOrderByFilter(page: Int,
                                          filter: OrdersFilterRequestBody
    ): BaseResponse<MutableList<Order>>? {
        try {

            val result = if (filter.regionList.isEmpty()) {
                Log.i("info","emp")
                api
                    ?.ordersByFilter(
                        page = page,
                        status = filter.status,
                        sort = filter.sort,
                        paginator = filter.paginator,
                        filter = filter.search,
                        userType = sharedPreferencesManager.getUserType()
                    )
            } else {
                Log.i("info","not emp")
                api
                    ?.ordersByFilter(
                        page = page,
                        status = filter.status,
                        sort = filter.sort,
                        regionList = filter.regionList,
                        paginator = filter.paginator,
                        filter = filter.search,
                        userType = sharedPreferencesManager.getUserType()
                    )
            }
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun getOrderDetails(id: String): BaseResponse<Order>? {
        try {
            val result = api
                ?.orderDetails(userType = sharedPreferencesManager.getUserType(), id)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun getOrdersStatus(): BaseResponse<MutableList<OrderStatus>>? {
        try {
            val result = api
                ?.getOrdersStatus(userType = sharedPreferencesManager.getUserType())
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun updateOrderStatus(id: String): BaseResponse<Order>? {
        try {
            val result = api
                ?.updateOrderStatus(userType = sharedPreferencesManager.getUserType(),id)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun cancelOrder(id: String,reasonMessage : String): BaseResponse<Order>? {
        try {
            val result = api
                ?.cancelOrder(userType = sharedPreferencesManager.getUserType(),id,reasonMessage)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun returnOrder(id: String,reasonMessage : String): BaseResponse<Order>? {
        return try {
            val result = api
                ?.returnOrder(userType = sharedPreferencesManager.getUserType(),id,reasonMessage)
            baseResponse(result)

        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getGovernorate(countryId: String): BaseResponse<MutableList<Governorate>>? {
        return try {
            val result = api
                ?.getGovernorate(userType = sharedPreferencesManager.getUserType(),countryId)
            baseResponse(result)

        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getRegionsByGovernorate(
        governorateId: String,
        countryId: String
    ): BaseResponse<MutableList<Region>>? {
        return try {
            val result = api
                ?.getRegionsByGovernorate(userType = sharedPreferencesManager.getUserType(),governorateId,countryId)
            baseResponse(result)

        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }


}
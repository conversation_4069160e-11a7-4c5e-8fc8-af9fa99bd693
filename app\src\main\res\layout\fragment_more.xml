<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/profile_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_gray_3">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/sc_view"
        android:layout_marginTop="8dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="40dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.cardview.widget.CardView
                android:id="@+id/profile_data"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                app:cardCornerRadius="18dp"
                app:cardElevation="5dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:visibility="visible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent">

                    <de.hdodenhof.circleimageview.CircleImageView
                        android:id="@+id/user_photo"
                        android:layout_width="65dp"
                        android:layout_height="65dp"
                        android:layout_marginStart="14dp"
                        android:src="@drawable/portrait_placeholder"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="9dp"
                        android:orientation="vertical"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/user_photo"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/user_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="User Name"
                            android:textAppearance="@style/ExtraLargeBoldDarkText"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/phone"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="<EMAIL>"
                            android:textAppearance="@style/ExtraSmallBoldDarkText"
                            android:textSize="13sp" />
                    </LinearLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                app:cardCornerRadius="18dp"
                app:cardElevation="5dp">

                <LinearLayout
                    android:id="@+id/user_services"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <LinearLayout

                        android:id="@+id/owners"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:visibility="gone"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="8dp"
                        android:paddingStart="16dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:backgroundTint="@color/pgreen"
                            android:innerRadius="0dp"
                            android:shape="ring"
                            app:cardCornerRadius="75dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_owners" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:gravity="center_vertical"
                            android:text="@string/owners"
                            android:textSize="15sp"
                            android:textAppearance="@style/Medium2RegularDarkSearch" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="13dp"
                        android:layout_marginEnd="50dp"
                        android:visibility="gone"
                        android:background="@drawable/line_divder_item"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />
                    <LinearLayout
                        android:id="@+id/account_setting"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="10dp"
                        android:paddingStart="16dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_centerInParent="true"
                            android:backgroundTint="@color/indian_red"
                            android:innerRadius="0dp"
                            android:shape="ring"
                            app:cardCornerRadius="75dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_me" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:text="@string/account_setting"
                            android:textSize="15sp"
                            android:gravity="center_vertical"
                            android:textAppearance="@style/Medium2RegularDarkSearch" />
                    </LinearLayout>

                    <View
                        android:id="@+id/account_divider"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:textSize="15sp"
                        android:layout_marginStart="13dp"
                        android:layout_marginEnd="50dp"
                        android:background="@drawable/line_divder_item"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <LinearLayout
                        android:id="@+id/language"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="10dp"
                        android:paddingStart="16dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:backgroundTint="@color/black"
                            android:innerRadius="0dp"
                            android:shape="ring"
                            app:cardCornerRadius="75dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_lang" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:text="@string/language"
                            android:gravity="center_vertical"
                            android:textAppearance="@style/Medium2RegularDarkSearch" />

                    </LinearLayout>



                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="13dp"
                        android:layout_marginEnd="50dp"
                        android:background="@drawable/line_divder_item"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />
                    <LinearLayout
                    android:id="@+id/support"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp">

                    <androidx.cardview.widget.CardView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_centerInParent="true"
                        android:backgroundTint="@color/red"
                        android:innerRadius="0dp"
                        android:shape="ring"
                        app:cardCornerRadius="75dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_support_chat" />
                    </androidx.cardview.widget.CardView>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:text="@string/support"
                        android:gravity="center_vertical"
                        android:textAppearance="@style/Medium2RegularDarkSearch" />

                    <TextView
                        android:id="@+id/supportUnReadNo"
                        style="@style/ExtraSmallBoldWhiteText"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/circle_red"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:visibility="gone"
                        tools:text="1" />

                </LinearLayout>
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="13dp"
                        android:layout_marginEnd="50dp"
                        android:background="@drawable/line_divder_item"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />
                    <LinearLayout
                        android:id="@+id/sign_in_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="8dp"
                        android:paddingStart="16dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:backgroundTint="@color/purple_700"
                            android:innerRadius="0dp"
                            android:shape="ring"
                            app:cardCornerRadius="75dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_logout" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:id="@+id/sign_in"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:text="@string/signin"
                            android:textSize="15sp"
                            android:gravity="center_vertical"
                            android:textAppearance="@style/Medium2RegularDarkSearch"
                            android:textColor="@color/app_color" />
                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                app:cardCornerRadius="18dp"
                app:cardElevation="5dp">

                <LinearLayout
                    android:id="@+id/co_link"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:id="@+id/website"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="8dp"
                        android:foreground="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:layout_marginEnd="4dp"
                        android:background="@drawable/ic_lang"
                        android:backgroundTint="@color/pgreen" />

                    <ImageView
                        android:id="@+id/facebook"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginStart="8dp"
                        android:layout_marginEnd="4dp"
                        android:foreground="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:layout_gravity="center"
                        android:background="@drawable/ic_facebook" />
                    <ImageView
                        android:id="@+id/whatsapp"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:foreground="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:layout_marginStart="8dp"
                        android:layout_marginEnd="4dp"
                        android:background="@drawable/ic_whatsapp" />
                    <TextView
                        android:id="@+id/developed_by"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:gravity="start"
                        android:textAlignment="viewStart"
                        android:layout_gravity="center_vertical"
                        android:text="@string/developed_by_techcubics"
                        android:textAppearance="@style/Medium2RegularDarkSearch" />
                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <include
        android:id="@+id/action_loading_animation"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
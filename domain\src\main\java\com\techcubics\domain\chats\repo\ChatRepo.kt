package com.techcubics.domain.chats.repo

import com.techcubics.domain.chats.models.OrderChat
import com.techcubics.domain.chats.models.OrderChatRoom
import com.techcubics.domain.chats.models.SupportChat
import com.techcubics.domain.chats.models.SupportChatResponse
import com.techcubics.domain.chats.models.SupportChatRoom
import com.techcubics.domain.chats.request.SendMessageRequest
import com.techcubics.domain.common.BaseResponse

interface ChatRepo {
    suspend fun getOrdersChatRooms(page: Int,ownerId: Int,shopId:Int): BaseResponse<ArrayList<OrderChatRoom>>?

    suspend fun getOrderChats(id: Int,ownerId: Int,shopId:Int): BaseResponse<ArrayList<OrderChat>>?

    suspend fun sendOrderChatMessage(
        request: SendMessageRequest,ownerId: Int,shopId:Int
    ): BaseResponse<OrderChat>?

    suspend fun getSupportChatRooms(page: Int): BaseResponse<ArrayList<SupportChatRoom>>?

    suspend fun getSupportChats(id: Int): BaseResponse<SupportChatResponse>?

    suspend fun sendSupportChatMessage(
        request: SendMessageRequest
    ): BaseResponse<SupportChat>?
}

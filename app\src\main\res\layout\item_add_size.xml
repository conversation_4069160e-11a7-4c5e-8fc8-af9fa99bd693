<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardUseCompatPadding="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="8dp">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/app_color2"
            app:cardCornerRadius="50dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_close"
                android:padding="5dp"
                app:tint="@color/white" />
        </com.google.android.material.card.MaterialCardView>


        <TextView
            android:id="@+id/title_ar"
            style="@style/SmallBoldAppColorText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/size_ar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/div"
            style="@style/SmallBoldAppColorText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:text=":"
            app:layout_constraintBottom_toBottomOf="@id/title_ar"
            app:layout_constraintStart_toEndOf="@id/title_ar"
            app:layout_constraintTop_toTopOf="@id/title_ar"
            tools:ignore="HardcodedText" />

        <TextView
            android:id="@+id/size_ar"
            style="@style/SmallRegularDarkText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            app:layout_constraintBottom_toBottomOf="@id/title_ar"
            app:layout_constraintStart_toEndOf="@id/div"
            app:layout_constraintTop_toTopOf="@id/title_ar"
            tools:text="صغير" />

        <TextView
            android:id="@+id/title_en"
            style="@style/SmallBoldAppColorText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:text="@string/size_en"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title_ar" />

        <TextView
            android:id="@+id/div2"
            style="@style/SmallBoldAppColorText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:text=":"
            app:layout_constraintBottom_toBottomOf="@id/title_en"
            app:layout_constraintStart_toEndOf="@id/title_en"
            app:layout_constraintTop_toTopOf="@id/title_en"
            tools:ignore="HardcodedText" />

        <TextView
            android:id="@+id/size_en"
            style="@style/SmallRegularDarkText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            app:layout_constraintBottom_toBottomOf="@id/title_en"
            app:layout_constraintStart_toEndOf="@id/div2"
            app:layout_constraintTop_toTopOf="@id/title_en"
            tools:text="Small" />

        <TextView
            android:id="@+id/price_title"
            style="@style/SmallBoldAppColorText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:text="@string/price"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title_en" />

        <TextView
            android:id="@+id/div3"
            style="@style/SmallBoldAppColorText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:text=":"
            app:layout_constraintBottom_toBottomOf="@id/price_title"
            app:layout_constraintStart_toEndOf="@id/price_title"
            app:layout_constraintTop_toTopOf="@id/price_title"
            tools:ignore="HardcodedText" />

        <TextView
            android:id="@+id/price"
            style="@style/SmallRegularDarkText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            app:layout_constraintBottom_toBottomOf="@id/price_title"
            app:layout_constraintStart_toEndOf="@id/div3"
            app:layout_constraintTop_toTopOf="@id/price_title"
            tools:text="50.5 EGP" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</com.google.android.material.card.MaterialCardView>
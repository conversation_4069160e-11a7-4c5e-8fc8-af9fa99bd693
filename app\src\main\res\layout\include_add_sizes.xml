<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:elevation="16dp"
    android:paddingHorizontal="16dp"
    android:paddingVertical="24dp"
    android:theme="@style/Theme.TextInputLayout"
    app:behavior_hideable="false"
    app:behavior_peekHeight="0dp"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

    <ImageView
        android:id="@+id/close_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:background="?selectableItemBackground"
        android:clickable="true"
        android:contentDescription="@string/img_cnt_desc"
        android:focusable="true"
        android:padding="5dp"
        android:src="@drawable/ic_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/app_color" />

    <TextView
        android:id="@+id/sizes_title"
        style="@style/LargeBoldAppColorText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/sizes"
        app:layout_constraintBottom_toBottomOf="@id/close_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/close_icon" />

    <View
        android:id="@+id/line1"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="16dp"
        android:background="@color/app_color2"
        app:layout_constraintTop_toBottomOf="@id/sizes_title" />


    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/size_ar_cont"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="6dp"
        android:hint="@string/size_ar"
        app:boxCornerRadiusBottomEnd="10dp"
        app:boxCornerRadiusBottomStart="10dp"
        app:boxCornerRadiusTopEnd="10dp"
        app:boxCornerRadiusTopStart="10dp"
        app:layout_constraintEnd_toStartOf="@id/size_en_cont"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line1">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/size_ar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:textAppearance="@style/Medium1RegularDarkText"
            tools:ignore="LabelFor" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/size_en_cont"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="6dp"
        android:hint="@string/size_en"
        app:boxCornerRadiusBottomEnd="10dp"
        app:boxCornerRadiusBottomStart="10dp"
        app:boxCornerRadiusTopEnd="10dp"
        app:boxCornerRadiusTopStart="10dp"
        app:layout_constraintBottom_toBottomOf="@id/size_ar_cont"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/size_ar_cont"
        app:layout_constraintTop_toTopOf="@id/size_ar_cont">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/size_en"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:textAppearance="@style/Medium1RegularDarkText"
            tools:ignore="LabelFor" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/price_cont"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="6dp"
        android:hint="@string/price"
        app:boxCornerRadiusBottomEnd="10dp"
        app:boxCornerRadiusBottomStart="10dp"
        app:boxCornerRadiusTopEnd="10dp"
        app:boxCornerRadiusTopStart="10dp"
        app:layout_constraintEnd_toStartOf="@id/add"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/size_ar_cont">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="numberDecimal"
            android:textAppearance="@style/Medium1RegularDarkText"
            tools:ignore="LabelFor" />

    </com.google.android.material.textfield.TextInputLayout>

    <include
        android:id="@+id/add"
        layout="@layout/btn_search_progress"
        android:layout_width="wrap_content"
        android:layout_height="45dp"
        android:layout_margin="6dp"
        app:layout_constraintBottom_toBottomOf="@id/price_cont"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/price_cont"
        app:layout_constraintTop_toTopOf="@id/price_cont" />

    <View
        android:id="@+id/line2"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="16dp"
        android:background="@color/app_color2"
        app:layout_constraintTop_toBottomOf="@id/add" />

    <androidx.recyclerview.widget.RecyclerView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="6dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line2"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:listitem="@layout/item_add_size"/>

</androidx.constraintlayout.widget.ConstraintLayout>


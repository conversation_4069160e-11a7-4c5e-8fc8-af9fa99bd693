package com.techcubics.albarkahyperdashboard.utils.components

import android.app.Dialog
import android.content.DialogInterface
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup
import android.view.Window
import androidx.appcompat.app.AppCompatDialogFragment
import com.techcubics.albarkahyperdashboard.MainActivity
import com.techcubics.albarkahyperdashboard.R
import com.techcubics.albarkahyperdashboard.databinding.DialogNoNetworkBinding
import com.techcubics.albarkahyperdashboard.databinding.IncludeNoNetworkLayoutBinding
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.listeners.OnAttachChangeListener


class NoNetworkDialog :
    AppCompatDialogFragment() {
    private var _binding: DialogNoNetworkBinding? = null
    private val binding get() = _binding!!
    private lateinit var onAttachChangeListener: OnAttachChangeListener

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        _binding = DialogNoNetworkBinding.inflate(layoutInflater)
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setContentView(binding.root)
        onAttachChangeListener = requireActivity() as MainActivity
        binding.noNetworkView.refresh.setOnClickListener {
            checkInternetConnectionManually()
        }
        dialog.setCanceledOnTouchOutside(true)
        dialog.setCancelable(true)
        dialog.window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        dialog.window?.setGravity(Gravity.CENTER)
        setStyle(STYLE_NORMAL, com.techcubics.resources.R.style.CustomAlertDialogTheme)
        return dialog
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onAttachChangeListener.updateAttached(false)
    }

    private fun checkInternetConnectionManually() {
        if (Helper.isConnected(requireContext())) {
            dismiss()
        }
    }

}

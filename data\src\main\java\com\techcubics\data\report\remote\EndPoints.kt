package com.techcubics.data.report.remote

import com.techcubics.data.report.utils.Constants
import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.reports.request.ReportRequest
import com.techcubics.domain.reports.response.ReportResponse
import retrofit2.Response
import retrofit2.http.*

interface EndPoints {

    @POST(Constants.reports)
    suspend fun getReports(
        @Path("userType") userType: String,
        @Body request: ReportRequest
    ): Response<BaseResponse<ReportResponse>>

}
package com.techcubics.albarkahyperdashboard

import android.content.Context
import android.content.res.Configuration
import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.setupWithNavController
import com.google.android.material.snackbar.Snackbar
import com.techcubics.albarkahyperdashboard.databinding.ActivityMainBinding
import com.techcubics.albarkahyperdashboard.features.auth.intents.AuthIntent
import com.techcubics.albarkahyperdashboard.features.auth.state.AuthViewState
import com.techcubics.albarkahyperdashboard.features.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyperdashboard.utils.components.ConnectionLiveData
import com.techcubics.albarkahyperdashboard.utils.components.NoNetworkDialog
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.listeners.NavigationBarVisibilityListener
import com.techcubics.albarkahyperdashboard.utils.listeners.OnAttachChangeListener
import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.data.auth.utils.Constants
import com.techcubics.data.auth.utils.LoginStateEnum
import com.zeugmasolutions.localehelper.LocaleHelper
import com.zeugmasolutions.localehelper.LocaleHelperActivityDelegate
import com.zeugmasolutions.localehelper.LocaleHelperActivityDelegateImpl
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.Locale

class MainActivity : AppCompatActivity(), NavigationBarVisibilityListener, OnAttachChangeListener {

    private val TAG = "MainActivity"
    private lateinit var binding: ActivityMainBinding
    private lateinit var navController: NavController
    private lateinit var noNetworkDialog: NoNetworkDialog
    private val viewModel by viewModel<AuthViewModel>()
    private val localeDelegate: LocaleHelperActivityDelegate = LocaleHelperActivityDelegateImpl()
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private var isAttached = false
    lateinit var connectionLiveData: ConnectionLiveData
    private var isConnected = false
    private lateinit var snackbar: Snackbar
    private lateinit var onDestinationChangedListener: NavController.OnDestinationChangedListener
    private var checkAuthFinished = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        connectionLiveData = ConnectionLiveData(this)
        noNetworkDialog =
            supportFragmentManager.findFragmentByTag("Offline Dialog") as NoNetworkDialog?
                ?: NoNetworkDialog()
        snackbar = Snackbar
            .make(
                binding.root,
                resources.getString(com.techcubics.resources.R.string.connecttotnetwork),
                Snackbar.LENGTH_INDEFINITE
            )
        isConnected = Helper.isConnected(this)
        connectionLiveData.observe(this) { isConnected ->
            if (this.isConnected!=isConnected) {
                this.isConnected = isConnected
                handleNetworkConnectivity()
            }
        }

        setupNavigation()
        observeViews()
        checkInternetConnection()
        val navHostFragment =
            supportFragmentManager.findFragmentById(R.id.fragmentContainerView) as NavHostFragment
        navController = navHostFragment.navController
        binding.bottomNavigationView.setupWithNavController(navController)
        Log.d("token", ":Bearer " + sharedPreferencesManager.getToken())
    }

    private fun observeViews() {
        lifecycleScope.launch {
            viewModel.viewState.collect {
                when (it) {
                    is AuthViewState.Idle -> {}
                    is AuthViewState.Error -> {
                        if (!checkAuthFinished) {
                            navController.navigate(R.id.loginFragment)
                            sharedPreferencesManager.setLoginState(LoginStateEnum.Other.value)
                            checkAuthFinished=true
                        }
                    }

                    else -> {}
                }
                viewModel.viewState.value = null
            }
        }
    }

    private fun setupNavigation() {
        val navHostFragment =
            supportFragmentManager.findFragmentById(binding.fragmentContainerView.id) as NavHostFragment
        navController = navHostFragment.navController
        binding.bottomNavigationView.setupWithNavController(navController)
        onDestinationChangedListener = NavController.OnDestinationChangedListener { _, _, _ ->
            if (sharedPreferencesManager.getLoginState()==LoginStateEnum.LoggedIn.value) {
                checkAuthFinished=false
                checkAuth()
            }
        }
        navController.addOnDestinationChangedListener(onDestinationChangedListener)
    }

    private fun checkAuth() {
        lifecycleScope.launch {
            viewModel.authIntent.send(AuthIntent.CheckAuth)
        }
    }

    override fun attachBaseContext(newBase: Context?) {
        super.attachBaseContext(newBase?.let { localeDelegate.attachBaseContext(it) })
    }

    open fun updateLocale(locale: Locale) {
        sharedPreferencesManager.saveLanguage(locale.language)
        localeDelegate.setLocale(this@MainActivity, locale)

    }
    override fun onResume() {
        super.onResume()
        localeDelegate.onResumed(this)
    }
    override fun getDelegate() = localeDelegate.getAppCompatDelegate(super.getDelegate())
    override fun onPause() {
        super.onPause()
        localeDelegate.onPaused()
    }

    override fun createConfigurationContext(overrideConfiguration: Configuration): Context {
        val context = super.createConfigurationContext(overrideConfiguration)
        return LocaleHelper.onAttach(context)
    }

    override fun getApplicationContext(): Context =
        localeDelegate.getApplicationContext(super.getApplicationContext())

    private fun checkInternetConnection() {
        if (!isConnected){
            handleNetworkConnectivity()
        }

    }

    private fun handleNetworkConnectivity() {
        isAttached = if (!isConnected) {
            showNoNetworkDialog()
            true
        } else {
            refreshCurrentFragment()
            dismissNoNetworkDialog()
            dismissSnackbar()
            false
        }
    }

    private fun refreshCurrentFragment() {
        val currentDestination = navController.currentDestination
        currentDestination?.id?.let {
            val e = navController.getBackStackEntry(it)
            val args = e.arguments
            navController.navigate(currentDestination.id, args)
        }
    }

    override fun navbarVisibility(isVisible: Int) {
        binding.bottomNavigationView.visibility = isVisible

    }


    override fun updateAttached(isAttached: Boolean) {
        this.isAttached = isAttached
        if (!isConnected && !isAttached) {
            snackbar.show()
        }
    }

    private fun showNoNetworkDialog() {
        if (!noNetworkDialog.isAdded) {
            noNetworkDialog.show(supportFragmentManager, "Offline Dialog")
        }
    }

    private fun dismissNoNetworkDialog() {
        if (noNetworkDialog.isAdded) {
            noNetworkDialog.dismiss()
        }
    }

    private fun dismissSnackbar() {
        if (snackbar.isShown) {
            snackbar.dismiss()
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        if (noNetworkDialog.isAdded) {
            supportFragmentManager.putFragment(outState, "Offline Dialog", noNetworkDialog)
        }
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        noNetworkDialog = supportFragmentManager.getFragment(
            savedInstanceState,
            "Offline Dialog"
        ) as NoNetworkDialog?
            ?: NoNetworkDialog()
    }

    override fun onDestroy() {
        super.onDestroy()
        navController.removeOnDestinationChangedListener(onDestinationChangedListener)
    }

}
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/product_details_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="16dp"
    android:paddingBottom="10dp"
    android:paddingTop="10dp">

    <com.tbuonomo.viewpagerdotsindicator.DotsIndicator
        android:id="@+id/dots_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:dotsColor="@color/color_gray_22"
        app:dotsCornerRadius="8dp"
        app:dotsSize="16dp"
        app:dotsSpacing="4dp"
        app:dotsWidthFactor="2.5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:progressMode="false"
        app:selectedDotColor="@color/color_gray_2" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:textAlignment="viewStart"
        android:theme="@style/LargeBoldDarkText"
        app:layout_constraintEnd_toStartOf="@id/tv_price"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dots_indicator"
        tools:text="لاب توب" />


    <TextView
        android:id="@+id/tv_price"
        style="@style/Medium2BoldGreenText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_title"
        tools:text="41.99 جنيه" />

    <LinearLayout
        android:id="@+id/min_qty_cont"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title">

        <TextView
            style="@style/Medium1RegularDarkText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/min_num" />

        <TextView
            style="@style/Medium1RegularDarkText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="5dp"
            android:text=":"
            tools:ignore="HardcodedText" />

        <TextView
            android:id="@+id/min_qty"
            style="@style/Medium1RegularGreenText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:textColor="@color/app_color"
            tools:text="5" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/max_qty_cont"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/min_qty_cont">

        <TextView
            style="@style/Medium1RegularDarkText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/max_num" />

        <TextView
            style="@style/Medium1RegularDarkText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="5dp"
            android:text=":"
            tools:ignore="HardcodedText" />

        <TextView
            android:id="@+id/max_qty"
            style="@style/Medium1RegularGreenText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:textColor="@color/app_color"
            tools:text="20" />
    </LinearLayout>


    <LinearLayout
        android:id="@+id/owner_cont"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="3dp"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/max_qty_cont">


        <com.google.android.material.card.MaterialCardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            app:cardCornerRadius="50dp">

            <ImageView
                android:id="@+id/owner_image"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:contentDescription="@string/img_cnt_desc"
                android:src="@drawable/portrait_contactus" />
        </com.google.android.material.card.MaterialCardView>

        <TextView
            android:id="@+id/owner_name"
            style="@style/SmallMediumDarkText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="البركة" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/shop_cont"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginBottom="3dp"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@id/owner_cont"
        app:layout_constraintStart_toEndOf="@id/owner_cont"
        app:layout_constraintTop_toTopOf="@id/owner_cont">


        <com.google.android.material.card.MaterialCardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            app:cardCornerRadius="50dp">

            <ImageView
                android:id="@+id/shop_image"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:src="@drawable/portrait_contactus" />
        </com.google.android.material.card.MaterialCardView>

        <TextView
            android:id="@+id/shop_name"
            style="@style/SmallMediumDarkText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="مؤسسة البركة" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/cat_cont"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="3dp"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="@id/owner_cont"
        app:layout_constraintTop_toBottomOf="@id/shop_cont">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            app:cardCornerRadius="50dp">

            <ImageView
                android:id="@+id/cat_image"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:contentDescription="@string/img_cnt_desc"
                android:src="@drawable/portrait_contactus" />
        </com.google.android.material.card.MaterialCardView>

        <TextView
            android:id="@+id/category"
            style="@style/SmallMediumDarkText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="جبن" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/subcat_cont"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginBottom="3dp"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@id/cat_cont"
        app:layout_constraintStart_toEndOf="@id/cat_cont"
        android:layout_marginStart="16dp"
        app:layout_constraintTop_toTopOf="@id/cat_cont">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            app:cardCornerRadius="50dp">

            <ImageView
                android:id="@+id/subcat_image"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:contentDescription="@string/img_cnt_desc"
                android:src="@drawable/portrait_contactus" />
        </com.google.android.material.card.MaterialCardView>

        <TextView
            android:id="@+id/subcategory"
            style="@style/SmallMediumDarkText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="جبن1" />
    </LinearLayout>

    <com.google.android.material.switchmaterial.SwitchMaterial
        android:id="@+id/active_status"
        style="@style/SmallMediumDarkText"
        android:layout_width="wrap_content"
        app:thumbTint="@drawable/selector_switch_thumb"
        app:trackTint="@drawable/selector_switch_track"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:text="@string/activate"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cat_cont"
        app:switchPadding="5dp" />

    <com.google.android.material.switchmaterial.SwitchMaterial
        android:id="@+id/wanted_status"
        style="@style/SmallMediumDarkText"
        android:layout_width="wrap_content"
        app:thumbTint="@drawable/selector_switch_thumb"
        app:trackTint="@drawable/selector_switch_track"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_weight="1"
        android:text="@string/wanted"
        app:layout_constraintBottom_toBottomOf="@id/active_status"
        app:layout_constraintStart_toEndOf="@id/active_status"
        app:layout_constraintTop_toTopOf="@id/active_status"

        app:switchPadding="5dp" />

    <View
        android:id="@+id/divider1"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginVertical="10dp"
        android:background="@color/color_gray_7"
        android:elevation="2dp"
        app:layout_constraintTop_toBottomOf="@id/active_status" />

    <TextView
        android:id="@+id/product_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/description"
        android:theme="@style/Medium2BoldDarkText"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider1" />

    <TextView
        android:id="@+id/product_details"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="start"
        android:textAlignment="viewStart"
        android:theme="@style/Medium1RegularDarkText"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/product_title"
        tools:text="لاب توب لينوفو V14 82C6006GED AMD 3020E - ذاكرة 4 رام جيجابايت - هارد 1 تيرابايت اتش دي دي - كارت شاشة ايه ام دي راديون - شاشة 14 بوصة HD عالية الدقة 220 مضادة للتوهج دي اوه اس - لون رمادي ايرون" />

    <TextView
        android:id="@+id/translations"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/translation"
        android:theme="@style/Medium2BoldDarkText"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/product_details" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_translations"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="vertical"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintHeight_max="450dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/translations"
        tools:itemCount="2"
        tools:listitem="@layout/item_translation" />

    <View
        android:id="@+id/divider2"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginVertical="10dp"
        android:background="@color/color_gray_7"
        android:elevation="2dp"
        app:layout_constraintTop_toBottomOf="@id/rv_translations" />

        <include
            android:id="@+id/edit"
            layout="@layout/btn_progress"
            android:layout_width="match_parent"
            android:baselineAligned="false"
            android:layout_height="45dp"
            android:layout_marginTop="10dp"
            android:layout_weight="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider2"/>


</androidx.constraintlayout.widget.ConstraintLayout>

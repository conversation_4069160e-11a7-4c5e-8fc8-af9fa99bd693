package com.techcubics.albarkahyperdashboard.features.storage.fragments.tabs.discount

import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.inputmethod.EditorInfo
import android.widget.ArrayAdapter
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.techcubics.albarkahyperdashboard.R
import com.techcubics.albarkahyperdashboard.databinding.FragmentDiscountsBinding
import com.techcubics.albarkahyperdashboard.features.storage.adapters.DiscountsAdapter
import com.techcubics.albarkahyperdashboard.features.storage.adapters.holders.toBoolean
import com.techcubics.albarkahyperdashboard.features.storage.adapters.holders.toInt
import com.techcubics.albarkahyperdashboard.features.storage.fragments.StorageFragment
import com.techcubics.albarkahyperdashboard.features.storage.fragments.StorageFragmentDirections
import com.techcubics.albarkahyperdashboard.features.storage.intents.DiscountIntent
import com.techcubics.albarkahyperdashboard.features.storage.states.DiscountsStates
import com.techcubics.albarkahyperdashboard.features.storage.viewmodels.StorageViewModel
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.extensions.hideKeyboard
import com.techcubics.albarkahyperdashboard.utils.listeners.FragmentActionsHandler
import com.techcubics.albarkahyperdashboard.utils.listeners.OnItemClickListener
import com.techcubics.data.auth.utils.Constants
import com.techcubics.domain.storage.enums.ProductStatusTypes
import com.techcubics.domain.storage.models.Discount
import com.techcubics.domain.storage.requests.ProductsFilter
import com.techcubics.domain.storage.requests.Sort
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.getViewModel

class DiscountsFragment() : Fragment() ,OnItemClickListener{
   private var _binding:FragmentDiscountsBinding?=null
    private val binding get() = _binding!!

    private var sort: String = Sort.DESC.value

    private val viewModel by lazy { requireParentFragment().getViewModel<StorageViewModel>() }

    private lateinit var discountsAdapter: DiscountsAdapter
    private lateinit var rvListener: RecyclerView.OnScrollListener

    private val keysList = arrayListOf(
        "filter[product]",
        "filter[owner]",
        "filter[shop]",
    )
    private var key = keysList[0]
    private lateinit var value: String

    private lateinit var searchTypeAdapter: ArrayAdapter<String>
    private var filter: ProductsFilter = ProductsFilter(sort,mapOf(key to ""))
    private var bottomSheetBehavior: BottomSheetBehavior<ConstraintLayout>? = null
    private var scrollToStart = true
    private lateinit var searchTypeList: ArrayList<String>

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        if (_binding==null) {
            _binding = FragmentDiscountsBinding.inflate(inflater, container, false)
            initViews()
            events()
            observeViews()
        }
        return binding.root
    }
    private fun initViews() {
        binding.bottomSearch.search.textView.text = getString(com.techcubics.resources.R.string.search)
        binding.bottomSearch.clearFilter.textView.text = getString(com.techcubics.resources.R.string.clear_filter)
        bottomSheetBehavior = BottomSheetBehavior.from(binding.bottomSearch.root)
        bottomSheetBehavior?.state = BottomSheetBehavior.STATE_HIDDEN
        binding.search.searchProducts.hint = getString(com.techcubics.resources.R.string.products_search)
        binding.search.searchProducts.isClickable = true
        binding.search.searchProducts.isFocusable = false
        setDiscountsAdapter()
        getDiscounts(1)

    }
    override fun onResume() {
        super.onResume()
        (parentFragment as? StorageFragment)?.setBottomSheet(bottomSheetBehavior)
        searchTypeList = arrayListOf(
            getString(com.techcubics.resources.R.string.product_name),
            getString(com.techcubics.resources.R.string.owner_name),
            getString(com.techcubics.resources.R.string.shop_name)
        )
        setSearchKeyAdapter()
    }
    private fun setSearchKeyAdapter() {
        searchTypeAdapter = ArrayAdapter(
            requireContext(),
            R.layout.item_dropdown,
            searchTypeList
        )
        binding.bottomSearch.searchType.setAdapter(searchTypeAdapter)
        binding.bottomSearch.searchType.setOnItemClickListener { _, _, i, _ ->
            key = keysList[i]
        }
        key = keysList[0]
        binding.bottomSearch.searchType.setText(searchTypeList[0], false)
    }
    private fun setDiscountsAdapter() {
        discountsAdapter = DiscountsAdapter(mutableListOf(), this)
        binding.rvDiscounts.adapter = discountsAdapter
    }

    private fun getDiscounts(page: Int) {
        lifecycleScope.launch {
            viewModel.discountIntent.send(DiscountIntent.GetDiscounts(page, filter))
        }
    }
    private fun events() {
        binding.addDiscount.setOnClickListener {
            val action = StorageFragmentDirections.viewAddNewDiscountFragment(null)
            findNavController().navigate(action)
        }
        rvListener = object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                val lastVisibleItemPosition: Int =
                    (recyclerView.layoutManager as LinearLayoutManager).findLastVisibleItemPosition()
                println("$lastVisibleItemPosition")
                val totalItemCount: Int = recyclerView.layoutManager?.itemCount ?: 0
                if (lastVisibleItemPosition == totalItemCount - 1) {
                    println("$totalItemCount")
                    if (viewModel.disHasMorePagesState.value) {
                        scrollToStart = false
                        setFilterData(false, viewModel.disPageState.value)
                    }
                }
            }
        }
        binding.rvDiscounts.addOnScrollListener(rvListener)

        binding.bottomSearch.searchText.setOnEditorActionListener { textView, i, _ ->
            when (i) {
                EditorInfo.IME_ACTION_SEARCH -> {
                    textView.clearFocus()
                    textView.hideKeyboard(requireActivity())
                    setFilterData(false, 1)
                    true
                }

                else -> false
            }
        }

        binding.search.searchProducts.setOnClickListener {
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_EXPANDED
        }

        binding.bottomSearch.closeIcon.setOnClickListener {
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
        }
        binding.bottomSearch.search.root.setOnClickListener {
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
            setFilterData(true, 1)
        }
        binding.bottomSearch.clearFilter.root.setOnClickListener {
            binding.bottomSearch.searchType.setText(searchTypeList[0], false)
            binding.bottomSearch.sortCont.check(binding.bottomSearch.desc.id)
            binding.bottomSearch.statusCont.check(binding.bottomSearch.available.id)
            binding.bottomSearch.statusCont.visibility = View.INVISIBLE
            binding.bottomSearch.searchCont.visibility = View.VISIBLE
            binding.bottomSearch.searchText.setText("")
            key = keysList[0]
            value = ""
            sort = Sort.DESC.value
            filter = ProductsFilter(sort, mapOf(key to value))
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
            setFilterData(true, 1)
        }
    }
    private fun setSortData() {
        sort = Sort.DESC.value
        if (binding.bottomSearch.sortCont.checkedRadioButtonId == binding.bottomSearch.asc.id) {
            sort = Sort.ASC.value
        }
    }
    private fun setFilterData(isNewFilter: Boolean, page: Int) {
        if (!isNewFilter) {
            viewModel.filterStates.value?.let { f -> filter = f }
        }
        setSortData()
        value = binding.bottomSearch.searchText.text.toString()
        filter = ProductsFilter(sort, mapOf(key to value))
        getDiscounts(page)
    }

    private fun observeViews() {
        lifecycleScope.launch {
            viewModel.discountState.collect {
                binding.loading.root.visibility = View.GONE
                binding.msgLayout.root.visibility = View.GONE
                binding.pagingLoadingImg.visibility = View.GONE
                when (it) {
                    is DiscountsStates.Idle -> {}
                    is DiscountsStates.Loading -> {
                        binding.rvDiscounts.visibility = View.GONE
                        binding.loading.root.visibility = View.VISIBLE
                    }

                    is DiscountsStates.Pagination -> {
                        binding.pagingLoadingImg.visibility = View.VISIBLE
                    }

                    is DiscountsStates.ViewDiscounts -> {
                        Log.d("ptime", "appendProducts: 2- " + System.currentTimeMillis())
                        renderProducts(it.discounts)
                    }

                    is DiscountsStates.Error -> {
                        if(!it.error.contains(Constants.unauthenticated)){
                            Helper.showErrorDialog(requireContext(), it.error)
                        }
                    }

                    is DiscountsStates.ServerError -> {
                        setMsgLayout(it.error, com.techcubics.resources.R.raw.lottie_error)
                    }

                    is DiscountsStates.StatusError -> {
                        Helper.showErrorDialog(requireContext(), it.error)
                        val index =
                            discountsAdapter.discounts?.indexOfFirst { p -> p.discountId == it.discountId }
                        if (index != -1 && index != null) {
                            discountsAdapter.discounts?.get(index)?.status =
                                (!discountsAdapter.discounts?.get(index)?.status!!.toBoolean()).toInt()
                            discountsAdapter.notifyItemChanged(index)
                        }
                    }

                    else -> {}
                }
            }
        }
    }

    private fun setMsgLayout(msg: String?, lottieIcon: Int) {
        binding.rvDiscounts.visibility = View.GONE
        binding.msgLayout.root.visibility = View.VISIBLE
        binding.msgLayout.icon.setAnimation(lottieIcon)
        binding.msgLayout.tvMessage.text = msg
    }

    private fun renderProducts(discounts: MutableList<Discount>) {
        if (discounts.isEmpty()) {
            setMsgLayout(getString(com.techcubics.resources.R.string.message_empty_list_general), com.techcubics.resources.R.raw.lottie_empty)
        } else {
            binding.rvDiscounts.visibility = View.VISIBLE
            binding.msgLayout.root.visibility = View.GONE
        }
        discountsAdapter.updateDiscounts(discounts)
        if (scrollToStart) {
            binding.rvDiscounts.scrollToPosition(0)
        } else {
            scrollToStart = true
        }
        binding.rvDiscounts.viewTreeObserver.addOnGlobalLayoutListener(object :
            ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                // Remove the listener to avoid multiple callbacks
                binding.rvDiscounts.viewTreeObserver.removeOnGlobalLayoutListener(this)
                Log.d("ptime", "appendProducts: 3- " + System.currentTimeMillis())

                // The RecyclerView has finished laying out all its items
                // Add your code here
            }
        })
    }

    override fun onClick(id: Int, type: ProductStatusTypes) {
        lifecycleScope.launch {
            viewModel.discountIntent.send(DiscountIntent.SetStatus(id))
        }
    }



}
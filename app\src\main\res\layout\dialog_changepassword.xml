<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:theme="@style/Theme.TextInputLayout"
    android:orientation="vertical"
    android:background="@drawable/bg_popup_dialog"
    android:minWidth="300dp">
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/change_password"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    android:layout_marginTop="44dp"
                    android:textAppearance="@style/LargeBoldDarkText" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/old_password"
                    android:layout_marginTop="24dp"
                    android:layout_marginEnd="8dp"
                    android:textAppearance="@style/Medium2RegularDarkSearch"
                    android:textColor="@color/dark_txt_color"
                    android:layout_marginStart="8dp" />
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    app:hintEnabled="false"
                    app:passwordToggleEnabled="true"
                    app:boxStrokeWidth="0dp"
                    android:textColorHint="@color/project_medium_gray"
                    app:boxStrokeWidthFocused="0dp">

                    <EditText
                        android:id="@+id/old_pass"
                        android:textAppearance="@style/Medium2RegularDarkSearch"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:textDirection="ltr"
                        android:inputType="textPassword"
                        android:textCursorDrawable="@drawable/cursor_color"
                        android:background="@drawable/et_style_with_borders"
                        android:layout_marginStart="8dp"
                        android:layout_marginEnd="8dp"
                        android:padding="5dp"
                        android:textSize="14sp"
                        />
                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/new_password"
                    android:layout_marginTop="16dp"
                    android:textColor="@color/dark_txt_color"
                    android:textAppearance="@style/Medium2RegularDarkSearch"
                    android:layout_marginEnd="8dp"
                    android:layout_marginStart="8dp" />
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    app:hintEnabled="false"
                    app:passwordToggleEnabled="true"
                    app:boxStrokeWidth="0dp"
                    android:textColorHint="@color/project_medium_gray"
                    app:boxStrokeWidthFocused="0dp">

                    <EditText
                        android:id="@+id/new_password"
                        android:textAppearance="@style/Medium2RegularDarkSearch"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:background="@drawable/et_style_with_borders"
                        android:layout_marginStart="8dp"
                        android:textCursorDrawable="@drawable/cursor_color"
                        android:textDirection="ltr"
                        android:inputType="textPassword"
                        android:layout_marginEnd="8dp"
                        android:padding="5dp"
                        android:textSize="14sp"
                        />
                </com.google.android.material.textfield.TextInputLayout>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/confirm_password"
                    android:layout_marginTop="16dp"
                    android:textAppearance="@style/Medium2RegularDarkSearch"
                    android:textColor="@color/dark_txt_color"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    />
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    app:hintEnabled="false"
                    app:passwordToggleEnabled="true"
                    app:boxStrokeWidth="0dp"
                    android:textColorHint="@color/project_medium_gray"
                    app:boxStrokeWidthFocused="0dp">

                    <EditText
                        android:id="@+id/confirm_pass"
                        android:textAppearance="@style/Medium2RegularDarkSearch"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:background="@drawable/et_style_with_borders"
                        android:layout_marginStart="8dp"
                        android:inputType="textPassword"
                        android:textCursorDrawable="@drawable/cursor_color"
                        android:layout_marginEnd="8dp"
                        android:textDirection="ltr"
                        android:padding="5dp"
                        android:textSize="14sp"
                        />
                </com.google.android.material.textfield.TextInputLayout>
                <include
                    android:id="@+id/sign_btn_progress"
                    layout="@layout/btn_progress"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginTop="36dp"
                    android:layout_marginEnd="8dp"
                    android:layout_marginBottom="44dp"/>
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.core.widget.NestedScrollView>





</LinearLayout>

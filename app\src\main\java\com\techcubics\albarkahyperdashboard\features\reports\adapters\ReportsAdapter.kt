package com.techcubics.albarkahyperdashboard.features.reports.adapters

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyperdashboard.R
import com.techcubics.albarkahyperdashboard.databinding.ItemReportBinding
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.domain.reports.response.ReportResponse


class ReportsAdapter(
    private val context: Context,
    private var listOfReports: List<String>
) : RecyclerView.Adapter<ReportsHolderItem>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ReportsHolderItem {
        val itemBinding =
            ItemReportBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ReportsHolderItem(itemBinding)
    }


    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(
        holder: ReportsHolderItem,
        @SuppressLint("RecyclerView") position: Int
    ) {
        holder.count.text = listOfReports[position]
        when (position) {
            0 -> {
                holder.title.text =
                    context.getString(com.techcubics.resources.R.string.no_new_bills)
                holder.img.setImageResource(com.techcubics.resources.R.drawable.ic_new_bill)
            }

            1 -> {
                holder.title.text =
                    context.getString(com.techcubics.resources.R.string.no_preparing_bills)
                holder.img.setImageResource(com.techcubics.resources.R.drawable.ic_prep_bill)
            }

            2 -> {
                holder.title.text =
                    context.getString(com.techcubics.resources.R.string.no_delivered_bills)
                holder.img.setImageResource(com.techcubics.resources.R.drawable.ic_delivered_bill)
            }

            3 -> {
                holder.title.text =
                    context.getString(com.techcubics.resources.R.string.no_returned_bills)
                holder.img.setImageResource(com.techcubics.resources.R.drawable.ic_returned_bill)
            }

            4 -> {
                holder.title.text =
                    context.getString(com.techcubics.resources.R.string.no_cancelled_bills)
                holder.img.setImageResource(com.techcubics.resources.R.drawable.ic_cancelled_bill)
            }

            5 -> {
                holder.title.text =
                    context.getString(com.techcubics.resources.R.string.no_products)
                holder.img.setImageResource(com.techcubics.resources.R.drawable.ic_no_products)
            }

            6 -> {
                holder.title.text =
                    context.getString(com.techcubics.resources.R.string.no_customers)
                holder.img.setImageResource(com.techcubics.resources.R.drawable.ic_no_customer)
            }

            7 -> {
                holder.title.text =
                    context.getString(com.techcubics.resources.R.string.total_new_sales)
                holder.img.setImageResource(com.techcubics.resources.R.drawable.ic_t_new_bill)
            }

            8 -> {
                holder.title.text =
                    context.getString(com.techcubics.resources.R.string.total_preparing_sales)
                holder.img.setImageResource(com.techcubics.resources.R.drawable.ic_t_preparing_bill)
            }

            9 -> {

                holder.title.text =
                    context.getString(com.techcubics.resources.R.string.total_delivered_sales)
                holder.img.setImageResource(com.techcubics.resources.R.drawable.ic_t_delivered_bill)
            }

            10 -> {
                holder.title.text =
                    context.getString(com.techcubics.resources.R.string.total_returned_sales)
                holder.img.setImageResource(com.techcubics.resources.R.drawable.ic_t_returned_bill)
            }

            11 -> {

                holder.title.text =
                    context.getString(com.techcubics.resources.R.string.total_cancelled_sales)
                holder.img.setImageResource(com.techcubics.resources.R.drawable.ic_t_cancelled_bill)
            }

            12 -> {
                holder.title.text =
                    context.getString(com.techcubics.resources.R.string.total_sales)
                holder.img.setImageResource(com.techcubics.resources.R.drawable.ic_total_cost)
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateList(list: List<String>) {
        this.listOfReports = list
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int {
        return listOfReports.size
    }
}

class ReportsHolderItem(itemView: ItemReportBinding) : RecyclerView.ViewHolder(itemView.root) {
    val title = itemView.title
    val count = itemView.count
    val img = itemView.img

}

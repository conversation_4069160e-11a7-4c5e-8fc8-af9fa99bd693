package com.techcubics.albarkahyperdashboard.utils.components

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.res.ResourcesCompat
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyperdashboard.R
import com.techcubics.albarkahyperdashboard.features.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyperdashboard.features.more.adapters.LanguageAdapter
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.domain.auth.requests.UpdatePasswordRequest
import com.techcubics.domain.auth.responses.UpdatePasswordResponse
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class PopupDialog(context: Context) {

    private var dialog: Dialog
    private var mcontext: Context

    init {
        dialog = Dialog(context)
        mcontext = context
    }

//
//    fun showSessionExpiredDialog(
//        context: Context
//    ) {
//        dialog.setContentView(R.layout.dialog_session_expired)
//        val progressbar: ProgressBar = dialog.findViewById(R.id.session_expired_progressbar)
//        progressbar.visibility = View.VISIBLE
//        dialog.show()
//        dialog.window?.setLayout(
//            ViewGroup.LayoutParams.WRAP_CONTENT,
//            ViewGroup.LayoutParams.WRAP_CONTENT
//        )
//        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
//
//    }
//
//    fun showMaterialCodeDialog(
//        context: Context,
//        challangeCode: String?,
//        courseCodePopupLiveData: MutableLiveData<String>?
//    ) {
//        dialog.setContentView(R.layout.dialog_materialcode)
//        val startChallange: ConstraintLayout = dialog.findViewById(R.id.start_challange)
//        val shareCode: ConstraintLayout = dialog.findViewById(R.id.share_code)
//        val code : EditText = dialog.findViewById(R.id.code)
//        val title : MaterialTextView = dialog.findViewById(R.id.title)
//        val startChallangeTextView : TextView = startChallange.findViewById(R.id.textView)
//        val shareCodeTextView : TextView = shareCode.findViewById(R.id.textView)
//
//        if(challangeCode.isNullOrEmpty()){
//            startChallangeTextView.setText(com.techcubics.resources.R.string.add_material)
//            startChallange.setOnClickListener {
//                dialog.dismiss()
//                courseCodePopupLiveData?.postValue(code.text.toString())
//            }
//        }else{
//            title.setText(com.techcubics.resources.R.string.share_code_title)
//            shareCode.visibility = View.VISIBLE
//            startChallange.setBackgroundResource(com.techcubics.resources.R.drawable.btn_green_ripple)
//            startChallangeTextView.setText(com.techcubics.resources.R.string.start_challange_now)
//            shareCodeTextView.setText(com.techcubics.resources.R.string.share_code)
//            code.setText(challangeCode)
//            code.inputType = InputType.TYPE_NULL
//            code.setTextIsSelectable(true)
//            code.setOnClickListener {
//                code.clearFocus()
//                val text = code.text.toString()
//                val clipboardManager = mcontext.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
//                val clipData = ClipData.newPlainText("Challange Code", text)
//                clipboardManager.setPrimaryClip(clipData)
//                Toast.makeText(mcontext, mcontext.getString(com.techcubics.resources.R.string.text_copied), Toast.LENGTH_SHORT).show()
//            }
//            shareCode.setOnClickListener {
//                Helper.shareLink(context,context.getString(com.techcubics.resources.R.string.challenge_code)+": "+challangeCode)
//            }
//            startChallange.setOnClickListener {
//                dialog.dismiss()
//                courseCodePopupLiveData?.postValue(PagesEnum.ChallangeInfo.value)
//
//            }
//        }
//
//
//        dialog.show()
//        dialog.window?.setLayout(
//            ViewGroup.LayoutParams.WRAP_CONTENT,
//            ViewGroup.LayoutParams.WRAP_CONTENT
//        )
//        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
//
//    }
//
//    fun showTurnDialog(
//        context: Context,
//        turnNo: String?,
//        popupLiveData: MutableLiveData<String>
//    ){
//        dialog.setContentView(R.layout.dialog_booking_turnno)
//        val turnno: TextView = dialog.findViewById(R.id.turnno)
//        val btn = dialog.findViewById<ConstraintLayout>(R.id.ok)
//        val okTextview = btn.findViewById<TextView>(R.id.textView)
//        okTextview.setText(com.techcubics.resources.R.string.ok)
//        turnno.setText(turnNo)
//        btn.setOnClickListener {
//            dialog.dismiss()
//            popupLiveData.postValue("ok")
//        }
//        dialog.show()
//        dialog.window?.setLayout(
//            ViewGroup.LayoutParams.WRAP_CONTENT,
//            ViewGroup.LayoutParams.WRAP_CONTENT
//        )
//        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
//
//    }
//
//
//    //    fun showBannerDialog(
////        context: Context,
////        data : BannerData
////    ) {
////        dialog.setContentView(R.layout.dialog_banner)
////        val img: ImageView = dialog.findViewById(R.id.bannerImage)
////        val closebtn: ImageView = dialog.findViewById(R.id.close_btn)
////        val text: TextView = dialog.findViewById(R.id.banner_tv)
////        Helper.loadImage(context,data.image,img)
////        val result = Html.fromHtml(data.content).toString()
////
////        text.text = result
////
////        closebtn.setOnClickListener {
////            dialog.dismiss()
////        }
////        dialog.show()
////        dialog.window?.setLayout(
////            ViewGroup.LayoutParams.WRAP_CONTENT,
////            ViewGroup.LayoutParams.WRAP_CONTENT
////        )
////        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
////
////
////    }
////
////    fun multiChoiceDialog(
////        context: Context,
////        shopTypePopup: Int,
////        itemList: List<Specializations>,
////        popupMenuItemLiveData: MutableLiveData<ArrayList<Specializations?>>
////    ) {
////        dialog.setContentView(shopTypePopup)
////        val shoptype_rv: RecyclerView = dialog.findViewById(R.id.shoptypeRV)
////        val yesbtn: AppCompatButton = dialog.findViewById(R.id.yes)
////        val nobtn: AppCompatButton = dialog.findViewById(R.id.no)
////        shoptype_rv.adapter = BranchesAdapter(
////            context, itemList, popupMenuItemLiveData, yesbtn, nobtn,
////            dialog
////        )
////
////        dialog.show()
////        dialog.window?.setLayout(
////            ViewGroup.LayoutParams.WRAP_CONTENT,
////            ViewGroup.LayoutParams.WRAP_CONTENT
////        )
////        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
////
////    }
////
    fun btnFinishedSuccessfully(
        constraintLayout: ConstraintLayout,
        textview: TextView,
        progressbar: ProgressBar
    ) {
        CoroutineScope(Dispatchers.Main).launch {
            constraintLayout.background = ResourcesCompat.getDrawable(
                mcontext.resources, com.techcubics.resources.R.drawable.btn_green,
                mcontext.theme
            )
            progressbar.visibility = View.GONE
            textview.visibility = View.VISIBLE
            textview.text = mcontext.getString(com.techcubics.resources.R.string.done)
            delay(1000)
            constraintLayout.background = ResourcesCompat.getDrawable(
                mcontext.resources, com.techcubics.resources.R.drawable.btn_style,
                mcontext.theme
            )
            progressbar.visibility = View.GONE
            textview.text = mcontext.getString(com.techcubics.resources.R.string.change)
            dialog.dismiss()
        }

    }

     fun cancelOrReturnConfirmMessage(
         context: Context,
         popup: Int,
         TAG: String?,
         popupMenuItemLiveData: MutableLiveData<String?>
    ){
        dialog.setContentView(popup)
        val message: EditText = dialog.findViewById(R.id.message)
        val header : TextView = dialog.findViewById(R.id.header)
        val close: ConstraintLayout = dialog.findViewById(R.id.close)
        val cancel : ConstraintLayout = dialog.findViewById(R.id.cancel)
        val closeTextView : TextView = close.findViewById(R.id.textView)
        val cancelTextView : TextView = cancel.findViewById(R.id.textView)
        val cancelImg : ImageView = dialog.findViewById(R.id.close_img)

         closeTextView.text = context.getString(com.techcubics.resources.R.string.back)
         cancelTextView.text = context.getString(com.techcubics.resources.R.string.ok)

         if(TAG == "cancel"){
            header.text = context.getString(com.techcubics.resources.R.string.cancel_reason)
        }else{
            header.text = context.getString(com.techcubics.resources.R.string.return_reason)
        }
        cancelImg.setOnClickListener {
            dialog.dismiss()
        }
        cancel.setOnClickListener {
            if(message.text.isNullOrEmpty()){
                Helper.showErrorDialog(context,context.getString(com.techcubics.resources.R.string.message_empty_list_general))
            }else{
                popupMenuItemLiveData.postValue(message.text.toString()+","+TAG)
            }
            dialog.dismiss()
        }
        close.setOnClickListener {
            dialog.dismiss()
        }
        dialog.show()
        dialog.window?.setLayout(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (dialog.window != null) {
            dialog.setCancelable(true)
        }

    }

    private fun btnFinishedFailed(
        constraintLayout: ConstraintLayout,
        textview: TextView,
        progressbar: ProgressBar
    ) {
        CoroutineScope(Dispatchers.Main).launch {
            constraintLayout.background = ResourcesCompat.getDrawable(
                mcontext.resources, com.techcubics.resources.R.drawable.btn_red,
                mcontext.theme
            )
            progressbar.visibility = View.GONE
            textview.visibility = View.VISIBLE
            textview.text = mcontext.getString(com.techcubics.resources.R.string.fail)
            delay(1500)
            constraintLayout.background = ResourcesCompat.getDrawable(
                mcontext.resources, com.techcubics.resources.R.drawable.btn_style,
                mcontext.theme
            )
            progressbar.visibility = View.GONE
            textview.text = mcontext.getString(com.techcubics.resources.R.string.change)
        }

    }


    fun showLanguageDialog(
        context: Context,
        languagePopup: Int,
        listOfLanguages: List<String>,
        popupMenuItemLiveData: MutableLiveData<String>
    ) {
        dialog.setContentView(languagePopup)
        val language_rv: RecyclerView = dialog.findViewById(R.id.languageRV)

        language_rv.adapter = LanguageAdapter(
            context,
            listOfLanguages,
            popupMenuItemLiveData,
            dialog
        )

        dialog.show()
        dialog.window?.setLayout(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (dialog.window != null) {
            dialog.setCancelable(true)
        }
    }


    fun showDialog(
    mutableliveDate: MutableLiveData<UpdatePasswordRequest?>,
    viewLifecycleOwner: LifecycleOwner,
    authViewModel: AuthViewModel,
    updatePasswordResponseMutableLiveData: MutableLiveData<UpdatePasswordResponse?>
    ) {
        dialog.setContentView(R.layout.dialog_changepassword)
        val oldPass: EditText = dialog.findViewById(R.id.old_pass)
        val newPass: EditText = dialog.findViewById(R.id.new_password)
        val confirmPass: EditText = dialog.findViewById(R.id.confirm_pass)
        val constraintLayout: ConstraintLayout = dialog.findViewById(R.id.sign_btn_progress)
        val progressbar: ProgressBar = constraintLayout.findViewById(R.id.progressBar2)
        val textview: TextView = constraintLayout.findViewById(R.id.textView)


        textview.text = mcontext.getString(com.techcubics.resources.R.string.change)


        constraintLayout.setOnClickListener {
            if (checkUiValidity(oldPass, newPass, confirmPass)) {
                progressbar.visibility = View.VISIBLE
                textview.visibility = View.GONE
                mutableliveDate.postValue(
                    UpdatePasswordRequest(
                        true,
                        oldPass.text.toString(),
                        newPass.text.toString(),
                        confirmPass.text.toString()
                    )
                )
            }
        }

        mutableliveDate.observe(viewLifecycleOwner) {
            if (it != null) {
                if (it.check) {
                    authViewModel.updatePassword(
                        UpdatePasswordRequest(
                            true,
                            it.old_password,
                            it.password,
                            it.password_confirmation
                        )
                    )
                }
                mutableliveDate.value = null
            }
        }


        updatePasswordResponseMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                dialog.dismiss()
                if (it.success!!) {
                    btnFinishedSuccessfully(constraintLayout, textview, progressbar)
                } else {
                    btnFinishedFailed(constraintLayout, textview, progressbar)
                    Helper.showErrorDialog(
                        mcontext,
                        it.message!!
                    )
                }
                updatePasswordResponseMutableLiveData.value = null
            }
        }
        dialog.show()
        dialog.window?.setLayout(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (dialog.window != null) {
            dialog.setCancelable(true)
        }
    }

    fun checkUiValidity(
        oldPass: EditText,
        newPass: EditText,
        confirmPass: EditText
    ): Boolean {

        var check = true

        if (oldPass.text.isNullOrEmpty()) {
            Helper.showErrorDialog(mcontext,mcontext.getString(com.techcubics.resources.R.string.invalid_old_password))
            check = false
        } else if (newPass.text.isNullOrEmpty()) {
            Helper.showErrorDialog(mcontext,mcontext.getString(com.techcubics.resources.R.string.invalid_new_password))
            check = false
        } else if (!confirmPass.text.toString().equals(newPass.text.toString())
            || confirmPass.text.isNullOrEmpty()
        ) {
            Helper.showErrorDialog(mcontext,mcontext.getString(com.techcubics.resources.R.string.confirm_password_isincorrect))
            check = false
        }


        return check
    }
//
//
//    fun showTermsAndConditionsDialog(
//        context: Context,
//        termsAndConditionsData: String,
//        sharedPreferencesManager: SharedPreferencesManager,
//        termsConditionsCheckbox: CheckBox
//    ) {
//        dialog.setContentView(R.layout.dialog_terms_and_conditions)
//        val constraintLayout: ConstraintLayout = dialog.findViewById(R.id.sign_btn_progress)
//        val progressbar: ProgressBar = constraintLayout.findViewById(R.id.progressBar2)
//        val textview: TextView = constraintLayout.findViewById(R.id.textView)
//        val webview: WebView = dialog.findViewById(R.id.terms_conditions_webview)
//
//        textview.text = context.getString(com.techcubics.resources.R.string.ok)
//        constraintLayout.setOnClickListener {
//            CoroutineScope(Dispatchers.Main).launch {
//                progressbar.visibility = View.VISIBLE
//                textview.visibility = View.GONE
//                delay(700)
//                sharedPreferencesManager.setTerms(true)
//                termsConditionsCheckbox.isChecked = true
//                btnFinishedSuccessfully(constraintLayout, textview, progressbar)
//            }
//
//        }
//
//
//        webview.loadDataWithBaseURL(
//            null,
//            termsAndConditionsData,
//            "text/html",
//            "utf-8",
//            null
//        )
//        dialog.show()
//        dialog.window?.setLayout(
//            ViewGroup.LayoutParams.WRAP_CONTENT,
//            ViewGroup.LayoutParams.WRAP_CONTENT
//        )
//        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
//
//    }
//
//    fun onDismiss() {
//        dialog.dismiss()
//    }


}

data class BasicData(
    var name: String? = null,
    var id1: Int? = null,
    var id2 : Int? = null
)
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:theme="@style/Theme.Colored">

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/textInputLayout"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:boxCornerRadiusBottomEnd="8dp"
        app:boxCornerRadiusBottomStart="8dp"
        app:boxCornerRadiusTopEnd="8dp"
        app:boxCornerRadiusTopStart="8dp"
        app:boxStrokeColor="@drawable/text_input_box_stroke_color"
        app:hintEnabled="false"
        app:boxBackgroundColor="@color/white"
        app:startIconDrawable="@drawable/ic_search"
        app:startIconTint="@color/light_txt_color1">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/search_products"
            style="@style/Medium2RegularDarkSearch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/products_search"
            android:inputType="text"
            android:imeOptions="actionSearch"
            android:textAlignment="viewStart" />
    </com.google.android.material.textfield.TextInputLayout>
</LinearLayout>
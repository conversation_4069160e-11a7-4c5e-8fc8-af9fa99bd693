package com.techcubics.albarkahyperdashboard.features.storage.fragments.tabs.product

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.inputmethod.EditorInfo
import android.widget.ArrayAdapter
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.techcubics.albarkahyperdashboard.databinding.FragmentProductsBinding
import com.techcubics.albarkahyperdashboard.features.storage.adapters.ProductsAdapter
import com.techcubics.albarkahyperdashboard.features.storage.fragments.StorageFragment
import com.techcubics.albarkahyperdashboard.features.storage.intents.ProductIntent
import com.techcubics.albarkahyperdashboard.features.storage.states.ProductsStates
import com.techcubics.albarkahyperdashboard.features.storage.viewmodels.StorageViewModel
import com.techcubics.albarkahyperdashboard.utils.components.AddProductDialog
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.extensions.hideKeyboard
import com.techcubics.albarkahyperdashboard.utils.listeners.FragmentActionsHandler
import com.techcubics.albarkahyperdashboard.utils.listeners.OnAttachChangeListener
import com.techcubics.albarkahyperdashboard.utils.listeners.OnItemClickListener
import com.techcubics.data.auth.utils.Constants
import com.techcubics.domain.storage.enums.ProductStatusTypes
import com.techcubics.domain.storage.models.Product
import com.techcubics.domain.storage.requests.ProductsFilter
import com.techcubics.domain.storage.requests.Sort
import com.techcubics.domain.storage.requests.Status
import com.techcubics.resources.R
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.getViewModel


class ProductsFragment() : Fragment(), OnItemClickListener, OnAttachChangeListener {
    private var sort: String = Sort.DESC.value
    private var isAttached: Boolean = false
    private var _binding: FragmentProductsBinding? = null
    private val binding get() = _binding!!
    private val viewModel by lazy { requireParentFragment().getViewModel<StorageViewModel>() }

    private lateinit var productsAdapter: ProductsAdapter
    private lateinit var rvListener: RecyclerView.OnScrollListener
    companion object{
      const val  TAG = "ProductsFragment"
    }

    private val keysList = arrayListOf(
        "filter[all]",
        "filter[name]",
        "filter[owner]",
        "filter[shop]",
        "filter[category]",
        "filter[sub_category]",
        "filter[status]"
    )
    private var key = keysList[0]
    private lateinit var value: String

    private lateinit var searchTypeAdapter: ArrayAdapter<String>
    private var filter: ProductsFilter = ProductsFilter(sort,mapOf(key to ""))
    private var bottomSheetBehavior: BottomSheetBehavior<ConstraintLayout>? = null
    private var scrollToStart = true
    private lateinit var searchTypeList: ArrayList<String>

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        if (_binding==null) {
            _binding = FragmentProductsBinding.inflate(inflater, container, false)
            initViews()
            events()
            observeViews()
        }
        return binding.root
    }


    private fun initViews() {
        binding.bottomSearch.search.textView.text = getString(R.string.search)
        binding.bottomSearch.clearFilter.textView.text = getString(R.string.clear_filter)
        bottomSheetBehavior = BottomSheetBehavior.from(binding.bottomSearch.root)
        bottomSheetBehavior?.state = BottomSheetBehavior.STATE_HIDDEN
        binding.search.searchProducts.hint = getString(R.string.products_search)
        binding.search.searchProducts.isClickable = true
        binding.search.searchProducts.isFocusable = false
        setProductsAdapter()
        getProducts(1)

    }
    override fun onResume() {
        super.onResume()
        (parentFragment as? StorageFragment)?.setBottomSheet(bottomSheetBehavior)
        searchTypeList = arrayListOf(
            getString(R.string.all),
            getString(R.string.product_name),
            getString(R.string.owner_name),
            getString(R.string.shop_name),
            getString(R.string.category_name),
            getString(R.string.subcategory_name),
            getString(R.string.product_status_title)
        )
        setSearchKeyAdapter()
    }
    private fun setSearchKeyAdapter() {
        searchTypeAdapter = ArrayAdapter(
            requireContext(),
            com.techcubics.albarkahyperdashboard.R.layout.item_dropdown,
            searchTypeList
        )
        binding.bottomSearch.searchType.setAdapter(searchTypeAdapter)
        binding.bottomSearch.searchType.setOnItemClickListener { _, _, i, l ->
            key = keysList[i]
            if (i == 6) {
                binding.bottomSearch.statusCont.visibility = View.VISIBLE
                binding.bottomSearch.titleStatus.visibility = View.VISIBLE
                binding.bottomSearch.searchCont.visibility = View.INVISIBLE
            } else {
                binding.bottomSearch.statusCont.visibility = View.INVISIBLE
                binding.bottomSearch.titleStatus.visibility = View.INVISIBLE
                binding.bottomSearch.searchCont.visibility = View.VISIBLE
            }
        }
        key = keysList[0]
        binding.bottomSearch.searchType.setText(searchTypeList[0], false)
    }

    private fun setProductsAdapter() {
        productsAdapter = ProductsAdapter(mutableListOf(), this)
        binding.rvProducts.adapter = productsAdapter
    }

    private fun getProducts(page: Int) {
        lifecycleScope.launch {
            viewModel.productIntent.send(ProductIntent.GetProducts(page, filter))
        }
    }

    private fun events() {
        binding.addProduct.setOnClickListener {
            if (!isAttached) {
                val dialog =
                    childFragmentManager.findFragmentByTag("add product Dialog") as AddProductDialog?
                        ?: AddProductDialog(this)
                dialog.show(childFragmentManager, "add product Dialog")
            }
        }
        rvListener = object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                val lastVisibleItemPosition: Int =
                    (recyclerView.layoutManager as LinearLayoutManager).findLastVisibleItemPosition()
                println("$lastVisibleItemPosition")
                val totalItemCount: Int = recyclerView.layoutManager?.itemCount ?: 0
                if (lastVisibleItemPosition == totalItemCount - 1) {
                    println("$totalItemCount")
                    if (viewModel.hasMorePagesState.value) {
                        scrollToStart = false
                        setFilterData(false, viewModel.pageState.value)
                    }
                }
            }
        }
        binding.rvProducts.addOnScrollListener(rvListener)

        binding.bottomSearch.searchText.setOnEditorActionListener { textView, i, _ ->
            when (i) {
                EditorInfo.IME_ACTION_SEARCH -> {
                    textView.clearFocus()
                    textView.hideKeyboard(requireActivity())
                    setFilterData(false, 1)
                    true
                }

                else -> false
            }
        }

        binding.search.searchProducts.setOnClickListener {
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_EXPANDED
        }

        binding.bottomSearch.closeIcon.setOnClickListener {
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
        }
        binding.bottomSearch.search.root.setOnClickListener {
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
            setFilterData(true, 1)
        }
        binding.bottomSearch.clearFilter.root.setOnClickListener {
            binding.bottomSearch.searchType.setText(searchTypeList[0], false)
            binding.bottomSearch.sortCont.check(binding.bottomSearch.desc.id)
            binding.bottomSearch.statusCont.check(binding.bottomSearch.available.id)
            binding.bottomSearch.statusCont.visibility = View.INVISIBLE
            binding.bottomSearch.searchCont.visibility = View.VISIBLE
            binding.bottomSearch.searchText.setText("")
            key = keysList[0]
            value = ""
            sort = Sort.DESC.value
            filter = ProductsFilter(sort, mapOf(key to value))
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
            setFilterData(true, 1)
        }
    }

    private fun setSortData() {
        sort = Sort.DESC.value
        if (binding.bottomSearch.sortCont.checkedRadioButtonId == binding.bottomSearch.asc.id) {
            sort = Sort.ASC.value
        }
    }

    private fun setStatusData() {
        value = when (binding.bottomSearch.statusCont.checkedRadioButtonId) {
            binding.bottomSearch.available.id -> {
                Status.Available.value
            }

            binding.bottomSearch.unavailable.id -> {
                Status.Unavailable.value
            }

            else -> {
                Status.Available.value
            }
        }.toString()
    }

    private fun setFilterData(isNewFilter: Boolean, page: Int) {
        if (!isNewFilter) {
            viewModel.filterStates.value?.let { f -> filter = f }
        }
        setSortData()
        if (key == keysList[6]) {
            setStatusData()
        } else {
            value = binding.bottomSearch.searchText.text.toString()
        }
        filter = ProductsFilter(sort, mapOf(key to value))
        getProducts(page)
    }

    private fun observeViews() {
        lifecycleScope.launch {
            viewModel.productState.collect {
                binding.loading.root.visibility = View.GONE
                binding.msgLayout.root.visibility = View.GONE
                binding.pagingLoadingImg.visibility = View.GONE
                when (it) {
                    is ProductsStates.Idle -> {}
                    is ProductsStates.Loading -> {
                        binding.rvProducts.visibility = View.GONE
                        binding.loading.root.visibility = View.VISIBLE
                    }

                    is ProductsStates.Pagination -> {
                        binding.pagingLoadingImg.visibility = View.VISIBLE
                    }

                    is ProductsStates.ViewProducts -> {
                        Log.d("ptime", "appendProducts: 2- " + System.currentTimeMillis())
                        renderProducts(it.products)
                    }

                    is ProductsStates.Error -> {
                        if(!it.error.contains(Constants.unauthenticated)){
                            Helper.showErrorDialog(requireContext(), it.error)
                        }
                    }

                    is ProductsStates.ServerError -> {
                        setMsgLayout(it.error, R.raw.lottie_error)
                    }

                    is ProductsStates.StatusError -> {
                        Helper.showErrorDialog(requireContext(), it.error)
                        val index =
                            productsAdapter.products?.indexOfFirst { p -> p.productId == it.productId }
                        if (index != -1 && index != null) {
                            if (it.type==ProductStatusTypes.Active) {
                                productsAdapter.products?.get(index)?.status =
                                    !productsAdapter.products?.get(index)?.status!!
                            }else{
                                productsAdapter.products?.get(index)?.wanted =
                                    !productsAdapter.products?.get(index)?.wanted!!
                            }
                            productsAdapter.notifyItemChanged(index)
                        }
                    }

                    else -> {}
                }
            }
        }
    }

    private fun setMsgLayout(msg: String?, lottieIcon: Int) {
        binding.rvProducts.visibility = View.GONE
        binding.msgLayout.root.visibility = View.VISIBLE
        binding.msgLayout.icon.setAnimation(lottieIcon)
        binding.msgLayout.tvMessage.text = msg
    }

    private fun renderProducts(products: MutableList<Product>) {
        if (products.isEmpty()) {
            setMsgLayout(getString(R.string.message_empty_list_general), R.raw.lottie_empty)
        } else {
            binding.rvProducts.visibility = View.VISIBLE
            binding.msgLayout.root.visibility = View.GONE
        }
        productsAdapter.updateProducts(products)
        if (scrollToStart) {
            binding.rvProducts.scrollToPosition(0)
        } else {
            scrollToStart = true
        }
        binding.rvProducts.viewTreeObserver.addOnGlobalLayoutListener(object :
            ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                // Remove the listener to avoid multiple callbacks
                binding.rvProducts.viewTreeObserver.removeOnGlobalLayoutListener(this)
                Log.d(TAG, "appendProducts: 3- " + System.currentTimeMillis())

                // The RecyclerView has finished laying out all its items
                // Add your code here
            }
        })
    }

    override fun onClick(id: Int, type: ProductStatusTypes) {
        lifecycleScope.launch {
            viewModel.productIntent.send(ProductIntent.SetStatus(id, type))
        }
    }

    override fun updateAttached(isAttached: Boolean) {
        this.isAttached = isAttached
        this.isAttached = isAttached
    }

}
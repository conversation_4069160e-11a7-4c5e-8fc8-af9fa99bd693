package com.techcubics.data.owner.remote

import com.techcubics.data.owner.utils.Constants
import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.orders.models.Order
import com.techcubics.domain.orders.models.OrderStatus
import com.techcubics.domain.owner.requests.OwnerRequest
import com.techcubics.domain.owner.requests.UpdateOwnerPasswordRequest
import com.techcubics.domain.storage.models.Owner
import retrofit2.Response
import retrofit2.http.*

interface EndPoints {
    @GET(Constants.owners)
    suspend fun getOwners(
        @Path("userType") userType: String,
        @Query("page") page: Int? = null
    ): Response<BaseResponse<MutableList<Owner>>>

    @FormUrlEncoded
    @POST(Constants.filter)
    suspend fun ownersByFilter(
        @Path("userType") userType: String,

        @Query("page") page: Int,
        @Field("paginator") paginator: Int,
        @FieldMap filter: Map<String, String>
    ): Response<BaseResponse<MutableList<Owner>>>

    @POST(Constants.owners)
    suspend fun createOwner(
        @Path("userType") userType: String,
        @Body request: OwnerRequest
    ): Response<BaseResponse<Nothing>>

    @GET(Constants.ownerDetails)
    suspend fun ownerDetails(
        @Path("userType") userType: String,
        @Path("id") id: String
    ): Response<BaseResponse<Owner>>


    @GET(Constants.updateOwnerStatus)
    suspend fun updateOwnerStatus(
        @Path("userType") userType: String,
        @Path("id") id: String
    ): Response<BaseResponse<Nothing>>

    @POST(Constants.updateOwner)
    suspend fun updateOwner(
        @Path("userType") userType: String,
        @Path("id") id: String, @Body request: OwnerRequest
    ): Response<BaseResponse<Nothing>>

    @POST(Constants.updatePassword)
    suspend fun updateOwnerPassword(
        @Path("userType") userType: String,
        @Body request: UpdateOwnerPasswordRequest
    ): Response<BaseResponse<Nothing>>
}
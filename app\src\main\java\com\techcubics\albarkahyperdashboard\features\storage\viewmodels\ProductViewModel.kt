package com.techcubics.albarkahyperdashboard.features.storage.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.techcubics.albarkahyperdashboard.features.storage.intents.ProductIntent
import com.techcubics.albarkahyperdashboard.features.storage.states.ProductsStates
import com.techcubics.domain.common.Constants
import com.techcubics.domain.orders.requests.DeleteImageRequest
import com.techcubics.domain.storage.enums.ProductAction
import com.techcubics.domain.storage.enums.ProductStatusTypes
import com.techcubics.domain.storage.requests.AddNewProduct
import com.techcubics.domain.storage.usecases.DeleteImagesUseCase
import com.techcubics.domain.storage.usecases.GetCategoriesUseCase
import com.techcubics.domain.storage.usecases.GetOwnersUseCase
import com.techcubics.domain.storage.usecases.GetProductDetailsUseCase
import com.techcubics.domain.storage.usecases.GetShopsByOwnerIdUseCase
import com.techcubics.domain.storage.usecases.GetSubCategoriesUseCase
import com.techcubics.domain.storage.usecases.SetStatusUseCase
import com.techcubics.domain.storage.usecases.StoreProductUseCase
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.consumeAsFlow
import kotlinx.coroutines.launch

class ProductViewModel(
    private val getProductDetailsUseCase: GetProductDetailsUseCase,
    private val setStatusUseCase: SetStatusUseCase,
    private val getOwnersUseCase: GetOwnersUseCase,
    private val getShopsByOwnerIdUseCase: GetShopsByOwnerIdUseCase,
    private val getCategoriesUseCase: GetCategoriesUseCase,
    private val getSubCategoriesUseCase: GetSubCategoriesUseCase,
    private val storeProductUseCase: StoreProductUseCase,
    private val deleteImagesUseCase: DeleteImagesUseCase
) : ViewModel() {
    val productIntent = Channel<ProductIntent>(Channel.UNLIMITED)
    private val mutableSeconds = MutableLiveData<Float>()
    val seconds: LiveData<Float> get() = mutableSeconds
    private val _productState = MutableStateFlow<ProductsStates>(ProductsStates.Idle)
    val productState: StateFlow<ProductsStates> get() = _productState

    init {
        handleIntent()
    }

    private fun handleIntent() {
        viewModelScope.launch {
            productIntent.consumeAsFlow().collect {
                when (it) {
                    is ProductIntent.GetProductDetails -> getProductDetails(it.productId)
                    is ProductIntent.SetStatus -> setStatus(it.productId, it.type)
                    is ProductIntent.GetOwners -> getOwners()
                    is ProductIntent.GetShopsByOwnerId -> getShopsByOwnerId(it.ownerId)
                    ProductIntent.GetCategories -> getCategories()
                    is ProductIntent.GetSubCategoriesByCatId -> getSubCategoriesByCatId(it.catId)
                    is ProductIntent.StoreUpdateProduct -> storeProduct(it.request, it.action)
                    is ProductIntent.DeleteImagesAndUpdateProduct -> deleteImages(
                        it.deleteImageRequest,
                        it.request,
                        it.action
                    )

                    else -> {}
                }
            }
        }
    }

    private fun deleteImages(
        deleteImagesRequest: DeleteImageRequest,
        request: AddNewProduct,
        action: ProductAction
    ) {
        _productState.value = ProductsStates.Loading
        viewModelScope.launch {
            val result = deleteImagesUseCase.invoke(deleteImagesRequest)
            try {
                if (result?.data != null && result.status == true) {
                    storeProduct(request, action)
                } else {
                    _productState.value = ProductsStates.Error(result?.message ?: "Error occurred while deleting images")
                }
            } catch (e: Exception) {
                _productState.value = ProductsStates.Error(e.localizedMessage)
            }
        }
    }

    private fun storeProduct(request: AddNewProduct, action: ProductAction) {
        _productState.value = ProductsStates.Loading
        viewModelScope.launch {
            val result = storeProductUseCase.invoke(request, action)
            _productState.value = try {
                if (result?.data != null) {
                    ProductsStates.Success
                } else {
                    ProductsStates.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                ProductsStates.Error(e.localizedMessage)
            }
        }
    }

    private fun getSubCategoriesByCatId(catId: Int) {
        _productState.value = ProductsStates.Loading
        viewModelScope.launch {
            val result = getSubCategoriesUseCase.invoke(catId)
            _productState.value = try {
                if (result?.data != null) {
                    ProductsStates.ViewSubCategories(result.data)
                } else {
                    ProductsStates.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                ProductsStates.Error(e.localizedMessage)
            }
        }
    }

    private fun getCategories() {
        _productState.value = ProductsStates.Loading
        viewModelScope.launch {
            val result = getCategoriesUseCase.invoke()
            _productState.value = try {
                if (result?.data != null) {
                    ProductsStates.ViewCategories(result.data)
                } else {
                    ProductsStates.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                ProductsStates.Error(e.localizedMessage)
            }
        }
    }

    private fun getShopsByOwnerId(ownerId: Int) {
        _productState.value = ProductsStates.Loading
        viewModelScope.launch {
            val result = getShopsByOwnerIdUseCase.invoke(ownerId)
            _productState.value = try {
                if (result?.data != null) {
                    ProductsStates.ViewShops(result.data)
                } else {
                    ProductsStates.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                ProductsStates.Error(e.localizedMessage)
            }
        }
    }

    private fun getOwners() {
        _productState.value = ProductsStates.Loading
        viewModelScope.launch {
            val result = getOwnersUseCase.invoke()
            _productState.value = try {
                if (result?.data != null) {
                    ProductsStates.ViewOwners(result.data)
                } else {
                    ProductsStates.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                ProductsStates.Error(e.localizedMessage)
            }
        }
    }

    private fun getProductDetails(productId: Int) {
        _productState.value = ProductsStates.Loading
        viewModelScope.launch {
            val result = getProductDetailsUseCase.invoke(productId)
            _productState.value = try {
                if (result?.message?.contains(Constants.SERVER_ERROR) == true) {
                    ProductsStates.ServerError(result.message!!)
                } else if (result?.data != null) {
                   ProductsStates.ViewProductDetails(result.data)
                } else {
                    ProductsStates.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                ProductsStates.Error(e.localizedMessage)
            }
        }
    }

    private fun setStatus(productId: Int, type: ProductStatusTypes) {
        viewModelScope.launch {
            val result = setStatusUseCase.invoke(productId, type)
            _productState.value = try {
                if (result?.data != null) {
                    ProductsStates.Idle
                } else {
                    ProductsStates.StatusError(result?.message ?: "error", productId,type)
                }
            } catch (e: Exception) {
                ProductsStates.StatusError(e.localizedMessage, productId,type)
            }
        }
    }

    fun setSeconds(sec: Float) {
        mutableSeconds.value = sec
    }
}
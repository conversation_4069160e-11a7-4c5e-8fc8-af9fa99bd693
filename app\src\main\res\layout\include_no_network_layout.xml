<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/networkView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:background="@color/white"
    android:visibility="visible">
    <LinearLayout
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center">
        <ImageView
            android:id="@+id/imageView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.539"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="200dp"
            android:layout_height="200dp"
            android:src="@drawable/portrait_internet_conn" />

        <TextView
            android:id="@+id/textView2"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imageView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/oops"
            style="@style/ExtraLargeRegularAppColorText"
            />

        <TextView
            android:id="@+id/nonetworkTv"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView2"
            app:layout_constraintVertical_bias="0.0"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:text="@string/connecttotnetwork"
            style="@style/SmallMediumAppColor2Text" />
        <ImageView
            android:id="@+id/refresh"
            android:layout_width="50dp"
            android:layout_height="50dp"
            app:layout_constraintTop_toBottomOf="@id/nonetworkTv"
            android:layout_marginTop="5dp"
            android:src="@drawable/refresh"
            android:clickable="true"
            android:background="?attr/selectableItemBackground"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>
    </LinearLayout>



</androidx.constraintlayout.widget.ConstraintLayout>

<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F0F0F1"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".features.storage.fragments.tabs.discount.DiscountsFragment">

    <include
        android:id="@+id/search"
        layout="@layout/include_search_layout"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_marginTop="5dp"
        android:visibility="gone"
        android:layout_marginHorizontal="10dp"
        android:layout_height="wrap_content"/>

    <include
        android:id="@+id/loading"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:elevation="20dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/msg_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        layout="@layout/include_placeholder"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/search" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_discounts"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/search"
        tools:listitem="@layout/item_discount" />

    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/add_discount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:icon="@drawable/ic_add"
        app:iconTint="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/pagingLoadingImg"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_gravity="bottom"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_rawRes="@raw/lottie_pager_loading" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:elevation="16dp">

        <include
            android:id="@+id/bottom_search"
            layout="@layout/include_products_filter" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
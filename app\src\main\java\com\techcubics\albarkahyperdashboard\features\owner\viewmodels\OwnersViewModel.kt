package com.techcubics.albarkahyperdashboard.features.owner.viewmodels

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.techcubics.albarkahyperdashboard.features.owner.intents.OwnersIntent
import com.techcubics.albarkahyperdashboard.features.owner.states.OwnersViewState
import com.techcubics.domain.common.Constants
import com.techcubics.domain.owner.requests.OwnerFilterRequest
import com.techcubics.domain.owner.usecases.CreateOwnerUseCase
import com.techcubics.domain.owner.usecases.OwnerDetailsUseCase
import com.techcubics.domain.owner.usecases.OwnersByFilterUseCase
import com.techcubics.domain.owner.usecases.UpdateOwnerPasswordUseCase
import com.techcubics.domain.owner.usecases.UpdateOwnerStatusUseCase
import com.techcubics.domain.owner.usecases.UpdateOwnerUseCase
import com.techcubics.domain.storage.models.Owner
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.consumeAsFlow
import kotlinx.coroutines.launch


class OwnersViewModel(
    private val getOwnersByFilterUseCase: OwnersByFilterUseCase,
    private val createOwnerUseCase: CreateOwnerUseCase,
    private val updateOwnerPasswordUseCase: UpdateOwnerPasswordUseCase,
    private val updateOwnerStatusUseCase: UpdateOwnerStatusUseCase,
    private val updateOwnerUseCase: UpdateOwnerUseCase,
    private val ownerDetailsUseCase: OwnerDetailsUseCase
) : ViewModel() {


    private val _viewState = MutableStateFlow<OwnersViewState?>(OwnersViewState.Idle)
    val viewState: MutableStateFlow<OwnersViewState?> get() = _viewState

    val ownersIntent = Channel<OwnersIntent>(Channel.UNLIMITED)
    var popupMenuItemLiveData: MutableLiveData<String?> = MutableLiveData()

    private val _hasMorePagesState = MutableStateFlow(false)
    val hasMorePagesState: StateFlow<Boolean> get() = _hasMorePagesState

    private val _pageState = MutableStateFlow(1)
    val pageState: StateFlow<Int> get() = _pageState

    private val _filterStates = MutableStateFlow<OwnerFilterRequest?>(null)
    val filterStates: StateFlow<OwnerFilterRequest?> get() = _filterStates

    private var owners = mutableListOf<Owner>()

    init {
        handleIntent()
    }


    private fun handleIntent() {
        viewModelScope.launch {
            ownersIntent.consumeAsFlow().collect {
                when (it) {
                    is OwnersIntent.OwnersFilter -> getOwners(it.request,it.page)
                    is OwnersIntent.ChangeOwnerStatus -> updateOwnerStatus(it.id)
                    else -> {}
                }
            }
        }
    }


    private fun updateOwnerStatus(id: String) {
        _viewState.value = OwnersViewState.Loading
        viewModelScope.launch {
            val result = updateOwnerStatusUseCase.invoke(id)
            if (result?.message?.contains(Constants.SERVER_ERROR) == true) {
                OwnersViewState.ServerError(result.message!!)

            }
            else if(result?.message?.contains(Constants.Unauthenticated) == true) {
                OwnersViewState.Error(result.message!!)
            }
            else if (result?.status == true) {
                _viewState.value = OwnersViewState.OwnerStatus(result.message!!)

            }else{
                OwnersViewState.Error(result?.message ?: "error")

            }
        }
    }

    private fun getOwners(filterRequest: OwnerFilterRequest,page: Int = 1) {
        _filterStates.value = filterRequest
        if (page == 1) {
            _viewState.value = OwnersViewState.Loading
            _pageState.value = page
            _hasMorePagesState.value = false
            owners.clear()
        }
        if (hasMorePagesState.value) {
            _viewState.value = OwnersViewState.Pagination
        }
        viewModelScope.launch {
            val result = getOwnersByFilterUseCase.invoke(page,filterRequest)
            if (result?.message?.contains(Constants.SERVER_ERROR) == true) {
                OwnersViewState.ServerError(result.message!!)
            }
            else if(result?.message?.contains(Constants.Unauthenticated) == true) {
                _viewState.value = OwnersViewState.Error(result.message!!)
            }
            else if (result?.data != null) {
                _hasMorePagesState.value = result.pagingator?.hasMorePages ?: false
                if (result.pagingator?.hasMorePages == true) {
                    _pageState.value =
                        result.pagingator?.currentPage?.plus(1) ?: pageState.value
                }
                _viewState.value = appendOwners(result.data!!)

            } else {
                if(result?.status != true){
                    _viewState.value = OwnersViewState.Error(result?.message!!)

                }

            }
        }
    }

    private fun appendOwners(data: MutableList<Owner>): OwnersViewState.OwnersByFilter {
        if (!owners.containsAll(data)) {
            owners.addAll(data)
        }
        return OwnersViewState.OwnersByFilter(owners)
    }


}
package com.techcubics.albarkahyperdashboard.features.more.adapters

import android.app.Dialog
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyperdashboard.MainActivity
import com.techcubics.albarkahyperdashboard.databinding.ItemLanguageBinding
import com.techcubics.data.auth.local.SharedPreferencesManager
import org.koin.java.KoinJavaComponent
import java.util.*


class LanguageAdapter(
    private val context: Context,
    private val listOfLanguages: List<String>,
    private val popupMenuItemLiveData: MutableLiveData<String>,
    private val dialog: Dialog
) : RecyclerView.Adapter<LanguageHolderItem>() {

    private val sharedPreferencesManager: SharedPreferencesManager by KoinJavaComponent.inject(
        SharedPreferencesManager::class.java
    )
    val maincontext = context as MainActivity

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LanguageHolderItem {

        val itemBinding =
            ItemLanguageBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return LanguageHolderItem(itemBinding)
    }


    override fun onBindViewHolder(holder: LanguageHolderItem, position: Int) {


        when (listOfLanguages.get(position)) {
            "ar" -> {
                holder.itemName.text = context.getString(com.techcubics.resources.R.string.arabic)
                holder.image.setImageResource(com.hbb20.R.drawable.flag_egypt)
            }
            "en" -> {
                holder.itemName.text = context.getString(com.techcubics.resources.R.string.english)
                holder.image.setImageResource(com.hbb20.R.drawable.flag_united_kingdom)
            }
        }
        holder.itemView.setOnClickListener {
            popupMenuItemLiveData.value = listOfLanguages.get(position)
            dialog.dismiss()
            chooseLanguage(listOfLanguages.get(position))
        }
    }


    override fun getItemCount(): Int {
        return listOfLanguages.size
    }


    private fun chooseLanguage(newValue: String) {
        when (newValue) {
            "ar" -> {
                if (!sharedPreferencesManager.getLanguage().equals("ar")) {
                    maincontext.updateLocale(Locale("ar"))
                }
            }
            "en" -> {
                if (!sharedPreferencesManager.getLanguage().equals("en")) {
                    maincontext.updateLocale(Locale("en"))

                }
            }
        }

    }
}

class LanguageHolderItem(itemView: ItemLanguageBinding) : RecyclerView.ViewHolder(itemView.root) {
    val itemName = itemView.languageName
    val image = itemView.languageImage
}

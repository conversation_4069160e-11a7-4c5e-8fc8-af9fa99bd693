package com.techcubics.domain.orders.usecases

import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.orders.models.Order
import com.techcubics.domain.orders.repositories.BillsRepo
import com.techcubics.domain.orders.requests.OrdersFilterRequest
import com.techcubics.domain.orders.requests.OrdersFilterRequestBody

class OrdersByFilterUseCase(private val repo: BillsRepo){
    suspend operator fun invoke(page: Int,filter: OrdersFilterRequest) : BaseResponse<MutableList<Order>>? {
        val request = OrdersFilterRequestBody(
            status = filter.status,
            sort = filter.sort,
            search = filter.search,
            governorate = filter.governorate,
            regionList = filter.regionList,
            paginator = filter.paginator,
            method = filter.method
        )
        return repo.getOrderByFilter(page,request)
    }
}
class OrderDetailsUseCase(private val repo: BillsRepo){
    suspend operator fun invoke(id:String) = repo.getOrderDetails(id)
}
class OrderStatusUseCase(private val repo: BillsRepo){
    suspend operator fun invoke() = repo.getOrdersStatus()
}

class UpdateOrderStatusUseCase(private val repo: BillsRepo){
    suspend operator fun invoke(id : String) = repo.updateOrderStatus(id)
}

class CancelOrderUseCase(private val repo: BillsRepo){
    suspend operator fun invoke(id: String,reasonMessage : String) = repo.cancelOrder(id,reasonMessage)
}

class ReturnOrderUseCase(private val repo: BillsRepo){
    suspend operator fun invoke(id: String,reasonMessage : String) = repo.returnOrder(id,reasonMessage)
}

class GetGovernorates(private val repo: BillsRepo){
    suspend operator fun invoke(id: String) = repo.getGovernorate(id)
}

class GetRegions(private val repo: BillsRepo){
    suspend operator fun invoke(governorateId : String, countryId: String) = repo.getRegionsByGovernorate(governorateId, countryId)

}
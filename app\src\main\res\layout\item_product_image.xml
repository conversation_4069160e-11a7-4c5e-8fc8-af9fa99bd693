<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="?selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <ImageView
        android:layout_margin="8dp"
        android:id="@+id/product_img"
        android:layout_width="@dimen/icons_size_height_weight_1"
        android:layout_height="@dimen/icons_size_height_weight_1"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:contentDescription="@string/img_cnt_desc"
        tools:srcCompat="@tools:sample/avatars" />
    <ImageView
        android:id="@+id/delete_img"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/ic_close"
        android:background="@drawable/circle_gray"
        android:elevation="5dp"
        android:padding="8dp"
        android:contentDescription="@string/img_cnt_desc"
        app:layout_constraintStart_toStartOf="@id/product_img"
        app:layout_constraintTop_toTopOf="@id/product_img"
        android:layout_marginTop="-8dp"
        android:layout_marginStart="-8dp"
        app:tint="@color/black" />
</androidx.constraintlayout.widget.ConstraintLayout>
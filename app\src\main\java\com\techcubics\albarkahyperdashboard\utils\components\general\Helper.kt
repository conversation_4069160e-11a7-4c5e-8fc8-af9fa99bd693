package com.techcubics.albarkahyperdashboard.utils.components.general

import android.app.Activity
import android.app.Dialog
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.ContextWrapper
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.Uri
import android.os.Build
import android.view.*
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.content.res.AppCompatResources.getDrawable
import com.airbnb.lottie.LottieAnimationView
import com.bumptech.glide.Glide
import com.tapadoo.alerter.Alerter
import com.techcubics.albarkahyperdashboard.R
import java.io.*
import java.util.*
import java.util.concurrent.TimeUnit

object Helper {

    private  val TAG = "Helper"
    private lateinit var dialog: Dialog

    fun loadImage(context: Context, path: String, photo: ImageView) {
        Glide.with(context).load(path)
            .placeholder(com.techcubics.resources.R.drawable.portrait_placeholder) // placeholder
            .error(com.techcubics.resources.R.drawable.portrait_placeholder) // image broken
            .fallback(com.techcubics.resources.R.drawable.portrait_placeholder) // no image
            .into(photo)
    }

    fun setMargin(
        view: View,
        left: Int,
        top: Int,
        right: Int,
        bottom: Int
    ): ViewGroup.MarginLayoutParams {

        val param = view.layoutParams as ViewGroup.MarginLayoutParams
        param.setMargins(left, top, right, bottom)

        return param

    }

    fun formatToDigitalClock(miliSeconds: Long): String {
        val hours = TimeUnit.MILLISECONDS.toHours(miliSeconds).toInt() % 24
        val minutes = TimeUnit.MILLISECONDS.toMinutes(miliSeconds).toInt() % 60
        val seconds = TimeUnit.MILLISECONDS.toSeconds(miliSeconds).toInt() % 60
        return when {
            hours > 0 -> String.format(Locale.US, "%d:%02d:%02d", hours, minutes, seconds)
            minutes > 0 -> String.format(Locale.US, "%02d:%02d", minutes, seconds)
            seconds > 0 -> String.format(Locale.US, "00:%02d", seconds)
            else -> {
                "00:00:00"
            }
        }
    }

    fun showDialog(context: Context, message: String) {
        Toast.makeText(context, message, Toast.LENGTH_LONG).show()
    }

    fun customView(v: View, backgroundColor: Int, borderColor: Int, borderWidth: Int) {
        val shape = GradientDrawable()
        shape.shape = GradientDrawable.OVAL
        //shape.cornerRadii = floatArrayOf(8f, 8f, 8f, 8f, 0f, 0f, 0f, 0f)
        shape.setColor(backgroundColor)
        shape.setStroke(borderWidth, borderColor)
        v.background = shape
    }

    fun ShowLoadingDialog(context: Context, message: String?) {

        dialog = Dialog(context)
        dialog.setContentView(R.layout.dialog_message)

        val btnClose = dialog.findViewById<View>(R.id.btnClose) as ImageButton
        val img = dialog.findViewById<View>(R.id.img) as LottieAnimationView
        val tvMessage = dialog.findViewById<View>(R.id.tvMessage) as TextView
        val btnUpdate = dialog.findViewById<View>(R.id.btnUpdate) as TextView
        //
        btnClose.visibility = View.GONE
        btnUpdate.visibility=View.GONE
        img.setAnimation(com.techcubics.resources.R.raw.lottie_pager_loading)
        //
        tvMessage.text = message
        //
        dialog.setCancelable(false)
        dialog.show()
    }

    fun showErrorDialog(context: Context, message: String) {

//        Alerter.create(context.getActivity()!!)
//            .setTitle(context.getString(com.techcubics.resources.R.string.error))
//            .setText(message)
//            .setBackgroundDrawable(context.resources.getDrawable(com.techcubics.resources.R.color.indian_red))
//            .show()
        Alerter.create(context.getActivity()!!, R.layout.dialog_error)
            .setBackgroundColorRes(com.techcubics.resources.R.color.transparent)
            .also { alerter ->
                val messageTv = alerter.getLayoutContainer()?.findViewById<TextView>(R.id.message)
                messageTv?.text = message
            }
            .show()
    }
    fun showSuccessDialog(context: Context, message: String) {

        getDrawable(context,com.techcubics.resources.R.color.color_59)?.let {
            Alerter.create(context.getActivity()!!)
                .setTitle(context.getString(com.techcubics.resources.R.string.success))
                .setText(message)
                .setBackgroundDrawable(it)
                .show()
        }
    }

    fun ShowUpdateDialog(context: Context, message: String?) {

        dialog = Dialog(context)
        dialog.setContentView(R.layout.dialog_message)
        dialog.window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )

        val btnClose = dialog.findViewById<View>(R.id.btnClose) as ImageButton
        val img = dialog.findViewById<View>(R.id.img) as LottieAnimationView
        val tvMessage = dialog.findViewById<View>(R.id.tvMessage) as TextView
        val btnUpdate = dialog.findViewById<View>(R.id.btnUpdate) as TextView
        //
        img.setAnimation(com.techcubics.resources.R.raw.lottie_update)
        //
        tvMessage.text = message
        //
        btnUpdate.setOnClickListener {

            var packageName="com.techcubics.smartyclinicapp"
            try {
                context.startActivity(Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=$packageName")))
            } catch (e: ActivityNotFoundException) {
                context.startActivity(Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=$packageName")))
            }

        }
        //
        btnClose.visibility=View.GONE
        dialog.setCancelable(false)
        dialog.show()
    }
    fun closeDialog() {
        dialog.dismiss()
    }

    fun ShowSuccessDialog(context: Context, message: String?) {

        dialog = Dialog(context)
        dialog.setContentView(R.layout.dialog_message)
        dialog.window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )

        val btnClose = dialog.findViewById<View>(R.id.btnClose) as ImageButton
        val img = dialog.findViewById<View>(R.id.img) as LottieAnimationView
        val tvMessage = dialog.findViewById<View>(R.id.tvMessage) as TextView
        val btnUpdate = dialog.findViewById<View>(R.id.btnUpdate) as TextView

        btnUpdate.visibility=View.GONE
        //
        img.setAnimation(com.techcubics.resources.R.raw.lottie_success)
        //
        tvMessage.text = message
        //
        btnClose.setOnClickListener { dialog.dismiss() }
        dialog.setCancelable(true)
        dialog.show()
    }

    fun shareLink(context: Context, link: String?) {
        val sendIntent = Intent()
        sendIntent.action = Intent.ACTION_SEND
        sendIntent.putExtra(Intent.EXTRA_TEXT, link)
        sendIntent.putExtra(
            Intent.EXTRA_SUBJECT,
            context.getString(com.techcubics.resources.R.string.app_name)
        )
        sendIntent.type = "text/plain"
        val shareIntent = Intent.createChooser(sendIntent, null)
        context.startActivity(shareIntent)
    }


    fun full(context: Context,hideNavigation:Boolean?=true) {
        val activity = context as Activity
        if (Build.VERSION.SDK_INT >= 19 && Build.VERSION.SDK_INT < 21) {
            if(hideNavigation==true){
                setWindowFlag(activity, WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS or
                        WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION, true)
            }else{
                setWindowFlag(activity, WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS ,true)

            }

        }
        if (Build.VERSION.SDK_INT >= 19) {

            if(hideNavigation==true){
                activity.window.decorView.systemUiVisibility =
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            }else{
                activity.window.decorView.systemUiVisibility =
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN




            }

        }
        if (Build.VERSION.SDK_INT >= 21) {

            setWindowFlag(activity, WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS, false)
            activity.window.statusBarColor = Color.TRANSPARENT
            if(hideNavigation==true){
                activity.window.navigationBarColor=Color.TRANSPARENT
            }else{

                activity.window.navigationBarColor = activity.resources.getColor(com.techcubics.resources.R.color.ghost_white)
            }

        }
    }

    fun exitFullScreen(context: Context) {
       val activity = context as Activity
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            activity.window.decorView.systemUiVisibility =
                View.SYSTEM_UI_FLAG_VISIBLE or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR or View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR
        }else{
            activity.window.decorView.systemUiVisibility =
                View.SYSTEM_UI_FLAG_VISIBLE
        }
        activity.window.clearFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN or
                    WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS or
                    WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION
        )
        activity.window.statusBarColor = activity.resources.getColor(com.techcubics.resources.R.color.white)
        activity.window.navigationBarColor = activity.resources.getColor(com.techcubics.resources.R.color.ghost_white)
    }

    fun setWindowFlag(activity: Activity, bits: Int, on: Boolean) {
        val win = activity.window
        val winParams = win.attributes
        if (on) {
            winParams.flags = winParams.flags or bits
        } else {
            winParams.flags = winParams.flags and bits.inv()
        }
        win.attributes = winParams
    }

    fun openMap(context: Context,latitude:String,longitude:String){
        val uri =
            "http://maps.google.com/maps?q=loc:$latitude,$longitude"
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(uri))
        intent.setPackage("com.google.android.apps.maps")
        context.startActivity(intent)

    }

    fun  loadingAnimationVisibility(isVisible:Int, loadingAnimation:View){
        loadingAnimation.visibility = isVisible

    }

    fun Context.getActivity(): Activity? {
        return when (this) {
            is Activity -> this
            is ContextWrapper -> this.baseContext.getActivity()
            is ContextThemeWrapper -> this.baseContext.getActivity()
            else -> null
        }
    }

     fun bitmapToFile(
        context: Context,
        bitmap: Bitmap?,
        fileNameToSave: String
    ): File? { // File name like "image.png"
        //create a file to write bitmap data
        val file: File?

        file = File(context.cacheDir, fileNameToSave)
        file.createNewFile()

        val bos = ByteArrayOutputStream()
        bitmap?.compress(Bitmap.CompressFormat.JPEG, 50 /*ignored for PNG*/, bos)

        val bitmapdata = bos.toByteArray()

        var fos: FileOutputStream? = null
        try {
            fos = FileOutputStream(file)
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
        }
        try {
            fos!!.write(bitmapdata)
            fos.flush()
            fos.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return file
    }
    fun hideSystemUIWithNavigation(window: Window) {
        window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                or View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR
                or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                or View.SYSTEM_UI_FLAG_FULLSCREEN
                )

    }
    fun hideSystemUI(window: Window) {
        window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_FULLSCREEN)

    }
    fun showSystemUIWithNavigation(window: Window) {
        window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                or View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR
                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN)
    }
    fun showSystemUI(window: Window) {
        window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN)
    }

    fun openApp(context: Context,packageName:String) {

        try {

            val launchIntent: Intent? = context.packageManager.getLaunchIntentForPackage(packageName)
            context.startActivity(launchIntent)
        }catch (ex:Exception){

            var intent = context!!.packageManager.getLaunchIntentForPackage(packageName)

            if (intent == null) {
                if (intent == null) {
                    intent = try {
                        Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=${packageName}"))
                    } catch (e: Exception) {
                        Intent(
                            Intent.ACTION_VIEW,
                            Uri.parse("https://play.google.com/store/apps/details?id=${packageName}")
                        )
                    }
                }
                context.startActivity(intent)
            }
        }



    }

    fun openUrl(context: Context,url:String){

        val intent = Intent(Intent.ACTION_VIEW).setData(Uri.parse(url))
        context.startActivity(intent)
    }

    fun isConnected(context: Context): Boolean {
        var isConnected = false
        val connMgr =
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val allNetworks: Array<Network> = connMgr.allNetworks
        for (network in allNetworks) {
            val networkCapabilities: NetworkCapabilities? =
                connMgr.getNetworkCapabilities(network)
            val isInternet =
                networkCapabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            if (networkCapabilities != null) {
                if (networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
                    || networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
                    || networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)
                ) {
                    if (isInternet == true) {
                        isConnected = true
                    }
                }
            }
        }
        return isConnected
    }


}
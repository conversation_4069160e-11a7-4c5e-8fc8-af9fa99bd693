package com.techcubics.albarkahyperdashboard.features.chats.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyperdashboard.databinding.ItemChatBinding
import com.techcubics.albarkahyperdashboard.features.chats.adapters.holders.ChatHolderItem
import com.techcubics.domain.chats.models.Chat
import com.techcubics.domain.storage.models.Discount

class ChatsAdapter : RecyclerView.Adapter<ChatHolderItem>() {
    private var chats: MutableList<Chat>  = mutableListOf()
    fun updateItems(_chat: List<Chat>) {
        chats.clear()
        chats.addAll(_chat)
        notifyItemRangeChanged(0, itemCount)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChatHolderItem {
        val itemBinding =
            ItemChatBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ChatHolderItem(itemBinding, parent.context)

    }

    override fun onBindViewHolder(holder: ChatHolderItem, position: Int) {
        holder.setData(chats[position])
    }


    override fun getItemCount(): Int {
        return chats.size
    }
    fun updateChats(chats: List<Chat>?) {
        val diffResult = DiffUtil.calculateDiff(ChatsDiffUtilCallback(this.chats, chats))
        this.chats.clear()
        chats?.let { this.chats.addAll(it) }
        diffResult.dispatchUpdatesTo(this)
    }
}



class ChatsDiffUtilCallback(
    private val oldList: List<Chat>?,
    private val newList: List<Chat>?
) : DiffUtil.Callback() {
    override fun getOldListSize(): Int {
        return oldList?.size?:0
    }

    override fun getNewListSize(): Int {
        return newList?.size?:0
    }

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return (oldList?.get(oldItemPosition)?.time == newList?.get(newItemPosition)?.time)&&
                (oldList?.get(oldItemPosition)?.date == newList?.get(newItemPosition)?.date)&&
                (oldList?.get(oldItemPosition)?.direction == newList?.get(newItemPosition)?.direction)
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return when (oldList?.size) {
            newList?.size -> true
            else -> false
        }
    }

    override fun getChangePayload(oldItemPosition: Int, newItemPosition: Int): Any {
        return when (oldItemPosition) {
            newItemPosition -> true
            else -> false
        }
    }

}
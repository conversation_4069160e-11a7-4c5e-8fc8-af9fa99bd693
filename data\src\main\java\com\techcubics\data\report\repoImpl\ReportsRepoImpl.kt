package com.techcubics.data.report.repoImpl

import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.common.RepositoryResponse
import com.techcubics.data.report.remote.EndPoints
import com.techcubics.data.common.RetrofitBuilder
import com.techcubics.domain.owner.requests.UpdateOwnerPasswordRequest
import com.techcubics.domain.reports.repositories.ReportsRepo
import com.techcubics.domain.reports.request.ReportRequest
import com.techcubics.domain.reports.response.ReportResponse

class ReportsRepoImpl(
    private val retrofitBuilder: RetrofitBuilder,
    private val sharedPreferencesManager: SharedPreferencesManager
) : ReportsRepo,
    RepositoryResponse {

    private val api = retrofitBuilder.start()?.create(EndPoints::class.java)

    override suspend fun getReports(
        request: ReportRequest
    ): BaseResponse<ReportResponse>? {
        return try {
            val result = api
                ?.getReports(userType = sharedPreferencesManager.getUserType(), request)
            baseResponse(result)

        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }


}
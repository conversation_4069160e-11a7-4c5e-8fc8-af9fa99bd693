<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.AppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:elevation="1dp"
    android:backgroundTint="@color/app_color"

    >

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
       >

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/offer_price"
            style="@style/LargeBoldWhiteText"
            android:layout_gravity="center_vertical|center"
            android:maxLines="2"
            android:ellipsize="end" />

        <ImageView
            android:id="@+id/refresh"
            android:layout_width="wrap_content"
            android:layout_height="?actionBarSize"
            android:background="?selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:visibility="gone"
            android:paddingHorizontal="10dp"
            android:paddingVertical="12dp"
            android:src="@drawable/ic_refresh_24"
            app:tint="@color/white" />

        <ImageView
            android:id="@+id/search"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="?actionBarSize"
            android:background="?selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:paddingHorizontal="10dp"
            android:paddingVertical="5dp"
            android:src="@drawable/ic_search_24"
            app:tint="@color/white" />

    </androidx.appcompat.widget.Toolbar>


</com.google.android.material.appbar.AppBarLayout>


<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:theme="@style/Theme.TextInputLayout"
    android:layout_height="match_parent"
    android:background="@color/color_gray_3"
    tools:context=".features.reports.fragments.ReportsFragment">

    <include
        android:id="@+id/toolbar_title"
        layout="@layout/toolbar_home"
        android:elevation="16dp"
        app:layout_constraintTop_toTopOf="parent" />
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar_title"
        android:layout_height="wrap_content">
        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:layout_margin="15dp"
            android:layout_height="wrap_content">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/owner_cont"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_margin="5dp"
                    android:layout_height="wrap_content"
                    android:hint="@string/owner_name"
                    app:hintTextAppearance="@style/Medium1RegularDarkText"
                    app:boxCornerRadiusBottomEnd="10dp"
                    app:boxCornerRadiusBottomStart="10dp"
                    app:boxCornerRadiusTopEnd="10dp"
                    app:boxCornerRadiusTopStart="10dp"
                    app:boxBackgroundColor="@color/white"
                    app:layout_constraintTop_toTopOf="parent">

                    <AutoCompleteTextView
                        android:id="@+id/owner"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none"
                        android:textAppearance="@style/Medium1RegularDarkText"
                        tools:ignore="LabelFor" />

                </com.google.android.material.textfield.TextInputLayout>
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/shop_cont"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_margin="5dp"
                    android:layout_height="wrap_content"
                    android:hint="@string/shop_name"
                    app:boxCornerRadiusBottomEnd="10dp"
                    app:boxCornerRadiusBottomStart="10dp"
                    app:boxCornerRadiusTopEnd="10dp"
                    app:boxCornerRadiusTopStart="10dp"
                    app:boxBackgroundColor="@color/white"
                    app:layout_constraintTop_toTopOf="parent">

                    <AutoCompleteTextView
                        android:id="@+id/shop"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none"
                        android:textAppearance="@style/Medium1RegularDarkText"
                        tools:ignore="LabelFor" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/from_cont"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_margin="5dp"
                    app:endIconDrawable="@drawable/ic_date"
                    app:endIconTint="@color/app_color"
                    android:layout_height="wrap_content"
                    android:hint="@string/from"
                    app:boxCornerRadiusBottomEnd="10dp"
                    app:boxCornerRadiusBottomStart="10dp"
                    app:boxCornerRadiusTopEnd="10dp"
                    app:boxCornerRadiusTopStart="10dp"
                    app:boxBackgroundColor="@color/white"
                    app:layout_constraintTop_toTopOf="parent">

                    <AutoCompleteTextView
                        android:id="@+id/from"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none"
                        android:textAppearance="@style/Medium1RegularDarkText"
                        tools:ignore="LabelFor" />

                </com.google.android.material.textfield.TextInputLayout>
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/to_cont"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_margin="5dp"
                    android:layout_height="wrap_content"
                    android:hint="@string/to"
                    app:boxCornerRadiusBottomEnd="10dp"
                    app:boxCornerRadiusBottomStart="10dp"
                    app:boxCornerRadiusTopEnd="10dp"
                    app:boxCornerRadiusTopStart="10dp"
                    app:boxBackgroundColor="@color/white"
                    app:endIconDrawable="@drawable/ic_date"
                    app:endIconTint="@color/app_color"
                    android:enabled="false"
                    android:clickable="false"
                    app:layout_constraintTop_toTopOf="parent">

                    <AutoCompleteTextView
                        android:id="@+id/to"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none"
                        android:textAppearance="@style/Medium1RegularDarkText"
                        tools:ignore="LabelFor" />

                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
            <androidx.recyclerview.widget.RecyclerView
                android:layout_marginBottom="80dp"
                android:id="@+id/reportsRV"
                android:layout_marginTop="15dp"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:itemCount="3"
                tools:listitem="@layout/item_report"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <include
        android:id="@+id/action_loading_animation"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/layout"
        layout="@layout/include_placeholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"

        />
</androidx.constraintlayout.widget.ConstraintLayout>
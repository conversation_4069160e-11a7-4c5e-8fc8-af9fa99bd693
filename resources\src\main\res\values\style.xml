<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="ExtraSmallBoldDarkText">
        <item name="android:textColor">@color/color_gray_35</item>
        <item name="android:textSize">@dimen/fontsize_8</item>
        <item name="android:fontFamily">?fontBold</item>
    </style>
    <style
        name="ExtraSmallBoldAppColorText" parent="ExtraSmallBoldDarkText">
        <item name="android:textColor">@color/app_color</item>
    </style>
    <style
        name="ExtraSmallBoldWhiteText" parent="ExtraSmallBoldDarkText">
        <item name="android:textColor">@color/white</item>
    </style>
    <style
        name="SmallRegularWhiteText">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/fontsize_7</item>
        <item name="android:fontFamily">?fontRegular</item>
    </style>
    <style
        name="SmallRegularAppColorText" parent="SmallRegularWhiteText">
        <item name="android:textColor">@color/app_color</item>
    </style>

    <style name="SmallBoldAppColorText" parent="SmallRegularWhiteText">
        <item name="android:fontFamily">?fontBold</item>
        <item name="android:textColor">@color/app_color</item>
    </style>

    <style name="SmallRegularDarkText" parent="SmallRegularWhiteText">
        <item name="android:textColor">@color/dark_black</item>
        <item name="android:textColorHint">@color/light_txt_color1</item>
    </style>

    <style name="SmallRegularLightText" parent="SmallRegularWhiteText">
        <item name="android:textColor">@color/light_txt_color2</item>
    </style>

    <style name="SmallMediumAppColor2Text" parent="SmallMediumDarkText">
        <item name="android:textColor">@color/app_color2</item>
    </style>

    <style name="SmallMediumDarkText">
        <item name="android:textColor">@color/color_gray_35</item>
        <item name="android:textSize">@dimen/fontsize_7</item>
        <item name="android:fontFamily">?fontMedium</item>
    </style>

    <style name="SmallMediumWhiteText" parent="SmallMediumDarkText">
        <item name="android:textColor">@color/white</item>

    </style>

    <style name="Medium1MediumAppColorText">
        <item name="android:textColor">@color/app_color</item>
        <item name="android:textSize">@dimen/fontsize_5</item>
        <item name="android:fontFamily">?fontMedium</item>
    </style>

    <style name="Medium1BoldWhiteText">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/fontsize_5</item>
        <item name="android:fontFamily">?fontBold</item>
    </style>

    <style name="Medium1RegularDarkText">
        <item name="android:textColor">@color/color_gray_35</item>
        <item name="android:textSize">@dimen/fontsize_5</item>
        <item name="android:textColorHint">@color/light_txt_color1</item>
        <item name="android:fontFamily">?fontRegular</item>
    </style>
    <style name="Medium1RegularGreenText" parent="Medium1RegularDarkText">
        <item name="android:textColor">@color/color_59</item>
    </style>
    <style name="Medium1RegularLightText" parent="Medium1RegularDarkText">
        <item name="android:textColor">@color/light_txt_color2</item>
    </style>

    <style name="Medium1BoldDarkText" parent="Medium1RegularDarkText">
        <item name="android:fontFamily">?fontBold</item>
    </style>
    <style name="Medium2BoldGreenText" parent="Medium2BoldDarkText">
        <item name="android:textColor">@color/color_59</item>
    </style>

    <style name="LargeBoldDarkText" parent="ExtraSmallBoldDarkText">
        <item name="android:textSize">@dimen/fontsize_2</item>
    </style>

    <style name="LargeBoldAppColorText" parent="LargeBoldDarkText">
        <item name="android:textColor">@color/app_color</item>
    </style>

    <style name="LargeBoldWhiteText" parent="LargeBoldDarkText">
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="ExtraLargeMediumAppColorText" parent="Medium1MediumAppColorText">
        <item name="android:textSize">@dimen/fontsize_1</item>
    </style>

    <style name="ExtraLargeRegularAppColorText" parent="SmallRegularAppColorText">
        <item name="android:textSize">@dimen/fontsize_1</item>
    </style>

    <style
        name="ExtraLargeBoldDarkText" parent="ExtraSmallBoldDarkText">
        <item name="android:textSize">@dimen/fontsize_1</item>
    </style>
    <style
        name="ExtraLargeBoldWhiteText" parent="ExtraLargeBoldDarkText">
        <item name="android:textColor">@color/white</item>
    </style>

    <style
        name="CustomAlertDialogTheme" parent="Theme.MaterialComponents.Light.Dialog.Alert">
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <style
        name="SmallRegularWhiteButton" parent="@android:style/TextAppearance.Widget.Button">
        <item name="android:textSize">@dimen/fontsize_7</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:fontFamily">?fontRegular</item>
    </style>

    <style
        name="Medium2RegularDarkSearch">
        <item name="android:textColor">@color/color_gray_36</item>
        <item name="android:textColorHint">@color/light_txt_color1</item>
        <item name="android:textSize">@dimen/fontsize_6</item>
        <item name="android:fontFamily">?fontRegular</item>
    </style>
    <style
        name="Medium2BoldDarkText" parent="Medium2RegularDarkSearch">
        <item name="android:fontFamily">?fontBold</item>
    </style>
    <style
        name="Medium2MediumDarkText" parent="Medium2RegularDarkSearch">
        <item name="android:fontFamily">?fontMedium</item>
    </style>

    <style name="Medium2RegularGray">
        <item name="android:textColor">@color/color_gray_28</item>
        <item name="android:textSize">@dimen/fontsize_6</item>
        <item name="android:fontFamily">?fontRegular</item>
        <item name="android:textStyle">bold</item>
    </style>
    --------------------tabs----------------------------
    <style name="tab_round">
        <item name="android:background">@android:color/transparent</item>
        <item name="tabStyle">@style/tab_theme_5</item>
    </style>

    <style name="tab_theme_5"
        parent="Widget.MaterialComponents.TabLayout">
        <item name="materialThemeOverlay">@style/tab_theme_1</item>
        <item name="tabTextAppearance">@style/tab_theme_2</item>
    </style>

    <style
        name="tab_theme_1" parent="">
        <item name="colorPrimary">@color/app_color</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorOnSurface">@color/color_gray_35</item>
    </style>

    <style name="tab_theme_2"
        parent="TextAppearance.Design.Tab">
        <item name="textAllCaps">false</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textColor">@color/color_gray_18</item>
        <item name="android:fontFamily">?fontRegular</item>
        <item name="android:textSize">@dimen/fontsize_6</item>
    </style>
    ----------------checkbox------------------------------
    <style name="Medium1BoldAppColorCheckBox">
        <item name="android:textColor">?checkbox_text_color</item>
        <item name="android:textSize">?checkbox_font_size</item>
        <item name="android:fontFamily">?checkbox_font_family</item>
        <item name="android:buttonTint">@color/app_color</item>
    </style>
    ---------------------- auth ------------------------------
    <style name="ExtraLargeBoldDark2Text">
        <item name="android:textColor">?header_auth_text_color</item>
        <item name="android:textSize">?header_auth_font_size</item>
        <item name="android:fontFamily">?header_auth_font_family</item>
        <item name="android:background">?header_auth_background_color</item>
        <item name="lineHeight">?header_auth_line_height</item>
    </style>


    <style
        name="SmallRegularAppColorText2" parent="SmallRegularAppColorText">
        <item name="lineHeight">@dimen/line_height_4</item>
    </style>

    <style
        name="Small2LightColorLightText">
        <item name="android:textSize">?auth_edittext_label_font_size</item>
        <item name="android:textColor">?auth_edittext_label_text_color</item>
        <item name="android:fontFamily">?auth_edittext_label_font_family</item>
    </style>

    <style name="SmallLightColorMediumText">
        <item name="android:fontFamily">?auth_label_font_family</item>
        <item name="android:textSize">?auth_label_text_size</item>
        <item name="android:textColor">?auth_label_text_color</item>
        <item name="lineHeight">@dimen/line_height_4</item>
    </style>

    <style name="MySpinnerDatePickerStyle" parent="android:Theme.Material.Light.Dialog.NoActionBar">
        <item name="android:buttonBarNegativeButtonStyle">@style/button_theme</item>
        <item name="android:buttonBarPositiveButtonStyle">@style/button_theme</item>
        <item name="android:datePickerStyle">@style/MySpinnerDatePicker</item>
        <item name="android:fontFamily">?fontRegular</item>
    </style>

    <style name="MySpinnerDatePicker" parent="android:Widget.Material.DatePicker">
        <item name="android:datePickerMode">spinner</item>
    </style>

    <style name="button_theme" parent="android:Widget.Material.Light.Button.Borderless">
        <item name="android:textColor">@color/app_color</item>
        <item name="android:fontFamily">?fontRegular</item>
    </style>
    <style name="dialog_animation">
        <item name="android:windowEnterAnimation">@anim/slide_in_bottom</item>
        <item name="android:windowExitAnimation">@anim/slide_out_bottom</item>

    </style>

    <style name="bottomsheet_style" parent="Theme.MaterialComponents.Light.BottomSheetDialog">
        <item name="behavior_draggable">false</item>
    </style>

    -----------------picker-------------------------------------

    <style name="MyDatePickerDialogTheme" parent="android:Theme.Material.Light.Dialog">
        <item name="android:datePickerStyle">@style/MyDatePickerStyle</item>
        <item name="android:colorAccent">@color/app_color</item>
        <item name="colorOnPrimary">@color/app_color</item>
    </style>

    <style name="MyDatePickerStyle" parent="@android:style/Widget.Material.Light.DatePicker">
        <item name="android:headerBackground">@color/app_color</item>
        <item name="android:datePickerMode">calendar</item>
        <item name="colorOnPrimary">@color/app_color</item>
    </style>
</resources>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".features.storage.fragments.tabs.product.ProductDetailsFragment"
    android:id="@+id/productdetails_layout">
    <include android:id="@+id/toolbar_fragment"
        layout="@layout/toolbar_fragment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"/>

    <include
        android:id="@+id/msg_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        layout="@layout/include_placeholder"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar_fragment" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar_fragment"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_height="0dp"
        android:visibility="gone">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="10dp">

            <include
                android:id="@+id/product_img"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintHeight_min="250dp"
                app:layout_constraintHeight_max="450dp"
                layout="@layout/include_images_view_pager"
                app:layout_constraintTop_toTopOf="parent"/>

            <include
                android:id="@+id/product_details_container"
                layout="@layout/include_product_details"
                android:layout_width="match_parent"
                app:layout_constraintTop_toBottomOf="@id/product_img"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_height="wrap_content" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <include
        android:id="@+id/loading"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
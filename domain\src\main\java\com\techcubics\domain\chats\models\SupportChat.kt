package com.techcubics.domain.chats.models

import com.google.gson.annotations.SerializedName

data class SupportChatResponse(

    @SerializedName("conversation" ) var conversation : SupportChatRoom?       = SupportChatRoom(),
    @SerializedName("messages"     ) var messages     : ArrayList<SupportChat> = arrayListOf()
)
data class SupportChat(
    @SerializedName("direction"    ) override var direction   : String?  = null,
    @SerializedName("read"         ) var read        : Any? = null,
    @SerializedName("message"      ) override var message     : String?  = null,
    @SerializedName("date"         ) override var date        : String?  = null,
    @SerializedName("time"         ) override var time        : String?  = null,
    @SerializedName("format_date"  ) override var formatDate  : String?  = null,
    @SerializedName("last_message" ) var lastMessage : String?  = null
):Chat

interface Chat{
    var direction   : String?
    var message     : String?
    var date        : String?
    var time        : String?
    var formatDate  : String?
}

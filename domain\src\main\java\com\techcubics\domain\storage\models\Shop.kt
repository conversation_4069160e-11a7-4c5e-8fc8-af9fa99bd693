package com.techcubics.domain.storage.models

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class Shop (

    @SerializedName("id"                 ) var id                : Int?     = null,
    @SerializedName("shop_id"            ) var shopId            : Int?     = null,
    @SerializedName("name"               ) var name              : String?  = null,
    @SerializedName("slug"               ) var slug              : String?  = null,
    @SerializedName("description"        ) var description       : String?  = null,
    @SerializedName("address"            ) var address           : String?  = null,
    @SerializedName("logo"               ) var logo              : String?  = null,
    @SerializedName("image"              ) var image             : String?  = null,
    @SerializedName("phone"              ) var phone             : String?  = null,
    @SerializedName("email"              ) var email             : String?  = null,
    @SerializedName("status"             ) var status            : Boolean? = null,
    @SerializedName("subscription_type"  ) var subscriptionType  : String?  = null,
    @SerializedName("subscription_value" ) var subscriptionValue : Double?  = null,
    @SerializedName("invoice_duration"   ) var invoiceDuration   : Int?     = null,
    @SerializedName("qr_image"           ) var qrImage           : String?  = null,
    @SerializedName("lat"                ) var lat               : Double?  = null,
    @SerializedName("lng"                ) var lng               : Double?  = null,
    @SerializedName("chat_enable"        ) var chatEnable        : Boolean? = null

): Parcelable
package com.techcubics.albarkahyperdashboard

import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.os.Bundle
import com.techcubics.albarkahyperdashboard.utils.di.networkModule
import com.techcubics.albarkahyperdashboard.utils.di.repositoryModule
import com.techcubics.albarkahyperdashboard.utils.di.sharedPreferenceModule
import com.techcubics.albarkahyperdashboard.utils.di.useCasesModule
import com.techcubics.albarkahyperdashboard.utils.di.viewModelModule
import com.zeugmasolutions.localehelper.LocaleHelper
import com.zeugmasolutions.localehelper.LocaleHelperApplicationDelegate
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.core.logger.Level


class ApplicationStartup : Application() {

    private val localeAppDelegate = LocaleHelperApplicationDelegate()

    override fun onCreate() {
        super.onCreate()
        startKoin(this)
        registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, p1: Bundle?) {
                activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
            }

            override fun onActivityStarted(p0: Activity) {

            }

            override fun onActivityResumed(p0: Activity) {
            }

            override fun onActivityPaused(p0: Activity) {
            }

            override fun onActivityStopped(p0: Activity) {
            }

            override fun onActivitySaveInstanceState(p0: Activity, p1: Bundle) {
            }

            override fun onActivityDestroyed(p0: Activity) {
            }

        })
    }

    private fun startKoin(app: Application) {
        org.koin.core.context.startKoin {
            androidLogger(Level.ERROR)
            androidContext(app)
            modules(
                useCasesModule,
                viewModelModule,
                repositoryModule,
                networkModule,
                sharedPreferenceModule
            )
        }
    }

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(localeAppDelegate.attachBaseContext(base))
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        localeAppDelegate.onConfigurationChanged(this)
    }

    override fun getApplicationContext(): Context =
    LocaleHelper.onAttach(super.getApplicationContext())


}
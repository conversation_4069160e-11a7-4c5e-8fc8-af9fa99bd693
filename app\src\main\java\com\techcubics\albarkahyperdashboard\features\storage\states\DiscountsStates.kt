package com.techcubics.albarkahyperdashboard.features.storage.states

import com.techcubics.albarkahyperdashboard.features.storage.intents.DiscountIntent
import com.techcubics.domain.storage.models.Owner
import com.techcubics.domain.storage.models.Discount
import com.techcubics.domain.storage.models.Product
import com.techcubics.domain.storage.models.Shop

sealed class DiscountsStates {
    object Idle : DiscountsStates()
    object Loading : DiscountsStates()
    object Pagination : DiscountsStates()
    data class ViewDiscountDetails(val discount: Discount?) : DiscountsStates()
    data class ViewOwners(val owners: ArrayList<Owner>?) : DiscountsStates()
    data class ViewShops(val shops: ArrayList<Shop>?) : DiscountsStates()
  data class ViewDiscounts(val discounts: MutableList<Discount>) : DiscountsStates()
    data class Error(val error: String) : DiscountsStates()
    data class StatusError(val error: String, val discountId: Int) : DiscountsStates()

    data class ServerError(val error: String) : DiscountsStates()
    object Success : DiscountsStates()
    data class ViewProducts(val products:ArrayList<Product>): DiscountsStates()
}
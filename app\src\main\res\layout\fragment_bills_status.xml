<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".features.bills.fragments.BillsStatusFragment">

    <include
        android:id="@+id/include_home_toolbar"
        layout="@layout/toolbar_home"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp" />

    <RelativeLayout
        app:layout_constraintTop_toBottomOf="@id/include_home_toolbar"
        android:layout_width="match_parent"
        android:id="@+id/tab_layout"
        android:layout_height="wrap_content">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:weightSum="5"
            android:layout_margin="8dp">
            <com.google.android.material.tabs.TabLayout
                android:id="@+id/viewpagertab"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                app:tabTextAppearance="@style/Medium2RegularGray"
                app:tabSelectedTextAppearance="@style/Medium2RegularGray"
                android:background="@null"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:tabIndicatorHeight="2dp"
                app:tabMode="scrollable"
                app:tabIndicatorColor="@color/app_color"
                app:tabRippleColor="@null"
                app:tabTextColor="@color/color_gray_14"
                app:tabSelectedTextColor="@color/color_blue_4" />

        </LinearLayout>
    </RelativeLayout>


    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewpager"
        app:layout_constraintTop_toBottomOf="@id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        />

</androidx.constraintlayout.widget.ConstraintLayout>
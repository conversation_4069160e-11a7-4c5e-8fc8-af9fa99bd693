package com.techcubics.albarkahyperdashboard.features.storage.adapters

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.TextView
import androidx.annotation.LayoutRes
import com.techcubics.domain.orders.models.Governorate
import com.techcubics.domain.orders.models.Region
import com.techcubics.domain.storage.models.Category
import com.techcubics.domain.storage.models.Owner
import com.techcubics.domain.storage.models.Product
import com.techcubics.domain.storage.models.Shop

class DropDownAdapter<T>(
    context: Context,
    @LayoutRes private val layoutResource: Int,
    private val items: List<T>
) : ArrayAdapter<T>(context, layoutResource, items) {

    override fun getItem(position: Int): T = items[position]

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        val view = createViewFromResource(convertView, parent, layoutResource)

        return bindData(getItem(position), view)
    }

    override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
        val view = createViewFromResource(
            convertView,
            parent,
            android.R.layout.simple_spinner_dropdown_item
        )
        return bindData(getItem(position), view)
    }

    private fun createViewFromResource(
        convertView: View?,
        parent: ViewGroup,
        layoutResource: Int
    ): TextView {
        val context = parent.context
        val view =
            convertView ?: LayoutInflater.from(context).inflate(layoutResource, parent, false)
        return view as TextView
    }

    private fun bindData(item: T, view: TextView): TextView {
        val name = when(item){
            is Owner->item.user?.name
            is Shop->item.name
            is Category->item.name
            is Product ->item.name
            is Governorate -> item.name
            is Region -> item.name
            else->""
        }
        view.text = name
        return view
    }
}

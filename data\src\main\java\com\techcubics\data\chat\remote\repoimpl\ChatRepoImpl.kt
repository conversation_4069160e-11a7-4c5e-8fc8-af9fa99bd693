package com.techcubics.data.chat.remote.repoimpl

import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.data.chat.remote.EndPoints
import com.techcubics.data.common.RetrofitBuilder
import com.techcubics.domain.chats.models.OrderChat
import com.techcubics.domain.chats.models.OrderChatRoom
import com.techcubics.domain.chats.models.SupportChat
import com.techcubics.domain.chats.models.SupportChatResponse
import com.techcubics.domain.chats.models.SupportChatRoom
import com.techcubics.domain.chats.repo.ChatRepo
import com.techcubics.domain.chats.request.SendMessageRequest
import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.common.RepositoryResponse

class ChatRepoImpl(private val retrofitBuilder: RetrofitBuilder, private val sharedPreferencesManager: SharedPreferencesManager) : Chat<PERSON><PERSON>o, RepositoryResponse {
    private val api = retrofitBuilder.start()?.create(EndPoints::class.java)
    override suspend fun getOrdersChatRooms(
        page: Int,
        ownerId: Int,
        shopId: Int
    ): BaseResponse<ArrayList<OrderChatRoom>>? {
        return try {
            val result = api?.getOrdersChatRooms(userType = sharedPreferencesManager.getUserType(),page,ownerId,shopId)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getOrderChats(
        id: Int,
        ownerId: Int,
        shopId: Int
    ): BaseResponse<ArrayList<OrderChat>>? {
        return try {
            val result = api?.getOrderChats(userType = sharedPreferencesManager.getUserType(),id,ownerId,shopId)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun sendOrderChatMessage(
        request: SendMessageRequest,
        ownerId: Int,
        shopId: Int
    ): BaseResponse<OrderChat>? {
        return try {
            val result = api?.sendOrderChatMessage(userType = sharedPreferencesManager.getUserType(),request,ownerId,shopId)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getSupportChatRooms(page: Int): BaseResponse<ArrayList<SupportChatRoom>>? {
        return try {
            val result = api?.getSupportChatRooms(userType = sharedPreferencesManager.getUserType(),page)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getSupportChats(id: Int): BaseResponse<SupportChatResponse>? {
        return try {
            val result = api?.getSupportChats(userType = sharedPreferencesManager.getUserType(),id)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun sendSupportChatMessage(request: SendMessageRequest): BaseResponse<SupportChat>? {
        return try {
            val result = api?.sendSupportChatMessage(userType = sharedPreferencesManager.getUserType(),request)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

}
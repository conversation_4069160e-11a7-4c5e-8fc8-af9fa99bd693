package com.techcubics.albarkahyperdashboard.features.storage.fragments.tabs.discount

import android.app.DatePickerDialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.doOnTextChanged
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.google.android.material.textfield.TextInputEditText
import com.techcubics.albarkahyperdashboard.R
import com.techcubics.albarkahyperdashboard.databinding.FragmentAddNewDiscountBinding
import com.techcubics.albarkahyperdashboard.features.storage.adapters.DropDownAdapter
import com.techcubics.albarkahyperdashboard.features.storage.intents.DiscountIntent
import com.techcubics.albarkahyperdashboard.features.storage.states.DiscountsStates
import com.techcubics.albarkahyperdashboard.features.storage.viewmodels.DiscountViewModel
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.listeners.NavigationBarVisibilityListener
import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.data.auth.utils.Constants
import com.techcubics.data.auth.utils.UserTypeEnum
import com.techcubics.domain.auth.models.User
import com.techcubics.domain.storage.enums.ProductAction
import com.techcubics.domain.storage.models.Discount
import com.techcubics.domain.storage.models.Owner
import com.techcubics.domain.storage.models.Product
import com.techcubics.domain.storage.models.Shop
import com.techcubics.domain.storage.requests.AddNewDiscount
import com.techcubics.resources.R.string
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale


class AddNewDiscountFragment : Fragment() {
    private var shopId: Int? = null
    private var ownerId: Int? = null
    private var productId: Int? = null
    private var productPrice: Float? = null
    private var _binding: FragmentAddNewDiscountBinding? = null
    private val binding get() = _binding!!
    private var ownerDropDownAdapter: DropDownAdapter<Owner>? = null
    private var shopDropDownAdapter: DropDownAdapter<Shop>? = null
    private var productDropDownAdapter: DropDownAdapter<Product>? = null
    private val viewModel by viewModel<DiscountViewModel>()
    private val addNewDiscount = AddNewDiscount()
    private var discount: Discount? = null

    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentAddNewDiscountBinding.inflate(inflater, container, false)
        initViews()
        events()
        observeViews()
        return binding.root
    }

    private fun initViews() {
        val bundle = arguments ?: return
        val args = AddNewDiscountFragmentArgs.fromBundle(bundle)
        discount = args.discount
        if (sharedPreferencesManager.getUserType() == UserTypeEnum.Owner.value) {
            binding.ownerCont.visibility = View.GONE
            ownerId = sharedPreferencesManager.getOwnerId()
            getShopsByOwnerId()
        } else {
            getOwners()
        }
        if (discount != null) {
            addNewDiscount.discountId = discount?.discountId
            binding.addDiscount.textView.text = getString(string.edit)
            binding.toolbar.tvTitle.text = getString(string.edit)
            if (sharedPreferencesManager.getUserType() == UserTypeEnum.Admin.value) {
                ownerId = discount?.owner?.id
            }
            shopId = discount?.shop?.shopId
            binding.ownerList.setText(discount?.owner?.user?.name, false)
            binding.productList.setText(discount?.product?.name, false)
            binding.shopList.setText(discount?.shop?.name, false)
            binding.minQty.setText(discount?.minimumOrderNumber.toString())
            binding.maxQty.setText(discount?.maximumOrderNumber.toString())
            binding.priceAfter.setText(discount?.priceAfter.toString())
            binding.priceBefore.setText(discount?.priceBefore.toString())
            binding.percent.setText(discount?.percent.toString())
            binding.startDate.setText(discount?.start)
            binding.endDate.setText(discount?.end)
            getShopsByOwnerId()
            addNewDiscount.ownerId = ownerId
            addNewDiscount.productId = discount?.product?.id
            addNewDiscount.shopId = discount?.shop?.shopId
            productPrice = discount?.priceBefore
            getProductsByOwnerAndShopId()
        } else {
            binding.addDiscount.textView.text = getString(string.add_discount)
            binding.toolbar.tvTitle.text = getString(string.add_discount)
        }
    }

    private fun getProductsByOwnerAndShopId() {
        lifecycleScope.launch {
            viewModel.discountIntent.send(
                DiscountIntent.GetProductsByOwnerAndShopId(
                    ownerId!!,
                    shopId!!
                )
            )
        }
    }

    private fun setProductsDropDown(products: ArrayList<Product>?) {
        if (products.isNullOrEmpty()) {
            products?.add(0, Product(name = " "))
        }
        productDropDownAdapter =
            DropDownAdapter(requireContext(), R.layout.item_dropdown, products!!)
        binding.productList.setAdapter(productDropDownAdapter)
        binding.productList.setOnItemClickListener { _, _, i, _ ->
            binding.productList.setText(products[i].name, false)
            addNewDiscount.productId = products[i].id
            productId = products[i].id
            productPrice = products[i].price
            binding.priceBefore.setText(productPrice.toString())
        }
    }

    private fun setOwnerDropDown(owners: ArrayList<Owner>?) {
        if (owners.isNullOrEmpty()) {
            owners?.add(0, Owner(user = User(name = " ")))
        }
        ownerDropDownAdapter = DropDownAdapter(requireContext(), R.layout.item_dropdown, owners!!)
        binding.ownerList.setAdapter(ownerDropDownAdapter)
        binding.ownerList.setOnItemClickListener { _, _, i, _ ->
            binding.ownerList.setText(owners[i].user?.name, false)
            addNewDiscount.ownerId = owners[i].id
            ownerId = owners[i].id
            getShopsByOwnerId()
        }

    }

    private fun setShopDropDown(shops: ArrayList<Shop>?) {
        if (shops.isNullOrEmpty()) {
            shops?.add(0, Shop(name = " "))
        }
        shopDropDownAdapter = DropDownAdapter(requireContext(), R.layout.item_dropdown, shops!!)
        binding.shopList.setAdapter(shopDropDownAdapter)
        binding.shopList.setOnItemClickListener { _, _, i, _ ->
            binding.shopList.setText(shops[i].name, false)
            shopId = shops[i].id
            addNewDiscount.shopId = shops[i].id
            getProductsByOwnerAndShopId()
        }

    }

    private fun getShopsByOwnerId() {
        lifecycleScope.launch {
            ownerId?.let { viewModel.discountIntent.send(DiscountIntent.GetShopsByOwnerId(it)) }
        }
    }

    private fun getOwners() {
        lifecycleScope.launch {
            viewModel.discountIntent.send(DiscountIntent.GetOwners)
        }
    }


    private fun events() {
        binding.toolbar.mainToolbar.setNavigationOnClickListener {
            findNavController().popBackStack()
        }
        binding.startDate.setOnClickListener {
            setDatePicker(binding.startDate)
        }
        binding.endDate.setOnClickListener {
            setDatePicker(binding.endDate)
        }
        binding.percent.doOnTextChanged { text, start, before, count ->
            val percent = if (!text.isNullOrEmpty()) {
                text.toString().toFloat()
            } else {
                0f
            }
            productPrice?.let { price ->
                val priceAfter =
                    ((price * 100).toInt() - (price * percent).toInt()).toFloat() / 100f
                binding.priceAfter.setText(priceAfter.toString())
            }
        }
        binding.addDiscount.root.setOnClickListener {
            setData()
            val type = if (discount != null) {
                ProductAction.Edit
            } else {
                ProductAction.Add
            }

            lifecycleScope.launch {
                viewModel.discountIntent.send(
                    DiscountIntent.StoreUpdateDiscount(
                        addNewDiscount,
                        type
                    )
                )
            }
        }

    }

    private fun setDatePicker(date: TextInputEditText) {
        val c = Calendar.getInstance()
        if (date.text?.isNotEmpty() == true) {
            val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH)
            c.time = sdf.parse(date.text.toString()) as Date
        }
        val datePicker = DatePickerDialog(
            requireContext(),
            com.techcubics.resources.R.style.MySpinnerDatePickerStyle,
            { _, year, month, dayOfMonth ->
                if (date.text.toString().isNotEmpty()) {
                    val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH)
                    c.time = sdf.parse(date.text.toString()) as Date
                }

                date.setText(
                    String.format(
                        Locale.ENGLISH,
                        "%04d-%02d-%02d", year, month + 1, dayOfMonth
                    )
                )

            },
            c.get(Calendar.YEAR),
            c.get(Calendar.MONTH),
            c.get(Calendar.DATE)
        )
        datePicker.datePicker.minDate = Date().time
        datePicker.show()
    }

    private fun setData() {
        addNewDiscount.start = binding.startDate.text.toString().ifEmpty { null }
        addNewDiscount.end = binding.endDate.text.toString().ifEmpty { null }
        addNewDiscount.percent = binding.percent.text.toString().ifEmpty { null }
        addNewDiscount.priceAfter = binding.priceAfter.text.toString().ifEmpty { null }
        addNewDiscount.priceBefore = productPrice.toString()
        addNewDiscount.minimumOrderNumber = binding.minQty.text.toString().ifEmpty { null }
        addNewDiscount.maximumOrderNumber = binding.maxQty.text.toString().ifEmpty { null }
    }


    private fun observeViews() {
        lifecycleScope.launch {
            viewModel.discountState.collect {
                binding.loading.root.visibility = View.GONE
                binding.msgLayout.root.visibility = View.GONE
                when (it) {
                    is DiscountsStates.Idle -> {}
                    is DiscountsStates.Loading -> {
                        binding.loading.root.visibility = View.VISIBLE
                    }

                    is DiscountsStates.Error -> {
                        if(!it.error.contains(Constants.unauthenticated)){
                            Helper.showErrorDialog(requireContext(), it.error)
                        }
                    }

                    is DiscountsStates.ViewProducts -> setProductsDropDown(it.products)
                    is DiscountsStates.ViewOwners -> setOwnerDropDown(it.owners)
                    is DiscountsStates.ViewShops -> setShopDropDown(it.shops)
                    DiscountsStates.Success -> {
                        val msg = if (discount != null) {
                            getString(string.discount_updated)
                        } else {
                            getString(string.add_discount_success)
                        }
                        Helper.showSuccessDialog(requireContext(), msg)
                        findNavController().popBackStack()
                    }

                    else -> {}
                }
            }
        }
    }


    override fun onStart() {
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
        super.onStart()
    }
}
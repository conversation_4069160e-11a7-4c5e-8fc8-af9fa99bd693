<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:elevation="16dp"
    android:paddingHorizontal="16dp"
    android:paddingVertical="24dp"
    android:theme="@style/Theme.TextInputLayout"
    app:behavior_hideable="false"
    app:behavior_peekHeight="0dp"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

    <ImageView
        android:id="@+id/close_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:background="?selectableItemBackground"
        android:clickable="true"
        android:contentDescription="@string/img_cnt_desc"
        android:focusable="true"
        android:padding="5dp"
        android:src="@drawable/ic_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/app_color" />

    <TextView
        android:id="@+id/filter_title"
        style="@style/LargeBoldAppColorText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/filter_title"
        app:layout_constraintBottom_toBottomOf="@id/close_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/close_icon" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="16dp"
        android:background="@color/app_color2"
        app:layout_constraintTop_toBottomOf="@id/filter_title" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:id="@+id/scroll_view"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/search_type_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/search_type"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintTop_toTopOf="parent">

                <AutoCompleteTextView
                    android:id="@+id/search_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textAppearance="@style/Medium1RegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/search_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/search"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintTop_toBottomOf="@id/search_type_cont">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/search_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:textAppearance="@style/Medium1RegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/payment_method_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/payment_method"
                android:visibility="gone"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintTop_toBottomOf="@+id/search_type_cont">

                <AutoCompleteTextView
                    android:id="@+id/method"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textAppearance="@style/Medium1RegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:id="@+id/title_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="6dp"
                android:layout_marginTop="6dp"
                android:text="@string/product_status_title"
                android:textAppearance="@style/LargeBoldDarkText"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/search_type_cont" />

            <RadioGroup
                android:id="@+id/status_cont"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="6dp"
                android:layout_marginBottom="6dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone"
                android:weightSum="2"
                app:layout_constraintTop_toBottomOf="@id/title_status">

                <RadioButton
                    android:id="@+id/available"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="10dp"
                    android:layout_weight="1"
                    android:checked="true"
                    android:text="@string/available"
                    android:textAlignment="viewStart"
                    android:textAppearance="@style/Medium1RegularDarkText" />

                <RadioButton
                    android:id="@+id/unavailable"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:text="@string/unavailable"
                    android:textAlignment="viewStart"
                    android:textAppearance="@style/Medium1RegularDarkText" />


            </RadioGroup>

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/barrier"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:barrierDirection="bottom"
                app:constraint_referenced_ids="status_cont,search_cont,payment_method_cont" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="6dp"
                android:layout_marginTop="6dp"
                android:text="@string/sort"
                android:textAppearance="@style/LargeBoldDarkText"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/barrier" />

            <RadioGroup
                android:id="@+id/sort_cont"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="6dp"
                android:layout_marginBottom="6dp"
                android:orientation="horizontal"
                android:weightSum="2"
                app:layout_constraintTop_toBottomOf="@id/title">

                <RadioButton
                    android:id="@+id/asc"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:drawableStart="@drawable/ic_sort_asc" />

                <RadioButton
                    android:id="@+id/desc"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:checked="true"
                    android:drawableStart="@drawable/ic_sort_desc" />

            </RadioGroup>
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/governorate_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/governorate"
                android:visibility="gone"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintTop_toBottomOf="@+id/sort_cont">

                <AutoCompleteTextView
                    android:id="@+id/governorate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textAppearance="@style/Medium1RegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/region_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/region"
                android:visibility="gone"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintTop_toBottomOf="@+id/governorate_cont">

                <AutoCompleteTextView
                    android:id="@+id/region"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textAppearance="@style/Medium1RegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:baselineAligned="false"
                android:orientation="horizontal"
                android:weightSum="2"
                app:layout_constraintTop_toBottomOf="@id/region_cont">

                <include
                    android:id="@+id/search"
                    layout="@layout/btn_search_progress"
                    android:layout_width="0dp"
                    android:layout_height="45dp"
                    android:layout_margin="6dp"
                    android:layout_weight="1" />

                <include
                    android:id="@+id/clear_filter"
                    layout="@layout/btn_search_progress"
                    android:layout_width="0dp"
                    android:layout_height="45dp"
                    android:layout_margin="6dp"
                    android:layout_weight="1" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.core.widget.NestedScrollView>

    <include
        android:id="@+id/loading"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>


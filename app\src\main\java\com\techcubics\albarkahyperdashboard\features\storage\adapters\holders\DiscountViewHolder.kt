package com.techcubics.albarkahyperdashboard.features.storage.adapters.holders

import android.content.Context
import android.view.View
import androidx.navigation.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyperdashboard.databinding.ItemDiscountBinding
import com.techcubics.albarkahyperdashboard.databinding.ItemProductBinding
import com.techcubics.albarkahyperdashboard.features.storage.adapters.DiscountsAdapter
import com.techcubics.albarkahyperdashboard.features.storage.adapters.ProductsAdapter
import com.techcubics.albarkahyperdashboard.features.storage.fragments.StorageFragmentDirections
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.listeners.OnItemClickListener
import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.data.auth.utils.UserTypeEnum
import com.techcubics.domain.storage.enums.ProductStatusTypes
import com.techcubics.domain.storage.models.Discount
import org.koin.java.KoinJavaComponent
import java.util.Locale

class DiscountViewHolder(
    private val binding: ItemDiscountBinding,
    private val context: Context,
    private val listener: OnItemClickListener
) : RecyclerView.ViewHolder(binding.root) {

    private val sharedPreferencesManager: SharedPreferencesManager by KoinJavaComponent.inject(
        SharedPreferencesManager::class.java
    )
   fun setData(discount: Discount?) {
       if (sharedPreferencesManager.getUserType() == UserTypeEnum.Owner.value) {
           binding.ownerCont.visibility = View.GONE
           binding.ownerTitle.visibility = View.GONE
           binding.itemsCont.weightSum = 2F
           binding.titlesCont.weightSum = 2F
       } else {
           binding.ownerCont.visibility = View.VISIBLE
           binding.ownerTitle.visibility = View.VISIBLE
           binding.itemsCont.weightSum = 3F
           binding.titlesCont.weightSum = 3F
           discount?.owner?.user?.avatar?.let { Helper.loadImage(context, it, binding.ownerImage) }
           binding.owner.text = discount?.owner?.user?.name
       }

       discount?.shop?.logo?.let { Helper.loadImage(context, it, binding.shopImage) }
       discount?.product?.icon?.let { Helper.loadImage(context, it, binding.productImage) }
        binding.shopName.text = discount?.shop?.name
        binding.pName.text = discount?.product?.name
        "Discount#${discount?.id}".also { binding.pCode.text = it }
        "${
            java.text.NumberFormat.getNumberInstance(Locale.ENGLISH).format(discount?.priceAfter)
        }${context.getString(com.techcubics.resources.R.string.currency_name)}".also {
            binding.discountPriceAfter.text = it
        }
       "${
            java.text.NumberFormat.getNumberInstance(Locale.ENGLISH).format(discount?.priceBefore)
        }${context.getString(com.techcubics.resources.R.string.currency_name)}".also {
            binding.discountPriceBefore.text = it
        }
       binding.discountPercentage.text = discount?.percent.toString()
       binding.startDate.text = discount?.startFormat
       binding.endDate.text = discount?.endFormat
        binding.activeStatus.isChecked = (discount?.status ?: 0).toBoolean()
        binding.activeStatus.setOnClickListener {
            discount?.id?.let { it1 ->
                listener.onClick(it1, ProductStatusTypes.Active)
            }
            (bindingAdapter as DiscountsAdapter).discounts?.get(bindingAdapterPosition)?.status = binding.activeStatus.isChecked.toInt()
            (bindingAdapter as DiscountsAdapter).notifyItemChanged(bindingAdapterPosition)
        }
        binding.root.setOnClickListener {
            discount?.discountId?.let { id ->
                val action = StorageFragmentDirections.viewDiscountDetailsFragment(id)
                it.findNavController().navigate(action)
            }
        }
        binding.edit.setOnClickListener {
            discount?.discountId?.let { id ->
                val action = StorageFragmentDirections.viewDiscountDetailsFragment(id)
                it.findNavController().navigate(action)
            }
        }
    }
}
fun Int.toBoolean() = this != 0
fun Boolean.toInt() = if (this) 1 else 0

ext{

    kotlin_core_version="1.13.0"
    kotlinDependencies=[
            core:"androidx.core:core-ktx:${kotlin_core_version}"
    ]
    androidx_version="1.7.0"
    androidxDependencies=[
            core:"androidx.appcompat:appcompat:${androidx_version}"
    ]
    material_version="1.12.0"
    materialDependencies=[
            core:"com.google.android.material:material:${material_version}"
    ]
    constraint_version="2.1.4"
    constraintDependencies=[

            core:"androidx.constraintlayout:constraintlayout:${constraint_version}"
    ]



    retrofit_version="2.9.0"
    retrofitDependencies=[
            retrofit:"com.squareup.retrofit2:retrofit:${retrofit_version}",
            gson:"com.squareup.retrofit2:converter-gson:${retrofit_version}",
            scalar:"com.squareup.retrofit2:converter-scalars:${retrofit_version}",
    ]

    httpOk_version="4.10.0"
    httpOkDependencies=[
            httpOK:"com.squareup.okhttp3:logging-interceptor:${httpOk_version}"
    ]


    lifecycle_version = "2.8.0"
    lifecycleDependencies=[
            viewModel:"androidx.lifecycle:lifecycle-viewmodel-ktx:${lifecycle_version}",
            liveData:"androidx.lifecycle:lifecycle-livedata-ktx:${lifecycle_version}"
    ]

    glide_version="4.16.0"
    glideDependencies=[
            glide:"com.github.bumptech.glide:glide:${glide_version}",
            annotation:"com.github.bumptech.glide:compiler:${glide_version}"
    ]


    circleImage_version="4.3.0"
    circleImageDependencies=[
            image:"com.mikhaellopez:circularimageview:${circleImage_version}"

    ]

    googleServices_version="20.2.0"
    googleServicesDependencies=[
            auth:"com.google.android.gms:play-services-auth:${googleServices_version}"

    ]
    koin_version="3.2.2"
    koinDependencies=[

            koin:"io.insert-koin:koin-core:$koin_version",
            // Koin main features for Android
            koin_android:"io.insert-koin:koin-android:${koin_version}",
            // Java Compatibility
            javaCompat:"io.insert-koin:koin-android-compat:${koin_version}",
            // Jetpack WorkManager
            workManager:"io.insert-koin:koin-androidx-workmanager:${koin_version}",
            // Navigation Graph
            navGraph:"io.insert-koin:koin-androidx-navigation:$koin_version"


    ]

    nav_version="2.7.7"
    navDependencies=[

            fragment:"androidx.navigation:navigation-fragment-ktx:$nav_version",
            ui:"androidx.navigation:navigation-ui-ktx:$nav_version",
            // Feature module Support
            dynamicFeatures:"androidx.navigation:navigation-dynamic-features-fragment:$nav_version",
            // Testing Navigation
            testing:"androidx.navigation:navigation-testing:$nav_version"
    ]


    glide_version="4.13.2"
    glideDependencies=[
            glide:"com.github.bumptech.glide:glide:${glide_version}",
            annotation:"com.github.bumptech.glide:compiler:${glide_version}"
    ]

    circleImage_version="4.3.0"
    circleImageDependencies=[
            image:"com.mikhaellopez:circularimageview:${circleImage_version}"

    ]

    circleImageDependencies=[
            mikhaell:"com.mikhaellopez:circularimageview:4.3.0",
            hdodenhof:"de.hdodenhof:circleimageview:3.1.0",
    ]

    shimmerLayout_version="0.5.0"
    shimmerLayoutDependencies=[
            shimmerLayout:"com.facebook.shimmer:shimmer:${shimmerLayout_version}"
    ]

    lottie_version="5.2.0"
    lottieDependencies=[
            lottie:"com.airbnb.android:lottie:${lottie_version}"
    ]

    paging_version="v1.1.1"
    pagingDependencies=[
            paging:"com.github.Batterii:PagingRecycler:${paging_version}"
    ]

    rangseekbar_version="1.2.0-alpha05'"
    rangeseekbarDependencies=[
            seekbar:"com.google.android.material:material:${rangseekbar_version}"
    ]
    favorite_button_version = "0.1.5"
    favoriteButtonDependencies = [
            fav_button: "com.github.ivbaranov:materialfavoritebutton:${favorite_button_version}"

    ]
    dots_indicator_version = "4.3"

    dotsIndicatorDependencies = [
            dots_indicator: "com.tbuonomo:dotsindicator:${dots_indicator_version}"
    ]
    tab_version="2.0.0@aar"
    tabDependencies=[
            smarttablibrary:"com.ogaclejapan.smarttablayout:library:${tab_version}",
            smarttabutil:"com.ogaclejapan.smarttablayout:utils-v4:${tab_version}"
    ]



    youtube_player_version = "11.1.0"
    youtubePlayerDependencies = [
            youtube_player: "com.pierfrancescosoffritti.androidyoutubeplayer:core:$youtube_player_version"
    ]
    firebase_ui_version="8.0.1"
    firebaseDependencies=[
            database:"com.firebaseui:firebase-ui-database:${firebase_ui_version}",
            firestore:"com.firebaseui:firebase-ui-firestore:${firebase_ui_version}",
            auth: "com.firebaseui:firebase-ui-auth:${firebase_ui_version}",
            storage: "com.firebaseui:firebase-ui-storage:${firebase_ui_version}",
            remote_config:"com.google.firebase:firebase-config-ktx:21.1.2"

    ]

    firebase_auth_version = "23.0.0"
    firebaseAuthDependencies = [
            core: "com.google.firebase:firebase-auth:${firebase_auth_version}"
    ]

    firebase_fcm_bom="33.1.0"
    firebaseFcmDependencies=[
            // Import the BoM for the Firebase platform
            bom:"com.google.firebase:firebase-bom:${firebase_fcm_bom}",
            // Declare the dependencies for the Firebase Cloud Messaging and Analytics libraries
            // When using the BoM, you don't specify versions in Firebase library dependencies
            messaging:"com.google.firebase:firebase-messaging-ktx",
            crashlytics : 'com.google.firebase:firebase-crashlytics-ktx',
            analytics :  'com.google.firebase:firebase-analytics-ktx'
    ]

    facebookDepdencies=[
            sdk:"com.facebook.android:facebook-android-sdk:latest.release"
    ]

    phoneVerficationDepdencies=[
            browser:"androidx.browser:browser:1.3.0",
            google_safetynet:"com.google.android.gms:play-services-safetynet:18.0.1"
    ]

    countryCode_version="2.6.1"
    countryCodeDependencies=[
            ccp:"com.hbb20:ccp:${countryCode_version}"
    ]

    compressor_version="3.0.1"
    compressorDepdencies=[
            compressor:"id.zelory:compressor:${compressor_version}"
    ]
    materialComponent_version="1.4.0"
    materialDepdencies=[
            material:"com.google.android.material:material:${materialComponent_version}"
    ]
    zing_version="4.3.0"
    zingDepdencies=[
            core:"com.journeyapps:zxing-android-embedded:${zing_version}",
            coreLibraryDesugaring:"com.android.tools:desugar_jdk_libs:1.1.5",
            multidex:"androidx.multidex:multidex:2.0.1"
    ]

    libphonenumber_version ="8.4.1"
    libphonenumberDependencies=[
            core:"com.googlecode.libphonenumber:libphonenumber:${libphonenumber_version}"
    ]

    googleMap_version="18.2.0"
    googlemapDependencies=[
            core:"com.google.android.gms:play-services-maps:${googleMap_version}"
    ]
    googleMapUtil_version = "2.3.0"
    googleMapUtils=[
            maputils:"com.google.maps.android:android-maps-utils:${googleMapUtil_version}"
    ]

    googleMapLocation_version="21.0.1"
    googlemapLocationDependencies=[
            core:"com.google.android.gms:play-services-location:${googleMapLocation_version}"
    ]

    prefences_version="1.2.0"
    prefencesDependencies=[
            core:"androidx.preference:preference:${prefences_version}"
    ]

    googleMapPlaces_version="2.6.0"
    googleMapPlacesDependencies=[
            core:"com.google.android.libraries.places:places:${googleMapPlaces_version}"
    ]

    phoenix_version="2.1.2"
    phoenixDependencies=[
            core:"com.jakewharton:process-phoenix:${phoenix_version}"
    ]

    junit_version="4.13.2"
    juintDependencies=[
            core:"junit:junit:${junit_version}"
    ]

    test_version="1.1.5"
    testDependencies=[
            core:"androidx.test.ext:${test_version}"
    ]

    espresso_version="3.5.1"
    espressoDependencies=[
            core:"androidx.test.espresso:espresso-core:${espresso_version}"
    ]


    photoView_version="2.0.0"
    photoview_dependecy = "com.github.chrisbanes:PhotoView:$photoView_version"

    googlePlay_version="1.8.3"
    googlePlayDependencies=[
            core:"com.google.android.play:core-ktx:${googlePlay_version}"
    ]
    localHelper_version="1.5.1"
    localHelperDependencies = "com.zeugmasolutions.localehelper:locale-helper-android:${localHelper_version}"

    custom_toast_version="7.2.4"
    customToastDependencies=[
            core:"com.github.tapadoo:alerter:${custom_toast_version}"
    ]

    previewLink = "1.13.1"
    previewLinkDependency= "org.jsoup:jsoup:${previewLink}"

    colorPicker = "2.3"
    colorPickerDependency= "com.github.Dhaval2404:ColorPicker:${colorPicker}"

    stepper_version = "0.3.0"
    stepper_dependencies= "com.github.acefalobi:android-stepper:$stepper_version"

    toggle_version = "1.1.4"
    toggle_dependencies = "com.github.BeppiMenozzi:TriStateToggleButton:$toggle_version"

    searchable_multiselect_version = "2.0"
    searchable_multiselect = "com.github.telichada:SearchableMultiSelectSpinner:$searchable_multiselect_version"



}
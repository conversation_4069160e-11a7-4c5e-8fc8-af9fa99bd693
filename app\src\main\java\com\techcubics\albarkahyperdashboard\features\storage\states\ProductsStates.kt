package com.techcubics.albarkahyperdashboard.features.storage.states

import com.techcubics.domain.storage.enums.ProductStatusTypes
import com.techcubics.domain.storage.models.Category
import com.techcubics.domain.storage.models.Owner
import com.techcubics.domain.storage.models.Product
import com.techcubics.domain.storage.models.Shop

sealed class ProductsStates {
    object Idle : ProductsStates()
    object Loading : ProductsStates()
    object Pagination : ProductsStates()
    data class ViewProductDetails(val product: Product?) : ProductsStates()
    data class ViewOwners(val owners: ArrayList<Owner>?) : ProductsStates()
    data class ViewShops(val shops: ArrayList<Shop>?) : ProductsStates()
    data class ViewCategories(val categories: ArrayList<Category>?) : ProductsStates()
    data class ViewSubCategories(val subCategories: ArrayList<Category>?) : ProductsStates()
    data class ViewProducts(val products: MutableList<Product>) : ProductsStates()
    data class Error(val error: String) : ProductsStates()
    data class StatusError(val error: String, val productId: Int, val type: ProductStatusTypes) :
        ProductsStates()

    data class ServerError(val error: String) : ProductsStates()
    object Success : ProductsStates()

}
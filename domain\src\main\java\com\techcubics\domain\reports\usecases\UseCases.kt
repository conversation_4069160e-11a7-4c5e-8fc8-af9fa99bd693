package com.techcubics.domain.reports.usecases

import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.reports.repositories.ReportsRepo
import com.techcubics.domain.reports.request.ReportRequest
import com.techcubics.domain.reports.response.ReportResponse

class GetReportsUseCase(private val repo: ReportsRepo){
    suspend operator fun invoke(request: ReportRequest) : BaseResponse<ReportResponse>? {
        return repo.getReports(request)
    }
}
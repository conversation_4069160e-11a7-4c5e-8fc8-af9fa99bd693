<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".features.storage.fragments.tabs.discount.DiscountDetailsFragment"
    android:id="@+id/discountdetails_layout">
    <include android:id="@+id/toolbar_fragment"
        layout="@layout/toolbar_fragment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"/>

    <include
        android:id="@+id/msg_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        layout="@layout/include_placeholder"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar_fragment" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar_fragment"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_height="0dp"
        android:visibility="gone">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="10dp">

            <ImageView
                android:id="@+id/product_icon"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintHeight_max="450dp"
                android:background="@color/gray"
                android:src="@drawable/portrait_placeholder"
                android:adjustViewBounds="true"
                app:layout_constraintTop_toTopOf="parent"/>
<!--            <ImageView-->
<!--                android:id="@+id/tag"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:src="@drawable/ic_offer_tag"-->
<!--                app:layout_constraintTop_toTopOf="@id/product_icon"-->
<!--                app:layout_constraintEnd_toEndOf="@id/product_icon"-->
<!--                android:layout_marginStart="16dp"/>-->
            <View
                android:id="@+id/divider1"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/color_gray_7"
                android:elevation="2dp"
                app:layout_constraintTop_toBottomOf="@id/product_icon" />

            <include
                android:id="@+id/details"
                layout="@layout/include_discount_details"
                android:layout_width="match_parent"
                app:layout_constraintTop_toBottomOf="@id/divider1"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_height="wrap_content" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <include
        android:id="@+id/loading"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
package com.techcubics.data.storage.remote

import com.techcubics.data.storage.utils.Constants
import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.orders.requests.DeleteImageRequest
import com.techcubics.domain.storage.models.Category
import com.techcubics.domain.storage.models.Discount
import com.techcubics.domain.storage.models.Owner
import com.techcubics.domain.storage.models.Product
import com.techcubics.domain.storage.models.Shop
import com.techcubics.domain.storage.requests.AddNewDiscount
import com.techcubics.domain.storage.requests.Sort
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Field
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part
import retrofit2.http.Path
import retrofit2.http.Query

interface EndPoints {
    @FormUrlEncoded
    @POST(Constants.products_filter)
    suspend fun getProducts(
        @Path("userType") userType: String,
        @Query("page") page: Int,
        @Field("filter[sort]") sort: String,
        @Field("paginator") paginator: Int,
        @FieldMap filter: Map<String, String>
    ): Response<BaseResponse<MutableList<Product>>>

    @FormUrlEncoded
    @POST(Constants.products_filter)
    suspend fun getProducts(
        @Path("userType") userType: String,
        @Query("page") page: Int,
        @Field("filter[sort]") sort: String,
        @Field("paginator") paginator: Int,
        @FieldMap filter: Map<String, String>,
        @Query("is_owner") isOwner: Int
    ): Response<BaseResponse<MutableList<Product>>>

    @GET(Constants.active_status)
    suspend fun setActiveStatus(
        @Path("id") id: Int, @Path("userType") userType: String,
    ): Response<BaseResponse<Product>>?

    @GET(Constants.wanted_status)
    suspend fun setWantedStatus(
        @Path("id") id: Int,
        @Path("userType") userType: String
    ): Response<BaseResponse<Product>>?

    @GET(Constants.product_details)
    suspend fun getProductsDetails(
        @Path("id") productId: Int,
        @Path("userType") userType: String
    ): Response<BaseResponse<Product>>?
    @GET(Constants.product_details)
    suspend fun getProductsDetails(
        @Path("id") productId: Int,
        @Path("userType") userType: String,
        @Query("is_owner") isOwner: Int
    ): Response<BaseResponse<Product>>?

    @GET(Constants.owners)
    suspend fun getOwners(@Path("userType") userType : String,@Query("paginator") paginator: Int = 0): Response<BaseResponse<ArrayList<Owner>>>?

    @Multipart
    @POST(Constants.shops_by_owner_id)
    suspend fun getShops(
        @Path("userType") userType : String,
        @Part("filter[owner]") ownerId: Int,
        @Part("paginator") paginator: Int = 0
    ): Response<BaseResponse<ArrayList<Shop>>>?

    @GET(Constants.categories)
    suspend fun getCategories(@Path("userType") userType : String): Response<BaseResponse<ArrayList<Category>>>?

    @GET(Constants.sub_categories)
    suspend fun getSubCategories(@Path("id") catId: Int,@Path("userType") userType : String): Response<BaseResponse<ArrayList<Category>>>?

    @Multipart
    @POST(Constants.store_product)
    suspend fun storeProduct(
        @Path("userType") userType : String,
        @Part("category_id") categoryId: RequestBody? = null,
        @Part("sub_category_id") subCategory: RequestBody? = null,
        @Part("owner_id") ownerId: RequestBody? = null,
        @Part("shop_id") shopId: RequestBody? = null,
        @Part("price") price: RequestBody? = null,
        @Part("minimum_order_number") minOrderNum: RequestBody? = null,
        @Part("maximum_order_number") maxOrderNum: RequestBody? = null,
        @Part("ar[name]") nameAr: RequestBody? = null,
        @Part("ar[description]") descAr: RequestBody? = null,
        @Part("en[name]") nameEn: RequestBody? = null,
        @Part("en[description]") descEn: RequestBody? = null,
        @Part("video") video: RequestBody? = null,
//        @PartMap productSizes: HashMap<String, String?>? = hashMapOf(),
//        @PartMap productColors: HashMap<String, String?>? = hashMapOf(),
        @Part images: ArrayList<MultipartBody.Part>? = null,
        @Part icon: MultipartBody.Part? = null
    ): Response<BaseResponse<Product>>?

    @Multipart
    @POST(Constants.update_product)
    suspend fun updateProduct(
        @Path("userType") userType : String,
        @Path("id") id: Int,
        @Part("category_id") categoryId: RequestBody? = null,
        @Part("sub_category_id") subCategory: RequestBody? = null,
        @Part("owner_id") ownerId: RequestBody? = null,
        @Part("shop_id") shopId: RequestBody? = null,
        @Part("price") price: RequestBody? = null,
        @Part("minimum_order_number") minOrderNum: RequestBody? = null,
        @Part("maximum_order_number") maxOrderNum: RequestBody? = null,
        @Part("ar[name]") nameAr: RequestBody? = null,
        @Part("ar[description]") descAr: RequestBody? = null,
        @Part("en[name]") nameEn: RequestBody? = null,
        @Part("en[description]") descEn: RequestBody? = null,
        @Part("video") video: RequestBody? = null,
        @Part images: ArrayList<MultipartBody.Part>? = null,
        @Part icon: MultipartBody.Part? = null
    ): Response<BaseResponse<Product>>?

    @POST(Constants.delete_image)
    suspend fun deleteImages(
        @Path("userType") userType : String,
        @Path("id") productId: Int,
        @Body request: DeleteImageRequest
    ): Response<BaseResponse<Product>>?

    @FormUrlEncoded
    @POST(Constants.discounts_filter)
    suspend fun getDiscounts(
        @Path("userType") userType : String,
        @Query("page") page: Int,
        @Field("filter[sort]") sort: String,
        @Field("paginator") paginator: Int,
        @FieldMap filter: Map<String, String>
    ): Response<BaseResponse<MutableList<Discount>>>?
    @FormUrlEncoded
    @POST(Constants.products_filter)
    suspend fun getProductsByOwnerAndShopId(
        @Path("userType") userType : String,
        @Field("filter[owner]") ownerId: String,
        @Field("filter[shop]") shopId: String,
        @Field("filter[sort]") sort: String=Sort.DESC.value,
        @Field("filter[status]") status: String="1",
        @Field("paginator") paginator: Int=0,
    ): Response<BaseResponse<ArrayList<Product>>>?

    @GET(Constants.discount_details)
    suspend fun getDiscountDetails(@Path("id") discountId: Int,@Path("userType") userType : String): Response<BaseResponse<Discount>>?

    @GET(Constants.discount_active_status)
    suspend fun setDiscountActiveStatus(@Path("id") id: Int,@Path("userType") userType : String): Response<BaseResponse<Discount>>?

    @POST(Constants.update_discount)
    suspend fun updateDiscount(@Path("id") discountId:Int,@Path("userType") userType : String,@Body request: AddNewDiscount): Response<BaseResponse<Discount>>?

    @POST(Constants.store_discount)
    suspend fun storeDiscount(@Body request: AddNewDiscount,@Path("userType") userType : String): Response<BaseResponse<Discount>>?
}

package com.techcubics.albarkahyperdashboard.features.owner.states

import com.techcubics.domain.orders.models.Order
import com.techcubics.domain.orders.models.OrderStatus
import com.techcubics.domain.storage.models.Owner

sealed class OwnersViewState {

    object Idle : OwnersViewState()
    object Loading : OwnersViewState()
    object BtnLoading : OwnersViewState()
    object BtnSuccess : OwnersViewState()
    object Pagination: OwnersViewState()
    data class ServerError(val error:String): OwnersViewState()

    data class Error(val message: String) : OwnersViewState()
    data class OwnersByFilter(val owner : MutableList<Owner>) : OwnersViewState()
    data class OwnerDetails(val owner : Owner) : OwnersViewState()
    data class GetOwners(val owner : MutableList<Owner>) : OwnersViewState()
    data class OwnerStatus(val status : String) : OwnersViewState()
}
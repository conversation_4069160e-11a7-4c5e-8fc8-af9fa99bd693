package com.techcubics.albarkahyperdashboard.features.more.fragments

import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.techcubics.albarkahyperdashboard.R
import com.techcubics.albarkahyperdashboard.databinding.FragmentAccountSettingsBinding
import com.techcubics.albarkahyperdashboard.features.auth.state.AuthViewState
import com.techcubics.albarkahyperdashboard.features.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyperdashboard.features.more.intents.MoreIntent
import com.techcubics.albarkahyperdashboard.features.more.states.MoreViewState
import com.techcubics.albarkahyperdashboard.features.more.viewmodels.MoreViewModel
import com.techcubics.albarkahyperdashboard.utils.components.PopupDialog
import com.techcubics.albarkahyperdashboard.utils.components.ProgressButton
import com.techcubics.albarkahyperdashboard.utils.components.general.Helper
import com.techcubics.albarkahyperdashboard.utils.listeners.NavigationBarVisibilityListener
import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.data.auth.utils.Constants
import com.techcubics.data.auth.utils.UserTypeEnum
import com.techcubics.domain.auth.requests.UpdatePasswordRequest
import com.techcubics.domain.auth.responses.UpdatePasswordResponse
import com.techcubics.domain.more.requests.AccountSettingRequest
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException

class AccountSettingsFragment : Fragment() {

    private var _binding: FragmentAccountSettingsBinding? = null
    private val binding get() = _binding!!
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private val authViewModel by viewModel<AuthViewModel>()
    private var updatePasswordResetMutableLiveData = MutableLiveData<UpdatePasswordRequest?>()
    private var updatePasswordResponseMutableLiveData = MutableLiveData<UpdatePasswordResponse?>()
    private val moreViewModel by viewModel<MoreViewModel>()
    private lateinit var popupDialog: PopupDialog
    private lateinit var progressButton: ProgressButton
    private var uriFilePath: Uri? = null
    private var bitmap: Bitmap? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentAccountSettingsBinding.inflate(inflater, container, false)
        initViews()
        events()
        observeViews()
        return binding.root
    }

    private fun initViews() {

        popupDialog = PopupDialog(requireContext())
        progressButton = ProgressButton(requireContext(), binding.signBtnProgress)

        binding.toolbar.mainToolbar.setNavigationOnClickListener {
            findNavController().popBackStack()
        }
        binding.toolbar.tvTitle.text = getString(com.techcubics.resources.R.string.account_setting)


        if (sharedPreferencesManager.getUserPhoto() != null) {
            Helper.loadImage(
                requireContext(),
                sharedPreferencesManager.getUserPhoto()!!,binding.profilePic)
        } else {
            binding.profilePic.setImageResource(com.techcubics.resources.R.drawable.circle_gray)
        }
        binding.name.setText(sharedPreferencesManager.getName())
        binding.email.setText(sharedPreferencesManager.getEmail())
        binding.phoneNumber.setText(sharedPreferencesManager.getPhone())
        binding.toolbar.mainToolbar.setNavigationOnClickListener {
            findNavController().popBackStack()
        }
        binding.signBtnProgress.textView.text = getString(com.techcubics.resources.R.string.save)
    }

    private fun events() {
        binding.ivCamera.setOnClickListener {
            if (ContextCompat.checkSelfPermission(
                    requireContext(),
                    android.Manifest.permission.READ_EXTERNAL_STORAGE
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                openGallery()
            } else {
                ActivityCompat.requestPermissions(
                    requireActivity(),
                    arrayOf(android.Manifest.permission.READ_EXTERNAL_STORAGE),
                    1
                )
            }

        }
        binding.signBtnProgress.constraintsLayout.setOnClickListener {
            val file: File?
            if (bitmap == null) {
                file = null
            } else {
                file = bitmapToFile(bitmap, "userPhoto")
            }

            val request = AccountSettingRequest(
                name = binding.name.text.toString(),
                phone = binding.phoneNumber.text.toString(),
                avatar = file,
                email = binding.email.text.toString()
            )
            lifecycleScope.launch {
                moreViewModel.moreIntent.send(
                    MoreIntent.AccountSetting(
                        request
                    )
                )
            }
        }
        binding.changePass.setOnClickListener {
            popupDialog.showDialog(
                updatePasswordResetMutableLiveData,
                viewLifecycleOwner,
                authViewModel,
                updatePasswordResponseMutableLiveData
            )
        }
    }

    private fun observeViews() {
        lifecycleScope.launch { authViewModel.viewState.collect { collectResponseFromAuth(it) } }

        lifecycleScope.launch { moreViewModel.viewState.collect { collectResponseFromMore(it) } }

    }

    private fun collectResponseFromMore(moreViewState: MoreViewState?) {
        when (moreViewState) {
            is MoreViewState.AccountSettingConfirmMessage -> {

            }

            is MoreViewState.Profile -> {
                sharedPreferencesManager.savePhone(moreViewState.user.phone)
                sharedPreferencesManager.saveObject(Constants.USER, moreViewState.user)
                if(sharedPreferencesManager.getUserType() == UserTypeEnum.Owner.value){
                    //case owner response
                    sharedPreferencesManager.saveEmail(moreViewState.user.user?.email)
                    sharedPreferencesManager.saveName(moreViewState.user.user?.name)
                    moreViewState.user.user?.avatar?.let { sharedPreferencesManager.saveUserPhoto(it) }
                }else{
                    //case admin response
                    sharedPreferencesManager.saveEmail(moreViewState.user.email)
                    sharedPreferencesManager.saveName(moreViewState.user.name)
                    moreViewState.user.avatar?.let { sharedPreferencesManager.saveUserPhoto(it) }
                }
            }

            is MoreViewState.BtnLoading -> {
                progressButton.btnActivated()
            }

            is MoreViewState.BtnSuccess -> {
                progressButton.btnFinishedSuccessfully(
                    getString(com.techcubics.resources.R.string.save),
                    null
                )
                lifecycleScope.launch {
//                    if (uriFilePath != null) {
//                        sharedPreferencesManager.saveUserPhoto(uriFilePath.toString())
//                    }
//                    sharedPreferencesManager.saveName(binding.firstName.text.toString())
                    delay(700)
                    findNavController().popBackStack()
                }
            }

            is MoreViewState.Error -> {
                progressButton.btnFinishedFailed(
                    getString(com.techcubics.resources.R.string.save),
                    null
                )
                if(!moreViewState.message.contains(Constants.unauthenticated)){
                    Helper.showErrorDialog(requireContext(), moreViewState.message)
                }
            }

            else -> {}
        }

        updatePasswordResponseMutableLiveData.observe(viewLifecycleOwner){
            if(it != null){
                findNavController().navigate(R.id.toLogin)
            }
        }
    }

    private fun collectResponseFromAuth(authViewState: AuthViewState?) {

        when (authViewState) {
            is AuthViewState.UpdatePasswordMessage -> {
                updatePasswordResponseMutableLiveData.postValue(
                    UpdatePasswordResponse(
                        true,
                        authViewState.message
                    )
                )
            }
            is AuthViewState.Error -> {
                if (authViewState.message.contains(com.techcubics.domain.common.Constants.SERVER_ERROR)) {
                    Helper.showErrorDialog(
                        requireContext(),
                        getString(com.techcubics.resources.R.string.server_error)
                    )
                } else {
                    updatePasswordResponseMutableLiveData.postValue(
                        UpdatePasswordResponse(
                            false,
                            authViewState.message
                        )
                    )
                    if(!authViewState.message.contains(Constants.unauthenticated)){
                        Helper.showErrorDialog(requireContext(), authViewState.message)
                    }
                }

            }

            else -> {}
        }
        authViewModel.viewState.value = null


    }



    private fun openGallery() {
        val intent = Intent()
        intent.action = Intent.ACTION_GET_CONTENT
        intent.type = "image/*"
        startActivityForResult(intent, 10)
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 10 && resultCode == Activity.RESULT_OK) {
            uriFilePath = data?.data
            if (uriFilePath != null) {
                Log.i("here", "uri path$uriFilePath")
                Helper.loadImage(
                    requireContext(),
                    uriFilePath.toString(),
                    binding.profilePic
                )
                try {
                    bitmap = MediaStore.Images.Media.getBitmap(
                        requireContext().contentResolver,
                        uriFilePath
                    )
                } catch (e: IOException) {
                    e.printStackTrace()
                }

            }

        }
    }


    private fun bitmapToFile(
        bitmap: Bitmap?,
        fileNameToSave: String
    ): File { // File name like "image.png"
        //create a file to write bitmap data
        val file: File?

        file = File(requireContext().cacheDir, fileNameToSave)
        file.createNewFile()

        val bos = ByteArrayOutputStream()
        bitmap?.compress(Bitmap.CompressFormat.JPEG, 50 /*ignored for PNG*/, bos)

        val bitmapdata = bos.toByteArray()

        var fos: FileOutputStream? = null
        try {
            fos = FileOutputStream(file)
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
        }
        try {
            fos!!.write(bitmapdata)
            fos.flush()
            fos.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return file
    }


    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}
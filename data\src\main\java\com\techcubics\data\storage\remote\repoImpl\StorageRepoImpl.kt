package com.techcubics.data.storage.remote.repoImpl

import android.util.Log
import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.data.common.RetrofitBuilder
import com.techcubics.data.storage.remote.EndPoints
import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.common.RepositoryResponse
import com.techcubics.domain.orders.requests.DeleteImageRequest
import com.techcubics.domain.storage.models.Category
import com.techcubics.domain.storage.models.Discount
import com.techcubics.domain.storage.models.Owner
import com.techcubics.domain.storage.models.Product
import com.techcubics.domain.storage.models.Shop
import com.techcubics.domain.storage.repositories.StorageRepo
import com.techcubics.domain.storage.requests.AddNewDiscount
import com.techcubics.domain.storage.requests.AddNewProductRequest
import com.techcubics.domain.storage.requests.ProductsFilterRequestBody

class StorageRepoImpl(private val retrofitBuilder: RetrofitBuilder, private val sharedPreferencesManager: SharedPreferencesManager) : StorageRepo,
    RepositoryResponse {

    private val api = retrofitBuilder.start()?.create(EndPoints::class.java)
    override suspend fun getProducts(
        page: Int,
        filter: ProductsFilterRequestBody
    ): BaseResponse<MutableList<Product>>? {
        return try {
            val result =    if (filter.isOwner==0){
                api?.getProducts(userType = sharedPreferencesManager.getUserType(),page, filter.sort, filter.paginator, filter.search,
                    filter.isOwner!!
                )
            }else{
                api?.getProducts(userType = sharedPreferencesManager.getUserType(),page, filter.sort, filter.paginator, filter.search)
            }
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun setActiveStatus(id: Int): BaseResponse<Product>? {
        return try {
            val result = api?.setActiveStatus(id,userType = sharedPreferencesManager.getUserType())
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun setWantedStatus(id: Int): BaseResponse<Product>? {
        return try {
            val result = api?.setWantedStatus(id,userType = sharedPreferencesManager.getUserType())
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getProductDetails(productId: Int,isOwner:Int?): BaseResponse<Product>? {
        return try {
            val result = if (isOwner == 0) {
                api?.getProductsDetails(
                    productId,
                    userType = sharedPreferencesManager.getUserType(),
                    isOwner
                )
            } else {
                api?.getProductsDetails(
                    productId,
                    userType = sharedPreferencesManager.getUserType()
                )
            }
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getOwners(): BaseResponse<ArrayList<Owner>>? {
        return try {
            val result = api?.getOwners(userType = sharedPreferencesManager.getUserType())
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getShops(ownerId: Int): BaseResponse<ArrayList<Shop>>? {
        return try {
            val result = api?.getShops(userType = sharedPreferencesManager.getUserType(),ownerId)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getCategories(): BaseResponse<ArrayList<Category>>? {
        return try {
            val result = api?.getCategories(userType = sharedPreferencesManager.getUserType())
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getSubCategories(catId: Int): BaseResponse<ArrayList<Category>>? {
        return try {
            val result = api?.getSubCategories(catId,userType = sharedPreferencesManager.getUserType())
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun storeProduct(request: AddNewProductRequest): BaseResponse<Product>? {
        return try {
            val result = api?.storeProduct(
                userType = sharedPreferencesManager.getUserType(),
                request.categoryId,
                request.subCategoryId,
                request.ownerId,
                request.shopId,
                request.price,
                request.minimumOrderNumber,
                request.maximumOrderNumber,
                request.nameAr,
                request.descriptionAr,
                request.nameEn,
                request.descriptionEn,
                request.video,
//                request.productSizes,
//                request.productColors,
                request.images,
                request.icon
            )
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun updateProduct(request: AddNewProductRequest): BaseResponse<Product>? {
        return try {
            val result = api?.updateProduct(
                userType = sharedPreferencesManager.getUserType(),
                request.productId!!,
                request.categoryId,
                request.subCategoryId,
                request.ownerId,
                request.shopId,
                request.price,
                request.minimumOrderNumber,
                request.maximumOrderNumber,
                request.nameAr,
                request.descriptionAr,
                request.nameEn,
                request.descriptionEn,
                request.video,
//                request.productSizes,
//                request.productColors,
                request.images,
                request.icon
            )
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun deleteImages(request: DeleteImageRequest): BaseResponse<Product>? {
        return try {
            val result = api?.deleteImages(userType = sharedPreferencesManager.getUserType(),request.productId!!, request)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun storeDiscount(request: AddNewDiscount): BaseResponse<Discount>? {
        return try {
            val result = api?.storeDiscount(request,userType = sharedPreferencesManager.getUserType())
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun updateDiscount(request: AddNewDiscount): BaseResponse<Discount>? {
        return try {
            val result = api?.updateDiscount(request.discountId!!,userType = sharedPreferencesManager.getUserType(), request)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun setDiscountActiveStatus(id: Int): BaseResponse<Discount>? {
        return try {
            val result = api?.setDiscountActiveStatus(id,userType = sharedPreferencesManager.getUserType())
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getDiscountDetails(discountId: Int): BaseResponse<Discount>? {
        return try {
            val result = api?.getDiscountDetails(discountId,userType = sharedPreferencesManager.getUserType())
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getDiscounts(
        page: Int,
        request: ProductsFilterRequestBody
    ): BaseResponse<MutableList<Discount>>? {
        return try {
            val result = api?.getDiscounts(userType = sharedPreferencesManager.getUserType(),page, request.sort, request.paginator, request.search)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getProductsByOwnerAndShopId(
        ownerId: Int,
        shopId: Int
    ): BaseResponse<ArrayList<Product>>? {
        return try {
            val result = api?.getProductsByOwnerAndShopId(userType = sharedPreferencesManager.getUserType(),ownerId.toString(), shopId.toString())
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }
}
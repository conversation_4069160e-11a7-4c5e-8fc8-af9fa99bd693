package com.techcubics.domain.chats.usecases

import com.techcubics.domain.chats.repo.ChatRepo
import com.techcubics.domain.chats.request.SendMessageRequest

class GetOrdersChatRoomsUseCase(private val repo: ChatRepo) {
    suspend operator fun invoke(page: Int,ownerId:Int,shopId:Int) = repo.getOrdersChatRooms(page,ownerId, shopId)
}

class GetOrderChatsUseCase(private val repo: ChatRepo) {
    suspend operator fun invoke(id: Int,ownerId:Int,shopId:Int) = repo.getOrderChats(id,ownerId, shopId)
}

class SendOrderChatMessageUseCase(private val repo: ChatRepo) {
    suspend operator fun invoke(request: SendMessageRequest,ownerId:Int,shopId:Int)= repo.sendOrderChatMessage(request,ownerId, shopId)
}

class GetSupportChatRoomsUseCase(private val repo: ChatRepo) {
    suspend operator fun invoke(page: Int) = repo.getSupportChatRooms(page)
}

class GetSupportChatsUseCase(private val repo: ChatRepo) {
    suspend operator fun invoke(id: Int) = repo.getSupportChats(id)
}

class SendSupportChatMessageUseCase(private val repo: ChatRepo) {
    suspend operator fun invoke(request: SendMessageRequest)= repo.sendSupportChatMessage(request)
}


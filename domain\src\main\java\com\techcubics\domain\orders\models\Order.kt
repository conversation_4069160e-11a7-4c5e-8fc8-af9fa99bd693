package com.techcubics.domain.orders.models

import com.google.gson.annotations.SerializedName

data class Order(

  @SerializedName("order_id") var orderId: Int? = null,
  @SerializedName("order_code") var orderCode: String? = null,
  @SerializedName("shop") var shop: Shop? = Shop(),
  @SerializedName("count_items") var countItems: Int? = null,
  @SerializedName("date") var date: String? = null,
  @SerializedName("total") var total: String? = null,
  @SerializedName("status_pay") var statusPay: String? = null,
  @SerializedName("order_status") var orderStatus: String? = null,
  @SerializedName("location") var location: Location? = Location(),
  @SerializedName("delivery_price") var deliveryPrice: Double? = null,
  @SerializedName("customer"     ) var customer    : Customer? = Customer(),
  @SerializedName("payment_method") var paymentMethod: String? = null,
  @SerializedName("coupon_code") var couponCode: String? = null,
  @SerializedName("coupon_price") var couponPrice: Double? = null,
  @SerializedName("total_price") var totalPrice: Double? = null,
  @SerializedName("range_discount") var rangeDiscount: Double? = null,
  @SerializedName("range_discount_amount") var rangeDiscountAmount: Double? = null,
  @SerializedName("range_price_from") var rangePriceFrom: Double? = null,
  @SerializedName("range_price_to") var rangePriceTo: Double? = null,
  @SerializedName("amount") var amount: Double? = null,
  @SerializedName("time") var time: String? = null,
  @SerializedName("format_date") var formatDate: String? = null,
  @SerializedName("day") var day: String? = null,
  @SerializedName("special_order") var specialOrder: String? = null,
  @SerializedName("note") var note: String? = null,
  @SerializedName("order_details") var orderDetails: ArrayList<OrderDetails> = arrayListOf()
)
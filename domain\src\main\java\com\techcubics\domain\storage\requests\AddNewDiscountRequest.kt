package com.techcubics.domain.storage.requests

import com.google.gson.annotations.SerializedName
import com.techcubics.domain.storage.models.AddColor
import com.techcubics.domain.storage.models.AddSize
import okhttp3.MultipartBody
import okhttp3.RequestBody
import java.io.File

data class AddNewDiscount(
    var discountId:Int? =null,
    @SerializedName("product_id")var productId:Int? =null,
    @SerializedName("owner_id") var ownerId: Int?=null,
    @SerializedName("shop_id") var shopId: Int?=null,
    @SerializedName("price_before") var priceBefore: String?=null,
    @SerializedName("price_after") var priceAfter: String?=null,
    @SerializedName("percent") var percent: String?=null,
    @SerializedName("minimum_order_number") var minimumOrderNumber: String?=null,
    @SerializedName("maximum_order_number") var maximumOrderNumber: String?=null,
    @SerializedName("start") var start: String?=null,
    @SerializedName("end") var end: String?=null,
)
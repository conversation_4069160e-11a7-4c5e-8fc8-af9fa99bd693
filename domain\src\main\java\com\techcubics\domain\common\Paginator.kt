package com.techcubics.domain.common

import com.google.gson.annotations.SerializedName

data class Paginator(
    @SerializedName("total")
    val total: Int,
    @SerializedName("count")
    val count: Int,
    @SerializedName("currentPage")
    val currentPage: Int,
    @SerializedName("lastPage")
    val lastPage: Int,
    @SerializedName("hasMorePages")
    val hasMorePages: <PERSON>olean,
    @SerializedName("nextPageUrl")
    val nextPageUrl: String,
    @SerializedName("previousPageUrl")
    val previousPageUrl: String

)

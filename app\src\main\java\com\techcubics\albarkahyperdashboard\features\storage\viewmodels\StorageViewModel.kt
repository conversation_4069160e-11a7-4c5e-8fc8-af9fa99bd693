package com.techcubics.albarkahyperdashboard.features.storage.viewmodels

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.techcubics.albarkahyperdashboard.features.storage.fragments.tabs.product.ProductsFragment
import com.techcubics.albarkahyperdashboard.features.storage.intents.DiscountIntent
import com.techcubics.albarkahyperdashboard.features.storage.intents.ProductIntent
import com.techcubics.albarkahyperdashboard.features.storage.states.DiscountsStates
import com.techcubics.albarkahyperdashboard.features.storage.states.ProductsStates
import com.techcubics.domain.common.Constants
import com.techcubics.domain.storage.enums.ProductAction
import com.techcubics.domain.storage.enums.ProductStatusTypes
import com.techcubics.domain.storage.models.Discount
import com.techcubics.domain.storage.models.Product
import com.techcubics.domain.storage.requests.AddNewDiscount
import com.techcubics.domain.storage.requests.AddNewProduct
import com.techcubics.domain.storage.requests.ProductsFilter
import com.techcubics.domain.storage.usecases.GetDiscountDetailsUseCase
import com.techcubics.domain.storage.usecases.GetDiscountsUseCase
import com.techcubics.domain.storage.usecases.GetOwnersUseCase
import com.techcubics.domain.storage.usecases.GetProductDetailsUseCase
import com.techcubics.domain.storage.usecases.GetProductsUseCase
import com.techcubics.domain.storage.usecases.GetShopsByOwnerIdUseCase
import com.techcubics.domain.storage.usecases.SetDiscountStatusUseCase
import com.techcubics.domain.storage.usecases.SetStatusUseCase
import com.techcubics.domain.storage.usecases.StoreDiscountUseCase
import com.techcubics.domain.storage.usecases.StoreProductUseCase
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.consumeAsFlow
import kotlinx.coroutines.launch

class StorageViewModel(
    private val getProductsUseCase: GetProductsUseCase,
    private val setStatusUseCase: SetStatusUseCase,
    private val getProductDetailsUseCase: GetProductDetailsUseCase,
    private val getOwnersUseCase: GetOwnersUseCase,
    private val getShopsByOwnerIdUseCase: GetShopsByOwnerIdUseCase,
    private val storeProductUseCase: StoreProductUseCase,
    private val getDiscountsUseCase: GetDiscountsUseCase,
    private val getDiscountDetailsUseCase: GetDiscountDetailsUseCase,
    private val storeDiscountUseCase: StoreDiscountUseCase,
    private val setDiscountStatusUseCase: SetDiscountStatusUseCase,

    ) : ViewModel() {
//product
    val productIntent = Channel<ProductIntent>(Channel.UNLIMITED)

    private val _productState = MutableStateFlow<ProductsStates>(ProductsStates.Idle)
    val productState: StateFlow<ProductsStates> get() = _productState

    private val _hasMorePagesState = MutableStateFlow(false)
    val hasMorePagesState: StateFlow<Boolean> get() = _hasMorePagesState

    private val _pageState = MutableStateFlow(1)
    val pageState: StateFlow<Int> get() = _pageState

    private val products = mutableListOf<Product>()
    private val _filterStates = MutableStateFlow<ProductsFilter?>(null)
    val filterStates: StateFlow<ProductsFilter?> get() = _filterStates

    ///discounts
    val discountIntent = Channel<DiscountIntent>(Channel.UNLIMITED)

    private val _discountState = MutableStateFlow<DiscountsStates>(DiscountsStates.Idle)

    val discountState: StateFlow<DiscountsStates> get() = _discountState

    private val _disHasMorePagesState = MutableStateFlow(false)

    val disHasMorePagesState: StateFlow<Boolean> get() = _disHasMorePagesState

    private val _disPageState = MutableStateFlow(1)

    val disPageState: StateFlow<Int> get() = _disPageState

    private val discounts = mutableListOf<Discount>()

    private val _disFilterStates = MutableStateFlow<ProductsFilter?>(null)

    val disFilterStates: StateFlow<ProductsFilter?> get() = _disFilterStates

    init {
        handleIntent()
    }

    private fun handleIntent() {
        viewModelScope.launch {
            productIntent.consumeAsFlow().collect {
                when (it) {
                    is ProductIntent.GetProducts -> getProductsByFilter(it.filter, it.page)
                    is ProductIntent.SetStatus -> setStatus(it.productId, it.type)
                    is ProductIntent.GetProductDetails -> getProductDetails(it.productId,it.isOwner)
                    is ProductIntent.GetOwners -> getOwners()
                    is ProductIntent.GetShopsByOwnerId -> getShopsByOwnerId(it.ownerId)
                    is ProductIntent.StoreUpdateProduct -> storeProduct(it.request, it.action)
                    else->{}
                }
            }
        }
        viewModelScope.launch {
            discountIntent.consumeAsFlow().collect {
                when (it) {
                    is DiscountIntent.GetDiscounts -> getDiscountsByFilter(it.filter, it.page)
                    is DiscountIntent.SetStatus -> setDiscountStatus(it.discountId)
                    is DiscountIntent.GetDiscountDetails -> getDiscountDetails(it.discountId)
                    is DiscountIntent.GetOwners -> getOwners()
                    is DiscountIntent.GetShopsByOwnerId -> getShopsByOwnerId(it.ownerId)
                    is DiscountIntent.StoreUpdateDiscount -> storeDiscount(it.request, it.action)
                    else->{}
                }
            }
        }
    }
    private fun getProductDetails(productId: Int, isOwner: Int?) {
        _productState.value = ProductsStates.Loading
        viewModelScope.launch {
            val result = getProductDetailsUseCase.invoke(productId,isOwner)
            _productState.value = try {
                if (result?.message?.contains(Constants.SERVER_ERROR) == true) {
                    ProductsStates.ServerError(result.message!!)
                } else if (result?.data != null) {
                    ProductsStates.ViewProductDetails(result.data)
                } else {
                    ProductsStates.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                ProductsStates.Error(e.localizedMessage)
            }
        }
    }
    private fun storeProduct(request: AddNewProduct, action: ProductAction) {
        _productState.value = ProductsStates.Loading
        viewModelScope.launch {
            val result = storeProductUseCase.invoke(request, action)
            _productState.value = try {
                if (result?.data != null) {
                    ProductsStates.Success
                } else {
                    ProductsStates.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                ProductsStates.Error(e.localizedMessage)
            }
        }
    }
    private fun getShopsByOwnerId(ownerId: Int) {
        _productState.value = ProductsStates.Loading
        viewModelScope.launch {
            val result = getShopsByOwnerIdUseCase.invoke(ownerId)
            _productState.value = try {
                if (result?.data != null) {
                    ProductsStates.ViewShops(result.data)
                } else {
                    ProductsStates.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                ProductsStates.Error(e.localizedMessage)
            }
        }
    }

    private fun getOwners() {
        _productState.value = ProductsStates.Loading
        viewModelScope.launch {
            val result = getOwnersUseCase.invoke()
            _productState.value = try {
                if (result?.data != null) {
                    ProductsStates.ViewOwners(result.data)
                } else {
                    ProductsStates.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                ProductsStates.Error(e.localizedMessage)
            }
        }
    }
    private fun setStatus(productId: Int, type: ProductStatusTypes) {
        viewModelScope.launch {
            val result = setStatusUseCase.invoke(productId, type)
            _productState.value = try {
                if (result?.data != null) {
                    ProductsStates.Idle
                } else {
                    ProductsStates.StatusError(result?.message ?: "error", productId,type)
                }
            } catch (e: Exception) {
                ProductsStates.StatusError(e.localizedMessage,productId,type)
            }
        }
    }


    private fun getProductsByFilter(filter: ProductsFilter? = null, page: Int = 1) {
        _filterStates.value = filter
        if (page == 1) {
            Log.d("ptime", "appendProducts: 0- "+System.currentTimeMillis())
            _productState.value = ProductsStates.Loading
            _pageState.value = page
            _hasMorePagesState.value = false
            products.clear()
        }
        if (hasMorePagesState.value) {
            _productState.value = ProductsStates.Pagination
        }
        viewModelScope.launch {
            val result = getProductsUseCase.invoke(page, filter!!)
            _productState.value = try {
                if (result?.message?.contains(Constants.SERVER_ERROR) == true) {
                    ProductsStates.ServerError(result.message!!)
                } else if (result?.data != null) {
                    _hasMorePagesState.value = result.pagingator?.hasMorePages ?: false
                    if (result.pagingator?.hasMorePages == true) {
                        _pageState.value =
                            result.pagingator?.currentPage?.plus(1) ?: pageState.value
                    }
                    appendProducts(result.data!!)
                } else {
                    ProductsStates.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                ProductsStates.Error(e.localizedMessage)
            }
        }
    }

    private fun appendProducts(data: MutableList<Product>): ProductsStates.ViewProducts {
        if (!products.containsAll(data)) {
            products.addAll(data)
        }
        Log.d(ProductsFragment.TAG, "appendProducts: 1- "+System.currentTimeMillis())
        return ProductsStates.ViewProducts(products)
    }



    /////discounts
    private fun getDiscountDetails(discountId: Int) {
        _discountState.value = DiscountsStates.Loading
        viewModelScope.launch {
            val result = getDiscountDetailsUseCase.invoke(discountId)
            _discountState.value = try {
                if (result?.message?.contains(Constants.SERVER_ERROR) == true) {
                    DiscountsStates.ServerError(result.message!!)
                } else if (result?.data != null) {
                    DiscountsStates.ViewDiscountDetails(result.data)
                } else {
                    DiscountsStates.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                DiscountsStates.Error(e.localizedMessage)
            }
        }
    }
    private fun storeDiscount(request: AddNewDiscount, action: ProductAction) {
        _discountState.value = DiscountsStates.Loading
        viewModelScope.launch {
            val result = storeDiscountUseCase.invoke(request, action)
            _discountState.value = try {
                if (result?.data != null) {
                    DiscountsStates.Success
                } else {
                    DiscountsStates.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                DiscountsStates.Error(e.localizedMessage)
            }
        }
    }

    private fun setDiscountStatus(discountId: Int) {
        viewModelScope.launch {
            val result = setDiscountStatusUseCase.invoke(discountId)
            _discountState.value = try {
                if (result?.data != null) {
                    DiscountsStates.Idle
                } else {
                    DiscountsStates.StatusError(result?.message ?: "error", discountId)
                }
            } catch (e: Exception) {
                DiscountsStates.StatusError(e.localizedMessage,discountId)
            }
        }
    }


    private fun getDiscountsByFilter(filter: ProductsFilter? = null, page: Int = 1) {
        _disFilterStates.value = filter
        if (page == 1) {
            _discountState.value = DiscountsStates.Loading
            _disPageState.value = page
            _disHasMorePagesState.value = false
            discounts.clear()
        }
        if (disHasMorePagesState.value) {
            _discountState.value = DiscountsStates.Pagination
        }
        viewModelScope.launch {
            val result = getDiscountsUseCase.invoke(page, filter!!)
            _discountState.value = try {
                if (result?.message?.contains(Constants.SERVER_ERROR) == true) {
                    DiscountsStates.ServerError(result.message!!)
                } else if (result?.data != null) {
                    _disHasMorePagesState.value = result.pagingator?.hasMorePages ?: false
                    if (result.pagingator?.hasMorePages == true) {
                        _disPageState.value =
                            result.pagingator?.currentPage?.plus(1) ?: disPageState.value
                    }
                    appendDiscounts(result.data!!)
                } else {
                    DiscountsStates.Error(result?.message ?: "error")
                }
            } catch (e: Exception) {
                DiscountsStates.Error(e.localizedMessage)
            }
        }
    }

    private fun appendDiscounts(data: MutableList<Discount>): DiscountsStates.ViewDiscounts {
        if (!discounts.containsAll(data)) {
            discounts.addAll(data)
        }
        Log.d("ptime", "appendProducts: 1- "+System.currentTimeMillis())
        return DiscountsStates.ViewDiscounts(discounts)
    }
}
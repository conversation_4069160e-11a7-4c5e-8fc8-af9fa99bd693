package com.techcubics.domain.owner.requests

import com.google.gson.annotations.SerializedName
import com.techcubics.domain.storage.requests.Paginator
import com.techcubics.domain.storage.requests.Sort

data class OwnerFilterRequest(
    var search: Map<String,String>,
    @SerializedName("paginator") var paginator: Int= Paginator.ENABLED.value
)
data class OwnerFilterRequestBody(
    var search: Map<String, String>,
    var paginator: Int
)

package com.techcubics.data.owner.repoImpl

import com.techcubics.data.auth.local.SharedPreferencesManager
import com.techcubics.domain.common.BaseResponse
import com.techcubics.domain.common.RepositoryResponse
import com.techcubics.data.owner.remote.EndPoints
import com.techcubics.data.common.RetrofitBuilder
import com.techcubics.domain.owner.repositories.OwnersRepo
import com.techcubics.domain.owner.requests.OwnerFilterRequestBody
import com.techcubics.domain.owner.requests.OwnerRequest
import com.techcubics.domain.owner.requests.UpdateOwnerPasswordRequest
import com.techcubics.domain.storage.models.Owner

class OwnersRepoImpl(private val retrofitBuilder: RetrofitBuilder, private val sharedPreferencesManager: SharedPreferencesManager) : OwnersRepo,
    RepositoryResponse {

    private val api=retrofitBuilder.start()?.create(EndPoints::class.java)


    override suspend fun getOwners(page : Int?): BaseResponse<MutableList<Owner>>? {
        try {
            val result = api
                ?.getOwners(userType = sharedPreferencesManager.getUserType(),page)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun ownersByFilter(
        page: Int,
        filter: OwnerFilterRequestBody
    ): BaseResponse<MutableList<Owner>>? {
        try {
            val result = api
                ?.ownersByFilter(userType = sharedPreferencesManager.getUserType(),page, filter.paginator, filter.search)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun createOwner(request: OwnerRequest): BaseResponse<Nothing>? {
        try {
            val result = api
                ?.createOwner(userType = sharedPreferencesManager.getUserType(),request)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun ownerDetails(id: String): BaseResponse<Owner>? {
        try {
            val result = api
                ?.ownerDetails(userType = sharedPreferencesManager.getUserType(),id)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun updateOwnerStatus(id: String): BaseResponse<Nothing>? {
        try {
            val result = api
                ?.updateOwnerStatus(userType = sharedPreferencesManager.getUserType(),id)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun updateOwner(id: String, request: OwnerRequest): BaseResponse<Nothing>? {
        try {
            val result = api
                ?.updateOwner(userType = sharedPreferencesManager.getUserType(),id,request)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun updateOwnerPassword(request: UpdateOwnerPasswordRequest): BaseResponse<Nothing>? {
        try {
            val result = api
                ?.updateOwnerPassword(userType = sharedPreferencesManager.getUserType(),request)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }


}
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="horizontal"
    android:visibility="visible">

<!--    <LinearLayout-->
<!--        android:layout_width="40dp"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:orientation="vertical">-->

<!--        <FrameLayout-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content">-->

<!--        <de.hdodenhof.circleimageview.CircleImageView-->
<!--            android:id="@+id/pending_circle"-->
<!--            android:layout_width="40dp"-->
<!--            android:layout_height="40dp"-->
<!--            android:layout_gravity="bottom|center_horizontal"-->
<!--            android:src="@drawable/circle_with_borders" />-->
<!--        <View-->
<!--            android:id="@+id/circle_one"-->
<!--            android:layout_width="20dp"-->
<!--            android:layout_height="20dp"-->
<!--            android:layout_gravity="center"-->
<!--            android:background="@drawable/ic_clock" />-->
<!--        </FrameLayout>-->
<!--        <TextView-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_gravity="center"-->
<!--            android:singleLine="false"-->
<!--            android:textAppearance="@style/ExtraSmallBoldDarkText"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="@string/pending"/>-->
<!--    </LinearLayout>-->

<!--    <View-->
<!--        android:id="@+id/line_one"-->
<!--        android:layout_width="30dp"-->
<!--        android:layout_height="3dp"-->
<!--        android:layout_marginBottom="20dp"-->
<!--        android:layout_gravity="center_vertical"-->
<!--        android:background="@drawable/divider" />-->

<!--    <LinearLayout-->
<!--        android:id="@+id/recieved_frame"-->
<!--        android:layout_width="40dp"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:orientation="vertical">-->

<!--        <FrameLayout-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content">-->

<!--            <de.hdodenhof.circleimageview.CircleImageView-->
<!--                android:id="@+id/recieved_circle"-->
<!--                android:layout_width="40dp"-->
<!--                android:layout_height="40dp"-->
<!--                android:layout_gravity="bottom|center_horizontal"-->
<!--                android:src="@drawable/circle_with_borders" />-->
<!--            <View-->
<!--                android:id="@+id/circle_two"-->
<!--                android:layout_width="20dp"-->
<!--                android:layout_height="20dp"-->
<!--                android:layout_gravity="center"-->
<!--                android:background="@drawable/ic_clock" />-->
<!--        </FrameLayout>-->
<!--        <TextView-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="8dp"-->
<!--            android:textAppearance="@style/ExtraSmallBoldDarkText"-->
<!--            android:layout_gravity="center"-->
<!--            android:text="@string/receive"/>-->
<!--    </LinearLayout>-->


<!--    <View-->
<!--        android:id="@+id/line_two"-->
<!--        android:layout_width="30dp"-->
<!--        android:layout_height="3dp"-->
<!--        android:layout_marginBottom="20dp"-->
<!--        android:layout_gravity="center_vertical"-->
<!--        android:background="@drawable/divider" />-->

<!--    <LinearLayout-->
<!--        android:id="@+id/returned_frame"-->
<!--        android:layout_width="40dp"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:orientation="vertical">-->
<!--        <FrameLayout-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content">-->

<!--            <de.hdodenhof.circleimageview.CircleImageView-->
<!--                android:id="@+id/returned_circle"-->
<!--                android:layout_width="40dp"-->
<!--                android:layout_height="40dp"-->
<!--                android:layout_gravity="bottom|center_horizontal"-->
<!--                android:src="@drawable/circle_with_borders" />-->
<!--            <View-->
<!--                android:id="@+id/circle_three"-->
<!--                android:layout_width="20dp"-->
<!--                android:layout_height="20dp"-->
<!--                android:layout_gravity="center"-->
<!--                android:background="@drawable/ic_clock" />-->
<!--        </FrameLayout>-->

<!--        <TextView-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_gravity="center"-->
<!--            android:layout_marginTop="8dp"-->
<!--            android:textAppearance="@style/ExtraSmallBoldDarkText"-->
<!--            android:text="@string/returned"/>-->
<!--    </LinearLayout>-->

<!--    <View-->
<!--        android:id="@+id/line_three"-->
<!--        android:layout_width="30dp"-->
<!--        android:layout_height="3dp"-->
<!--        android:layout_gravity="center_vertical"-->
<!--        android:layout_marginBottom="20dp"-->
<!--        android:background="@drawable/divider" />-->

<!--    <LinearLayout-->
<!--        android:id="@+id/canceled_frame"-->
<!--        android:layout_width="40dp"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:orientation="vertical">-->
<!--        <FrameLayout-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content">-->

<!--            <de.hdodenhof.circleimageview.CircleImageView-->
<!--                android:id="@+id/cancelled_circle"-->
<!--                android:layout_width="40dp"-->
<!--                android:layout_height="40dp"-->
<!--                android:layout_gravity="bottom|center_horizontal"-->
<!--                android:src="@drawable/circle_with_borders" />-->
<!--            <View-->
<!--                android:id="@+id/circle_four"-->
<!--                android:layout_width="20dp"-->
<!--                android:layout_height="20dp"-->
<!--                android:layout_gravity="center"-->
<!--                android:background="@drawable/ic_clock" />-->
<!--        </FrameLayout>-->

<!--        <TextView-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_gravity="center"-->
<!--            android:layout_marginTop="8dp"-->
<!--            android:textAppearance="@style/ExtraSmallBoldDarkText"-->
<!--            android:text="@string/cancel"/>-->
<!--    </LinearLayout>-->
<!--    <View-->
<!--        android:id="@+id/line_four"-->
<!--        android:layout_width="30dp"-->
<!--        android:layout_height="3dp"-->
<!--        android:layout_marginBottom="20dp"-->
<!--        android:layout_gravity="center_vertical"-->
<!--        android:background="@drawable/divider" />-->

<!--    <LinearLayout-->
<!--        android:id="@+id/completed_frame"-->
<!--        android:layout_width="40dp"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:orientation="vertical">-->
<!--        <FrameLayout-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content">-->

<!--            <de.hdodenhof.circleimageview.CircleImageView-->
<!--                android:id="@+id/completed_circle"-->
<!--                android:layout_width="40dp"-->
<!--                android:layout_height="40dp"-->
<!--                android:layout_gravity="bottom|center_horizontal"-->
<!--                android:src="@drawable/circle_with_borders" />-->
<!--            <View-->
<!--                android:id="@+id/circle_five"-->
<!--                android:layout_width="20dp"-->
<!--                android:layout_height="20dp"-->
<!--                android:layout_gravity="center"-->
<!--                android:background="@drawable/ic_clock" />-->
<!--        </FrameLayout>-->

<!--        <TextView-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_gravity="center"-->
<!--            android:layout_marginTop="8dp"-->
<!--            android:textAppearance="@style/ExtraSmallBoldDarkText"-->
<!--            android:text="@string/completed"/>-->
<!--    </LinearLayout>-->

</LinearLayout>

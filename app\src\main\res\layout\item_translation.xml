<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="5dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/locale"
        style="@style/SmallBoldAppColorText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="@string/arabic"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:textLocale="ar" />
    <TextView
        android:id="@+id/name"
        style="@style/SmallMediumDarkText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="@string/product_name"
        android:layout_marginTop="4dp"
        app:layout_constraintStart_toStartOf="@id/locale"
        app:layout_constraintTop_toBottomOf="@id/locale"
        tools:textLocale="ar" />
    <TextView
        android:id="@+id/description"
        style="@style/SmallRegularDarkText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        tools:text="@string/product_details"
        app:layout_constraintStart_toStartOf="@id/locale"
        app:layout_constraintTop_toBottomOf="@id/name"
        tools:textLocale="ar" />
<View
    android:id="@+id/divider"
    android:layout_width="match_parent"
    android:layout_height="0.5dp"
    android:background="@color/color_gray_7"
    app:layout_constraintTop_toBottomOf="@id/description"
    android:layout_marginTop="5dp"
    android:elevation="2dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>
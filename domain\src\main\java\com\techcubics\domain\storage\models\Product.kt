package com.techcubics.domain.storage.models

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class Product (

    @SerializedName("id"                   ) var id                 : Int?      = null,
    @SerializedName("product_id"           ) var productId          : Int?      = null,
    @SerializedName("name"                 ) var name               : String?   = null,
    @SerializedName("slug"                 ) var slug               : String?   = null,
    @SerializedName("description"          ) var description        : String?   = null,
    @SerializedName("category"             ) var category           : Category? = null,
    @SerializedName("owner"                ) var owner              : Owner?    = null,
    @SerializedName("shop"                 ) var shop               : Shop?     = null,
    @SerializedName("price"                ) var price              : Float?    = null,
    @SerializedName("video"                ) var video              : String?   = null,
    @SerializedName("status"               ) var status             : Boolean?  = null,
    @SerializedName("wanted"               ) var wanted             : Boolean?  = null,
    @SerializedName("icon"                 ) var icon               : String?   = null,
    @SerializedName("minimum_order_number" ) var minimumOrderNumber : Int?      = null,
    @SerializedName("maximum_order_number" ) var maximumOrderNumber : Int?      = null,
    @SerializedName("translations"         ) var translations       : ArrayList<Translations>? = null,
    @SerializedName("sub_category"         ) var subCategory        : Category?                = null,
    @SerializedName("images"               ) var images             : ArrayList<Image>?        = null,
    @SerializedName("sizes"                ) var sizes              : ArrayList<String>?       = null,
    @SerializedName("colors"               ) var colors             : ArrayList<String>?       = null

):Parcelable
@Parcelize
data class Translations (

    @SerializedName("name"        ) var name        : String? = null,
    @SerializedName("slug"        ) var slug        : String? = null,
    @SerializedName("description" ) var description : String? = null,
    @SerializedName("locale"      ) var locale      : String? = null

):Parcelable
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="10dp"
    android:clickable="true"
    android:focusable="true"
    android:layout_marginHorizontal="8dp"
    android:layout_marginTop="8dp"
    android:layout_marginBottom="4dp"
    android:background="@drawable/ripple_bg_chat_room"
    xmlns:tools="http://schemas.android.com/tools">

    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/user_img"
        android:layout_width="50dp"
        android:layout_height="50dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@tools:sample/avatars" />
    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@id/user_img"
        app:layout_constraintEnd_toStartOf="@id/seen_msgs"
        app:layout_constraintTop_toTopOf="@id/user_img"
        tools:text="Huda Anwar"
        android:textAlignment="viewStart"
        android:layout_marginStart="8dp"
        style="@style/Medium1BoldDarkText"
        android:id="@+id/user_name"/>
    <TextView
        android:layout_width="25dp"
        android:layout_height="25dp"
        app:layout_constraintStart_toEndOf="@id/user_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/user_name"
        app:layout_constraintBottom_toBottomOf="@id/user_name"
        tools:text="5"
        android:background="@drawable/bg_unseen_msgs"
        android:textAlignment="center"
        android:gravity="center"
        style="@style/SmallRegularWhiteText"
        android:id="@+id/seen_msgs"/>
    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="@id/user_name"
        app:layout_constraintBottom_toBottomOf="@id/user_img"
        app:layout_constraintEnd_toStartOf="@id/dateTime"
        app:layout_constraintTop_toBottomOf="@id/user_name"
        android:textAlignment="viewStart"
        android:layout_marginTop="5dp"
        tools:text="Hi there"
        android:maxLines="1"
        android:ellipsize="end"
        style="@style/SmallRegularLightText"
        android:id="@+id/last_msg"/>
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@id/last_msg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/last_msg"
        app:layout_constraintTop_toTopOf="@id/last_msg"
        android:textAlignment="viewEnd"
        tools:text="01:22 PM"
        style="@style/SmallRegularLightText"
        android:id="@+id/dateTime"/>

</androidx.constraintlayout.widget.ConstraintLayout>
package com.techcubics.albarkahyperdashboard.features.storage.adapters.holders

import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyperdashboard.databinding.ItemTranslationBinding
import com.techcubics.domain.storage.models.Translations
import java.util.Locale

class TranslationViewHolder(private val binding: ItemTranslationBinding) :
    RecyclerView.ViewHolder(binding.root) {

    fun setData(translation: Translations) {
        with(binding) {
            locale.textLocale = Locale(translation.locale ?: "ar")
            locale.text = Locale(translation.locale ?: "ar").displayName
            name.text = translation.name
            description.text = translation.description
        }
    }

    fun hideDivider(isLastItem: Boolean) {
        if (isLastItem) {
            binding.divider.visibility = View.GONE
        } else {
            binding.divider.visibility = View.VISIBLE
        }
    }

}
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_fragment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_chats"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintBottom_toTopOf="@id/include_chat_send_box"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        tools:listitem="@layout/item_chat" />

    <include
        android:id="@+id/include_chat_send_box"
        layout="@layout/include_chat_send_box"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent" />

    <include
        android:id="@+id/placeholder"
        layout="@layout/include_placeholder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/include_chat_send_box"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <include
        android:id="@+id/loading"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:theme="@style/Theme.TextInputLayout"
    tools:context=".features.storage.fragments.tabs.product.AddNewProductFragment">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_fragment" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipToPadding="true"
        android:paddingBottom="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/cont"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/owner_cont"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="6dp"
                    android:layout_weight="1"
                    android:hint="@string/owner_name"
                    app:boxCornerRadiusBottomEnd="10dp"
                    app:boxCornerRadiusBottomStart="10dp"
                    app:boxCornerRadiusTopEnd="10dp"
                    app:boxCornerRadiusTopStart="10dp">

                    <AutoCompleteTextView
                        android:id="@+id/owner_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none"
                        android:textAppearance="@style/SmallRegularDarkText"
                        tools:ignore="LabelFor" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/shop_cont"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="6dp"
                    android:layout_weight="1"
                    android:hint="@string/shop_name"
                    app:boxCornerRadiusBottomEnd="10dp"
                    app:boxCornerRadiusBottomStart="10dp"
                    app:boxCornerRadiusTopEnd="10dp"
                    app:boxCornerRadiusTopStart="10dp">

                    <AutoCompleteTextView
                        android:id="@+id/shop_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none"
                        android:textAppearance="@style/SmallRegularDarkText"
                        tools:ignore="LabelFor" />

                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/category_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/category"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintEnd_toStartOf="@id/subcategory_cont"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cont">

                <AutoCompleteTextView
                    android:id="@+id/category_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/subcategory_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/subcategory"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintBottom_toBottomOf="@id/category_cont"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/category_cont"
                app:layout_constraintTop_toTopOf="@id/category_cont">

                <AutoCompleteTextView
                    android:id="@+id/subcategory_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/price_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/price"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintTop_toBottomOf="@id/subcategory_cont">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/price"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="numberDecimal"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/min_qty_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/min_num"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintEnd_toStartOf="@id/max_qty_cont"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/price_cont">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/min_qty"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/max_qty_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/max_num"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintBottom_toBottomOf="@id/min_qty_cont"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/min_qty_cont"
                app:layout_constraintTop_toTopOf="@id/min_qty_cont">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/max_qty"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/name_ar_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/name_ar"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintEnd_toStartOf="@id/name_en_cont"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/min_qty_cont">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/name_ar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/name_en_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/name_en"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintBottom_toBottomOf="@id/name_ar_cont"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/name_ar_cont"
                app:layout_constraintTop_toTopOf="@id/name_ar_cont">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/name_en"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>


            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/desc_ar_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/desc_ar"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintHeight_max="200dp"
                app:layout_constraintTop_toBottomOf="@id/name_ar_cont">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/desc_ar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="top|start"
                    android:inputType="textMultiLine"
                    android:lines="4"
                    android:overScrollMode="always"
                    android:scrollbarStyle="insideInset"
                    android:scrollbars="vertical"
                    android:textAlignment="viewStart"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/desc_en_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/desc_en"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintHeight_max="200dp"
                app:layout_constraintTop_toBottomOf="@id/desc_ar_cont">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/desc_en"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="top|start"
                    android:inputType="textMultiLine"
                    android:lines="4"
                    android:overScrollMode="always"
                    android:scrollbarStyle="insideInset"
                    android:scrollbars="vertical"
                    android:textAlignment="viewStart"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/vid_cont"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                android:hint="@string/video_url"
                app:boxCornerRadiusBottomEnd="10dp"
                app:boxCornerRadiusBottomStart="10dp"
                app:boxCornerRadiusTopEnd="10dp"
                app:boxCornerRadiusTopStart="10dp"
                app:layout_constraintTop_toBottomOf="@id/desc_en_cont">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/video_url"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:textAppearance="@style/SmallRegularDarkText"
                    tools:ignore="LabelFor" />

            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:id="@+id/uplaod_icon_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                android:background="?selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical|start"
                android:text="@string/icon_img"
                android:textAlignment="viewStart"
                app:layout_constraintStart_toStartOf="parent"
                android:textAppearance="@style/Medium2BoldDarkText"
                app:layout_constraintTop_toBottomOf="@id/vid_cont"/>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/icon_cont"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="6dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/uplaod_icon_title"
                app:strokeColor="@color/color_gray_19"
                app:strokeWidth="1dp">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:layout_gravity="center"
                    android:gravity="center">

                    <ImageView
                        android:id="@+id/icon"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:adjustViewBounds="true"
                        android:scaleType="centerCrop"
                        android:contentDescription="@string/img_cnt_desc"
                        android:src="@drawable/portrait_placeholder" />

                    <ImageView
                        android:id="@+id/uplaod_icon"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:layout_gravity="center"
                        android:background="@drawable/ripple_circle_white_transparent"
                        android:contentDescription="@string/img_cnt_desc"
                        android:src="@drawable/ic_camera" />
                </FrameLayout>
            </com.google.android.material.card.MaterialCardView>

            <TextView
                android:id="@+id/uplaod_images_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                android:background="?selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical|start"
                android:text="@string/product_images"
                android:textAlignment="viewStart"
                app:layout_constraintBottom_toBottomOf="@id/uplaod_images"
                app:layout_constraintStart_toStartOf="parent"
                android:textAppearance="@style/Medium2BoldDarkText"
                app:layout_constraintTop_toTopOf="@id/uplaod_images" />

            <ImageView
                android:id="@+id/uplaod_images"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                android:layout_marginEnd="10dp"
                android:background="@drawable/btn_progress_ripple"
                android:contentDescription="@string/img_cnt_desc"
                android:paddingHorizontal="15dp"
                android:paddingVertical="5dp"
                android:src="@drawable/ic_camera"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/icon_cont"
                app:tint="@color/white" />

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/imgs_cont"
                android:layout_width="match_parent"
                android:layout_height="190dp"
                android:layout_margin="6dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/uplaod_images"
                app:strokeColor="@color/color_gray_19"
                app:strokeWidth="1dp">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="190dp"
                    >

                    <ImageView
                        android:id="@+id/uplaod_images2"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/ic_camera"
                        android:clickable="true"
                        android:background="?selectableItemBackground"
                        android:contentDescription="@string/img_cnt_desc"/>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_imgs"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:itemCount="5"
                        tools:listitem="@layout/item_product_image"
                        tools:orientation="horizontal"
                        android:layout_gravity="center_vertical"/>

                </FrameLayout>

            </com.google.android.material.card.MaterialCardView>



            <LinearLayout
                android:id="@+id/features_btns"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:baselineAligned="false"
                android:orientation="horizontal"
                android:weightSum="2"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/imgs_cont">

                <include
                    android:id="@+id/add_sizes"
                    layout="@layout/btn_search_progress"
                    android:layout_width="0dp"
                    android:layout_height="45dp"
                    android:layout_margin="6dp"
                    android:layout_weight="1" />

                <include
                    android:id="@+id/add_colors"
                    layout="@layout/btn_search_progress"
                    android:layout_width="0dp"
                    android:layout_height="45dp"
                    android:layout_margin="6dp"
                    android:layout_weight="1" />
            </LinearLayout>

            <include
                android:id="@+id/add_product"
                layout="@layout/btn_search_progress"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:layout_margin="6dp"
                app:layout_constraintTop_toBottomOf="@id/imgs_cont" />


        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <include
        android:id="@+id/loading"
        layout="@layout/include_action_loading_animation"
        android:elevation="20dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/msg_layout"
        layout="@layout/include_placeholder"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:elevation="16dp">

        <include
            android:id="@+id/bottom_add_sizes"
            layout="@layout/include_add_sizes" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:elevation="16dp">

        <include
            android:id="@+id/bottom_add_colors"
            layout="@layout/include_add_colors" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</androidx.constraintlayout.widget.ConstraintLayout>